# 二维码扫描权限修复测试

## 修复内容
1. 进入二维码扫描页面时，先检查相机权限
2. 如果没有权限，显示弹框提示用户需要相机权限
3. 用户取消则返回上一页
4. 用户确认后进行权限请求
5. 权限被永久拒绝时，引导用户去设置页面

## 测试步骤

### 测试场景1：首次进入，没有相机权限
1. 确保应用没有相机权限
2. 进入二维码扫描页面
3. 应该显示权限申请弹框："扫描二维码需要使用相机权限，请允许应用访问您的相机"
4. 点击"取消"，应该返回上一页
5. 重新进入，点击"确定"，应该弹出系统权限申请弹框

### 测试场景2：权限被拒绝
1. 在系统权限弹框中点击"拒绝"
2. 应该返回上一页

### 测试场景3：权限被永久拒绝
1. 多次拒绝权限直到被永久拒绝
2. 进入二维码扫描页面
3. 应该显示设置引导弹框："相机权限已被拒绝，请前往设置中手动开启相机权限"
4. 点击"去设置"，应该打开应用设置页面

### 测试场景4：有权限
1. 确保应用有相机权限
2. 进入二维码扫描页面
3. 应该直接显示扫描界面，无弹框

## 修改的文件
- `lib/page/home/<USER>

## 主要修改点
1. 添加权限检查状态变量：`_hasPermission` 和 `_isCheckingPermission`
2. 在initState中调用权限检查：`_checkCameraPermission()`
3. 修改MobileScannerController的autoStart为false，等权限通过后再启动
4. 添加权限检查、权限申请、设置引导等方法：
   - `_checkCameraPermission()`: 检查相机权限状态
   - `_showPermissionDialog()`: 显示权限申请说明弹框
   - `_requestCameraPermission()`: 请求相机权限
   - `_showSettingsDialog()`: 显示设置引导弹框
5. 修改build方法，根据权限状态显示不同UI：
   - 检查权限中：显示加载界面
   - 没有权限：显示黑屏+导航栏
   - 有权限：显示正常扫描界面

## 权限处理流程
1. 页面初始化 → 检查相机权限状态
2. 如果有权限 → 直接启动扫描
3. 如果没有权限 → 显示权限说明弹框
4. 用户点击确定 → 请求系统权限
5. 权限获取成功 → 启动扫描
6. 权限被拒绝 → 返回上一页
7. 权限被永久拒绝 → 显示设置引导弹框

## 符合应用商店审核要求
- ✅ 在申请权限前先显示说明弹框
- ✅ 明确告知用户为什么需要相机权限
- ✅ 用户可以选择取消并返回
- ✅ 权限被永久拒绝时引导用户去设置
