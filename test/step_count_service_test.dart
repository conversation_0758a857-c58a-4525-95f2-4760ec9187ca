import 'package:flutter_test/flutter_test.dart';
import 'package:npemployee/service/step_count_service.dart';
import 'dart:convert';

void main() {
  group('StepCountService Tests', () {
    late StepCountService stepCountService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      stepCountService = StepCountService();
    });

    test('StepCountService singleton should return same instance', () {
      final instance1 = StepCountService();
      final instance2 = StepCountService();
      expect(instance1, equals(instance2));
    });

    test('checkStepPermissionApi should return valid JSON', () async {
      final result = await stepCountService.checkStepPermissionApi();
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded.containsKey('code'), isTrue);
      expect(decoded.containsKey('data'), isTrue);
      expect(decoded.containsKey('msg'), isTrue);
      expect(decoded['data'], isA<bool>());
    });

    test('requestStepPermissionApi should return valid JSON', () async {
      final result = await stepCountService.requestStepPermissionApi();
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded.containsKey('code'), isTrue);
      expect(decoded.containsKey('data'), isTrue);
      expect(decoded.containsKey('msg'), isTrue);
      expect(decoded['data'], isA<bool>());
    });

    test('getTodayStepCountApi should return valid JSON', () async {
      final result = await stepCountService.getTodayStepCountApi();
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded.containsKey('code'), isTrue);
      expect(decoded.containsKey('data'), isTrue);
      expect(decoded.containsKey('msg'), isTrue);
      expect(decoded['data'], isA<int>());

      // 步数应该是非负数
      expect(decoded['data'], greaterThanOrEqualTo(0));
    });

    test('getStepCountByDateApi should return valid JSON for today', () async {
      final today = DateTime.now();
      final result = await stepCountService.getStepCountByDateApi(today);
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded.containsKey('code'), isTrue);
      expect(decoded.containsKey('data'), isTrue);
      expect(decoded.containsKey('msg'), isTrue);
      expect(decoded['data'], isA<int>());

      // 步数应该是非负数
      expect(decoded['data'], greaterThanOrEqualTo(0));
    });

    test('getStepCountByDateApi should handle historical dates', () async {
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      final result = await stepCountService.getStepCountByDateApi(yesterday);
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded.containsKey('code'), isTrue);
      expect(decoded.containsKey('data'), isTrue);
      expect(decoded.containsKey('msg'), isTrue);
      expect(decoded['data'], isA<int>());
    });

    test('startStepCountStreamApi should return valid JSON', () async {
      final result = await stepCountService.startStepCountStreamApi();
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded.containsKey('code'), isTrue);
      expect(decoded.containsKey('data'), isTrue);
      expect(decoded.containsKey('msg'), isTrue);
      expect(decoded['data'], isA<bool>());
    });

    test(
        'stopStepCountStreamApi should return valid JSON when no stream is running',
        () async {
      final result = await stepCountService.stopStepCountStreamApi();
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded['code'], equals(0));
      expect(decoded['data'], equals(false));
      expect(decoded['msg'], equals('没有正在进行的步数监听'));
    });

    test('stopStepCountStreamApi should stop running stream', () async {
      // 先开始监听
      await stepCountService.startStepCountStreamApi();

      // 然后停止监听
      final result = await stepCountService.stopStepCountStreamApi();
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded['code'], equals(0));
      expect(decoded['data'], isA<bool>());
    });

    test('openAppSettingsApi should return valid JSON', () async {
      final result = await stepCountService.openAppSettingsApi();
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded.containsKey('code'), isTrue);
      expect(decoded.containsKey('data'), isTrue);
      expect(decoded.containsKey('msg'), isTrue);
      expect(decoded['data'], isA<bool>());
    });

    test('service should handle multiple calls gracefully', () async {
      // 连续调用多次getTodayStepCountApi
      final results = await Future.wait([
        stepCountService.getTodayStepCountApi(),
        stepCountService.getTodayStepCountApi(),
        stepCountService.getTodayStepCountApi(),
      ]);

      for (final result in results) {
        final decoded = jsonDecode(result);
        expect(decoded, isA<Map<String, dynamic>>());
        expect(decoded.containsKey('code'), isTrue);
        expect(decoded.containsKey('data'), isTrue);
        expect(decoded.containsKey('msg'), isTrue);
      }
    });

    test('service should handle date edge cases', () async {
      // 测试未来日期
      final futureDate = DateTime.now().add(const Duration(days: 1));
      final result = await stepCountService.getStepCountByDateApi(futureDate);
      final decoded = jsonDecode(result);

      expect(decoded, isA<Map<String, dynamic>>());
      expect(decoded.containsKey('code'), isTrue);
      expect(decoded.containsKey('data'), isTrue);
      expect(decoded.containsKey('msg'), isTrue);
      expect(decoded['data'], isA<int>());

      // 未来日期的步数应该是0
      expect(decoded['data'], equals(0));
    });

    tearDown(() {
      // 清理资源
      stepCountService.dispose();
    });
  });
}
