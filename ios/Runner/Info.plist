<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>File </key>
	<string></string>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<array>
		<string>zh-CN</string>
	</array>
	<key>CFBundleDisplayName</key>
	<string>新途径人</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh-CN</string>
	</array>
	<key>CFBundleName</key>
	<string>npemployee</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxcfff833da12005ba</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>xtjr</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>xtjr</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FDAllFilesDownloadedMessage</key>
	<string>文件已下载完毕</string>
	<key>FlutterDeepLinkingEnabled</key>
	<string>false</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>itms-beta</string>
		<string>itms</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>weixinURLParamsAPI</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>App需要访问蓝牙功能</string>
	<key>NSCameraUsageDescription</key>
	<string>App需要访问您的相机用来更换头像</string>
	<key>NSHealthShareUsageDescription</key>
	<string>我们将与 Apple Health 应用同步您的数据，以便为您提供更好的见解</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>我们将与 Apple Health 应用同步您的数据，以便为您提供更好的见解</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>App需要访问您的位置信息</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>App需要访问您的位置信息</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>App需要访问您的位置信息</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>App需要访问您的麦克风权限</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>App需要访问您的相册用来更换头像</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>audio</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
