#import <Flutter/Flutter.h>
#import "AliAuthHandler.h"
#import "MBProgressHUD.h"

// Toast Helper Macro
#define PNSToast(str, duration) ({ \
    UIViewController *topVC = [self getTopViewController]; \
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:topVC.view animated:YES]; \
    hud.mode = MBProgressHUDModeText; \
    hud.detailsLabel.font = [UIFont boldSystemFontOfSize:14.f]; \
    hud.detailsLabel.text = (str); \
    hud.detailsLabel.textColor = [UIColor whiteColor]; \
    hud.bezelView.color = [UIColor blackColor]; \
    hud.bezelView.style = MBProgressHUDBackgroundStyleSolidColor; \
    [hud hideAnimated:YES afterDelay:(duration)]; \
})


@implementation AliAuthHandler

+ (instancetype)sharedInstance {
    static AliAuthHandler *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (void)quitLoginPage {
    [[TXCommonHandler sharedInstance] cancelLoginVCAnimated:YES complete:^{
        NSLog(@"Authorization page closed, starting phone number change");
    }];
}

// Main AliAuth entry point
- (void)startAliAuthWithCompletion:(void(^)(NSString *token, NSError *error))completion {
    TXCustomModel *model = [self getCustomModel];

    [[TXCommonHandler sharedInstance] getLoginTokenWithTimeout:3.0
                                                    controller:[self getRootViewController]
                                                         model:model
                                                      complete:^(NSDictionary * _Nonnull resultDic) {
        [self handleAuthResult:resultDic completion:completion];
    }];
}

// Handle the result of authentication
- (void)handleAuthResult:(NSDictionary *)resultDic completion:(void(^)(NSString *token, NSError *error))completion {
    NSString *resultCode = resultDic[@"resultCode"];
    //不调用这个invokeMethod 不会执行
    [self handleAuthPageSuccess:completion];
    
    if ([PNSCodeLoginControllerPresentSuccess isEqualToString:resultCode]) {
        
    } else if ([PNSCodeSuccess isEqualToString:resultCode]) {
        [self handleLoginSuccess:resultDic completion:completion];
    } else {
        [self handleAuthFailure:resultDic];
    }
}

// Handle successful authentication page presentation
- (void)handleAuthPageSuccess:(void(^)(NSString *token, NSError *error))completion {
    NSLog(@"Authorization page presented successfully");
    UIViewController *rootVC = [self getRootViewController];
    [MBProgressHUD hideHUDForView:rootVC.view animated:YES];
    
    NSString *placeholderToken = @"pending";
    FlutterMethodChannel *authChannel = [self getAuthChannel];
    [authChannel invokeMethod:@"onAuthTokenReceived" arguments:placeholderToken];
    completion(placeholderToken, nil);  // Pass placeholder until real token is received
}

// Handle successful token retrieval
- (void)handleLoginSuccess:(NSDictionary *)resultDic completion:(void(^)(NSString *token, NSError *error))completion {
    NSLog(@"LoginToken successfully retrieved: %@", resultDic);
    NSString *token = resultDic[@"token"];
    if (token) {
        FlutterMethodChannel *authChannel = [self getAuthChannel];
        [authChannel invokeMethod:@"onAuthTokenReceived" arguments:token];
        completion(token, nil);  // Token passed to completion handler
    }
}

// Handle failure or error cases in the authentication process
- (void)handleAuthFailure:(NSDictionary *)resultDic {
    NSString *resultCode = resultDic[@"resultCode"];
    
    if ([PNSCodeLoginControllerClickLoginBtn isEqualToString:resultCode]) {
        NSNumber *isChecked = resultDic[@"isChecked"];
        if ([isChecked integerValue] == 0) {
            PNSToast(@"请先勾选同意后登录", 3.0);
        }
    } else if ([PNSCodeNoCellularNetwork isEqualToString:resultCode]) {
        PNSToast(@"蜂窝网络未开启或不稳定", 3.0);
    } else {
        [self handleAuthErrorWithCode:resultCode];
    }
}

// Method to handle different error codes and switch to other login methods
- (void)handleAuthErrorWithCode:(NSString *)resultCode {
    NSString *errorMessage = @"";
    BOOL shouldChangePhone = YES; // Flag to determine whether to call changePhone or not

    if ([PNSCodeGetOperatorInfoFailed isEqualToString:resultCode]) {
        errorMessage = @"获取运营商配置信息失败";
    } else if ([PNSCodeNoSIMCard isEqualToString:resultCode]) {
        errorMessage = @"未检测到sim卡";
    } else if ([PNSCodeUnknownOperator isEqualToString:resultCode]) {
        errorMessage = @"无法判定运营商";
    } else if ([PNSCodeUnknownError isEqualToString:resultCode]) {
        errorMessage = @"未知异常";
    } else if ([PNSCodeGetTokenFailed isEqualToString:resultCode]) {
        errorMessage = @"获取token失败";
    } else if ([PNSCodeGetMaskPhoneFailed isEqualToString:resultCode]) {
        errorMessage = @"预取号失败";
    } else if ([PNSCodeInterfaceDemoted isEqualToString:resultCode]) {
        errorMessage = @"运营商维护升级，该功能不可用";
    } else if ([PNSCodeInterfaceLimited isEqualToString:resultCode]) {
        errorMessage = @"运营商维护升级，该功能已达最大调用次数";
    } else if ([PNSCodeInterfaceTimeout isEqualToString:resultCode]) {
        errorMessage = @"接口超时";
    } else if ([PNSCodeDecodeAppInfoFailed isEqualToString:resultCode]) {
        errorMessage = @"AppID、Appkey解析失败";
    } else if ([PNSCodePhoneBlack isEqualToString:resultCode]) {
        errorMessage = @"该号码已被运营商管控";
    } else if ([PNSCodeCarrierChanged isEqualToString:resultCode]) {
        errorMessage = @"运营商已切换";
    } else if ([PNSCodeEnvCheckFail isEqualToString:resultCode]) {
        errorMessage = @"终端环境检测失败";
    } else {
//        errorMessage = @"发生未知错误，请稍后再试";
        shouldChangePhone = NO; // Do not call changePhone for the unknown error case
    }

    // 显示提示信息
    if(errorMessage.length > 0){
        // PNSToast(errorMessage, 3.0);
    }

    if (shouldChangePhone) {
        [self changePhone];
    }
}

// Define custom UI for the login page
- (TXCustomModel *)getCustomModel {
    TXCustomModel *model = [[TXCustomModel alloc] init];
    
    // Set up navigation bar
    [self configureNavigationBar:model];
    
    // Set up background
    [self configureBackground:model];
    
    // Set up phone number and login button
    [self configurePhoneNumberAndLoginButton:model];
    
    // Set up privacy agreements
    [self configurePrivacyAgreements:model];
    
    // Add custom views
     [self configureCustomViews:model];
    
    model.sloganIsHidden = YES;
    model.changeBtnIsHidden = YES;
    
    return model;
}

// Configure navigation bar appearance
- (void)configureNavigationBar:(TXCustomModel *)model {
    model.navIsHidden = NO;
    model.navBackImage = [UIImage imageNamed:@"icon_nav_back_light"];
    model.navColor = [UIColor clearColor];
    model.navTitle = [[NSAttributedString alloc] initWithString:@""];
    model.navBackButtonFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        CGFloat width = 0;
        CGFloat height = 0;
        return CGRectMake(frame.origin.x, frame.origin.y, width, height);
    };
}

// Configure background image
- (void)configureBackground:(TXCustomModel *)model {
    model.backgroundImage = [UIImage imageNamed:@"login_bg"];
    model.backgroundImageContentMode = UIViewContentModeScaleAspectFill;
}

// Configure phone number display and login button
- (void)configurePhoneNumberAndLoginButton:(TXCustomModel *)model {
    model.numberColor = [UIColor whiteColor];
    model.numberFont = [UIFont boldSystemFontOfSize:40];
    model.numberFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        return CGRectMake(33, 150, screenSize.width - 66, 40);
    };

    model.loginBtnText = [[NSAttributedString alloc] initWithString:@"一键登录"
                                                        attributes:@{
                                                            NSForegroundColorAttributeName: [UIColor blackColor],
                                                            NSFontAttributeName: [UIFont systemFontOfSize:16 weight:UIFontWeightBold]
                                                        }];
    model.loginBtnBgImgs = @[[UIImage imageNamed:@"oneclick_login_bg"], [UIImage imageNamed:@"oneclick_login_bg"], [UIImage imageNamed:@"oneclick_login_bg"]];
    model.loginBtnFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        return CGRectMake(33, 280, screenSize.width - 66, 60);
    };
}

// Configure privacy agreements and checkbox
- (void)configurePrivacyAgreements:(TXCustomModel *)model {
    model.privacyOne = @[@"《用户协议》", @"https://web.xtjstatic.cn/agreement/protocol.html"];
    model.privacyTwo = @[@"《隐私协议》", @"https://web.xtjstatic.cn/agreement/privacyAgreement.htm"];
    model.privacySufText = @"并同意协议内容";
    model.privacyOperatorPreText = @"《";
    model.privacyOperatorSufText = @"》";
    model.privacyFont = [UIFont systemFontOfSize:12];
    model.privacyOperatorFont = [UIFont boldSystemFontOfSize:12];
    model.privacyColors = @[[UIColor whiteColor], [UIColor whiteColor]];
    model.privacyAlignment = NSTextAlignmentLeft;
    model.privacyFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        return CGRectMake(60, 450, screenSize.width - 120, 60);
    };
    model.checkBoxImages = @[[UIImage imageNamed:@"check_unselected"], [UIImage imageNamed:@"check_selected"]];
}

// Configure custom views and layout
- (void)configureCustomViews:(TXCustomModel *)model {
    UILabel *sloganLabel = [self createSloganLabel];
    UIButton *changePhoneButton = [self createChangePhoneButton];
//    UILabel *otherLoginLabel = [self createOtherLoginLabel];
//    UIImageView *wechatIcon = [self createWechatIcon];
    UIView *blackTransparentBackground = [self createBlackTransparentBackground];

    model.customViewBlock = ^(UIView * _Nonnull superCustomView) {
        [superCustomView addSubview:sloganLabel];
        [superCustomView addSubview:changePhoneButton];
//        [superCustomView addSubview:otherLoginLabel];
//        [superCustomView addSubview:wechatIcon];
        [superCustomView addSubview:blackTransparentBackground];
    };

    model.customViewLayoutBlock = ^(CGSize screenSize, CGRect contentViewFrame, CGRect navFrame, CGRect titleBarFrame, CGRect logoFrame, CGRect sloganFrame, CGRect numberFrame, CGRect loginFrame, CGRect changeBtnFrame, CGRect privacyFrame) {
        sloganLabel.frame = CGRectMake(33, 25, screenSize.width - 66, 80);
        changePhoneButton.frame = CGRectMake(33, CGRectGetMaxY(loginFrame) + 15, screenSize.width - 66, 60);
//        otherLoginLabel.frame = CGRectMake((screenSize.width - 100) / 2, CGRectGetMaxY(privacyFrame) + 100, 100, 20);
//        wechatIcon.frame = CGRectMake((screenSize.width - 40) / 2, CGRectGetMaxY(otherLoginLabel.frame) + 10, 40, 40);
        blackTransparentBackground.frame = CGRectMake(33, 430, screenSize.width - 66, 70);
    };
}

// Helper methods to create UI elements
- (UILabel *)createSloganLabel {
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.text = @"成为\n受人尊重的教育品牌！";
    label.textColor = [UIColor whiteColor];
    label.numberOfLines = 2;
    label.font = [UIFont boldSystemFontOfSize:27];
    label.textAlignment = NSTextAlignmentLeft;
    return label;
}

- (UIButton *)createChangePhoneButton {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
    [button setTitle:@"更换手机号" forState:UIControlStateNormal];
    [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    button.backgroundColor = [[UIColor whiteColor] colorWithAlphaComponent:0.1];
    button.layer.cornerRadius = 15;
    [button addTarget:self action:@selector(changePhone) forControlEvents:UIControlEventTouchUpInside];
    return button;
}

- (UILabel *)createOtherLoginLabel {
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.text = @"其他登录方式";
    label.textColor = [UIColor whiteColor];
    label.font = [UIFont systemFontOfSize:13];
    label.textAlignment = NSTextAlignmentCenter;
    return label;
}

- (UIImageView *)createWechatIcon {
    UIImageView *imageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"wechat_login"]];
    imageView.userInteractionEnabled = YES;
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    [imageView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(wechatLogin)]];
    return imageView;
}

- (UIView *)createBlackTransparentBackground {
    UIView *view = [[UIView alloc] initWithFrame:CGRectZero];
    view.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.1];
    view.layer.cornerRadius = 15;
    return view;
}

// Change phone event
- (void)changePhone {
    FlutterMethodChannel *authChannel = [self getAuthChannel];
    [authChannel invokeMethod:@"changePhoneEvent" arguments:@{@"eventCode": @1001, @"description": @"Change Phone Event"}];
    [self quitLoginPage];
}

// Handle WeChat login event
- (void)wechatLogin {
    FlutterMethodChannel *authChannel = [self getAuthChannel];
    [authChannel invokeMethod:@"wechatLoginEvent" arguments:@{@"eventCode": @1002, @"description": @"WeChat Login Event"}];
    [self quitLoginPage];
}

- (UIViewController *)getTopViewController {
    UIViewController *rootViewController = [self getRootViewController];
    UIViewController *topController = rootViewController;
    
    while (topController.presentedViewController) {
        topController = topController.presentedViewController;
    }
    
    return topController;
}


// Get the root view controller
- (UIViewController *)getRootViewController {
    UIWindow *keyWindow;
    if (@available(iOS 13.0, *)) {
        for (UIWindowScene *windowScene in [UIApplication sharedApplication].connectedScenes) {
            if (windowScene.activationState == UISceneActivationStateForegroundActive) {
                keyWindow = windowScene.windows.firstObject;
                break;
            }
        }
    } else {
        keyWindow = [UIApplication sharedApplication].keyWindow;
    }
    return keyWindow.rootViewController;
}

// Get the Flutter method channel
- (FlutterMethodChannel *)getAuthChannel {
    FlutterViewController *controller = (FlutterViewController *)[self getRootViewController];
    FlutterMethodChannel *authChannel = [FlutterMethodChannel methodChannelWithName:@"com.npemployee/ali_auth"
                                                                    binaryMessenger:controller.binaryMessenger];
    return authChannel;
}

@end
