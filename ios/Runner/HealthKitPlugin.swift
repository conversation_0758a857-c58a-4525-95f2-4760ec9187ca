//
//  HealthKitPlugin.swift
//  Runner
//
//  Created by yu z on 2024/12/1.
//

import Foundation
import Flutter
import HealthKit

class HealthKitPlugin: NSObject, FlutterPlugin {
    static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "health_kit_plugin", binaryMessenger: registrar.messenger())
        let instance = HealthKitPlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "health_kit_authorized":
            
            print("原生原生 health_kit_authorized")
            result(isHealthKitAuthorized())
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func isHealthKitAuthorized() -> Bool {
        if HKHealthStore.isHealthDataAvailable() {
            let healthStore = HKHealthStore()
            let stepCountType = HKObjectType.quantityType(forIdentifier: .stepCount)!
            // 检查权限状态并打印
            let status = healthStore.authorizationStatus(for: stepCountType)
            print("HealthKit授权状态: \(status.rawValue)")
            
            // 检查具体状态
            switch status {
            case .sharingAuthorized:
                print("已授权")
                return true
            case .sharingDenied:
                print("已拒绝")
                return false
            case .notDetermined:
                print("未确定")
                return false
            default:
                print("其他状态")
                return false
            }
        } else {
            print("HealthKit 不可用")
            return false
        }
    }
}
