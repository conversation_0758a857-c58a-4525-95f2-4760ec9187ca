import UIKit
import Flutter
import flutter_downloader
import HealthKit
import tencent_cloud_chat_push
import TIMPush
import app_links

private func registerPlugins(registry: FlutterPluginRegistry) {
    if (!registry.hasPlugin("FlutterDownloaderPlugin")) {
       FlutterDownloaderPlugin.register(with: registry.registrar(forPlugin: "FlutterDownloaderPlugin")!)
    }
    
}

@main
@objc class AppDelegate: FlutterAppDelegate, TIMPushDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        
        //友盟初始化
        UMCommonSwift.setLogEnabled(bFlag: true)
        UMCommonSwift.initWithAppkey(appKey: "678da8079a16fe6dcd324de0", channel: "App Store");
        
        // Retrieve the link from parameters
        if let url = AppLinks.shared.getLink(launchOptions: launchOptions) {
          // We have a link, propagate it to your Flutter app or not
          AppLinks.shared.handleLink(url: url)
          return true // Returning true will stop the propagation to other packages
        }
        
        HealthKitPlugin.register(with: self.registrar(forPlugin: "HealthKitPlugin")!)
        FlutterDownloaderPlugin.setPluginRegistrantCallback(registerPlugins)
        

        let authSDKInfo = "g4R0oqln2F+p5UsBhxpLKCAzNmDpsplolrcyj76rotws297/kRsKQA/aJc6xKYEulqA6alWpOdwBx4c2GHuzI0I9ozXM2RM/IuyDM8dO/kRl+L/Z9WsXFNrccfXZr9blLMjCdMX8sZ4C+SM0mf5/OZUMF/ijpbZr/1q8Sku9E+1HCqM9FAvavj7TKn/Fc+uxx8JKzTeAjsWduTlOWhmNzS/oRP5kYv4L74fkItAriVyOjWIcj5fID3i/uxVjdfGdcHha0AOAagY="
           
           // Setting the AliAuth SDK key
           TXCommonHandler.sharedInstance().setAuthSDKInfo(authSDKInfo) { resultDic in
               print("SDK Info Setup Result: \(resultDic)")
           }
        
        let controller = window?.rootViewController as! FlutterViewController
        let authChannel = FlutterMethodChannel(name: "com.npemployee/ali_auth", binaryMessenger: controller.binaryMessenger)

        authChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "startAliAuth":
                self.startAliAuth(result: result)
            case "quitAliAuth":
                AliAuthHandler.sharedInstance().quitLoginPage()
            default:
                result(FlutterMethodNotImplemented)
            }
        }
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    private func startAliAuth(result: @escaping FlutterResult) {
        AliAuthHandler.sharedInstance().startAliAuth { (token, error) in
            if let token = token {
                result(token)  // 返回token给Flutter端
            } else if let error = error {
                result(FlutterError(code: "AUTH_ERROR", message: error.localizedDescription, details: nil))
            }
        }
    }


    private func changePhone(result: FlutterResult) {
//        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
//           let window = windowScene.windows.first,
//           let presentedVC = window.rootViewController?.presentedViewController {
//            presentedVC.dismiss(animated: true) {
//                result("Phone change handled")
//            }
//        } else {
//            result(FlutterError(code: "CHANGE_PHONE_ERROR", message: "No auth page to dismiss for phone change", details: nil))
//        }
    }
    
    @objc func businessID() -> Int32 {
        return TencentCloudChatPushFlutterModal.shared.businessID();
    }

    @objc func applicationGroupID() -> String {
        return TencentCloudChatPushFlutterModal.shared.applicationGroupID()
    }
    
    @objc func onRemoteNotificationReceived(_ notice: String?) -> Bool {
        TencentCloudChatPushPlugin.shared.tryNotifyDartOnNotificationClickEvent(notice)
        return true
    }
}
