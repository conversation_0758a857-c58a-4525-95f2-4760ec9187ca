# StepCountService 使用说明

## 概述

StepCountService 是一个封装了步数获取功能的单例服务类，支持Android和iOS平台的步数获取。该服务参考了HealthRankPage中的步数获取逻辑，并提供了统一的API接口。

## 主要功能

- ✅ 检查步数权限
- ✅ 请求步数权限  
- ✅ 获取今日步数
- ✅ 获取历史步数（iOS支持，Android仅支持今日）
- ✅ 监听步数变化（Android支持）
- ✅ 打开应用设置页面

## 平台支持

### Android平台
- 使用 `daily_pedometer` 插件
- 需要 `活动识别权限` (Activity Recognition Permission)
- 支持实时步数监听
- 仅支持获取今日步数

### iOS平台  
- 使用 `health` 插件和 `HealthKitChannel`
- 需要 `HealthKit权限`
- 支持获取历史步数
- 暂不支持实时步数监听

## API接口

### 1. 权限管理

```dart
// 检查步数权限
String result = await StepCountService().checkStepPermissionApi();

// 请求步数权限
String result = await StepCountService().requestStepPermissionApi();

// 打开应用设置
String result = await StepCountService().openAppSettingsApi();
```

### 2. 步数获取

```dart
// 获取今日步数
String result = await StepCountService().getTodayStepCountApi();

// 获取指定日期步数
DateTime date = DateTime(2024, 1, 1);
String result = await StepCountService().getStepCountByDateApi(date);
```

### 3. 步数监听

```dart
// 开始监听步数变化（仅Android）
String result = await StepCountService().startStepCountStreamApi();

// 停止监听步数变化
String result = await StepCountService().stopStepCountStreamApi();
```

## 返回格式

所有API方法都返回JSON格式的字符串：

```json
{
  "code": 0,           // 0表示成功，-1表示失败
  "data": 12345,       // 具体数据，类型根据接口而定
  "msg": "操作成功"     // 操作结果描述
}
```

### 示例返回值

```json
// 获取今日步数成功
{
  "code": 0,
  "data": 8520,
  "msg": "获取今日步数成功"
}

// 权限检查结果
{
  "code": 0,
  "data": true,
  "msg": "已授权步数权限"
}

// 操作失败
{
  "code": -1,
  "data": false,
  "msg": "未授权活动识别权限"
}
```

## 使用示例

### 基本使用

```dart
import 'package:npemployee/service/step_count_service.dart';
import 'dart:convert';

class StepCountExample {
  final StepCountService _stepCountService = StepCountService();
  
  Future<void> getStepCount() async {
    // 1. 检查权限
    String permissionResult = await _stepCountService.checkStepPermissionApi();
    Map permissionData = jsonDecode(permissionResult);
    
    if (permissionData['code'] != 0 || !permissionData['data']) {
      // 2. 请求权限
      String requestResult = await _stepCountService.requestStepPermissionApi();
      Map requestData = jsonDecode(requestResult);
      
      if (requestData['code'] != 0 || !requestData['data']) {
        print('权限获取失败');
        return;
      }
    }
    
    // 3. 获取今日步数
    String stepResult = await _stepCountService.getTodayStepCountApi();
    Map stepData = jsonDecode(stepResult);
    
    if (stepData['code'] == 0) {
      int stepCount = stepData['data'];
      print('今日步数: $stepCount');
    } else {
      print('获取步数失败: ${stepData['msg']}');
    }
  }
}
```

### 在WebView中使用

StepCountService已集成到WebviewScreenPage中，可以通过JavaScript调用：

```javascript
// 检查权限
sendToNative('checkStepPermission');

// 获取今日步数
sendToNative('getTodayStepCount');

// 获取指定日期步数
sendToNative('getStepCountByDate', {
  date: '2024-01-01'
});

// 处理回调
window.stepCountCallback = function(action, result) {
  const data = JSON.parse(result);
  console.log(`${action} 返回:`, data);
};
```

## 测试

项目包含完整的单元测试，位于 `test/step_count_service_test.dart`：

```bash
# 运行步数服务测试
flutter test test/step_count_service_test.dart
```

## 示例页面

项目提供了完整的示例页面：

- **Dart页面**: `lib/pages/step_count_example_page.dart`
- **HTML页面**: `assets/html/step_count_example.html`

### 访问示例页面

在首页双击二维码扫描按钮即可进入步数功能示例页面。

## 注意事项

1. **权限要求**：
   - Android需要活动识别权限
   - iOS需要HealthKit权限

2. **平台差异**：
   - Android仅支持今日步数和实时监听
   - iOS支持历史步数但不支持实时监听

3. **错误处理**：
   - 所有方法都有完善的错误处理
   - 返回统一的JSON格式便于解析

4. **资源管理**：
   - 使用完毕后调用 `dispose()` 方法释放资源
   - 单例模式确保资源的合理使用

## 依赖项

- `daily_pedometer`: Android步数获取
- `health`: iOS健康数据获取  
- `permission_handler`: 权限管理
- `npemployee/methodchannel/health_kit_channel.dart`: iOS HealthKit桥接

## 更新日志

- **v1.0.0**: 初始版本，支持基本的步数获取功能
- 参考HealthRankPage的实现逻辑
- 提供完整的测试用例和示例页面
