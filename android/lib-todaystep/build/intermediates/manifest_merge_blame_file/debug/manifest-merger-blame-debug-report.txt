1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.today.step" >
5
6    <uses-sdk
7        android:minSdkVersion="23"
7-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml
8        android:targetSdkVersion="30" />
8-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml
9
10    <!-- Android 12 要使用0微秒的采样率，需要声明正常权限HIGH_SAMPLING_RATE_SENSORS -->
11    <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS" />
11-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:7:5-85
11-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:7:22-82
12    <!-- Android 11 存储权限 -->
13    <uses-permission
13-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:9:5-10:40
14        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
14-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:9:22-79
15        tools:ignore="ScopedStorage" />
15-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:10:9-37
16    <!-- Android 10 运动权限 -->
17    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
17-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:12:5-79
17-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:12:22-76
18    <!-- Android适配Android 9.0 前台服务 -->
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:14:5-77
19-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:14:22-74
20    <!-- 存储 -->
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:16:5-80
21-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:16:22-77
22    <uses-permission
22-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:17:5-18:40
23        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
23-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:17:22-78
24        tools:ignore="ScopedStorage" />
24-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:18:9-37
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:19:5-68
25-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:19:22-65
26    <!-- 开机广播 -->
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:21:5-81
27-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:21:22-78
28    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
28-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:22:5-85
28-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:22:22-82
29
30    <!-- 协处理器计步权限 例如，健身应用可以报告用户需要走多少步才能达到每天的计步目标。 -->
31    <uses-feature
31-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:27:5-29:35
32        android:name="android.hardware.sensor.stepcounter"
32-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:28:9-59
33        android:required="true" />
33-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:29:9-32
34    <!-- 应用使用设备的步测器 例如，健身应用可以利用每步的间隔时间来推测用户正在进行的锻炼类型。 -->
35    <uses-feature
35-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:31:5-33:35
36        android:name="android.hardware.sensor.stepdetector"
36-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:32:9-60
37        android:required="true" />
37-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:33:9-32
38
39    <application android:requestLegacyExternalStorage="true" >
39-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:36:5-79:19
39-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:37:9-52
40
41        <!-- 计步Service -->
42        <service
42-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:40:9-58:19
43            android:name="com.today.step.service.TodayStepService"
43-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:41:13-53
44            android:enabled="true"
44-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:42:13-35
45            android:exported="true"
45-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:43:13-36
46            android:launchMode="singleInstance"
46-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:45:13-48
47            android:priority="1000"
47-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:46:13-36
48            android:process=":todaystep" >
48-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:44:13-41
49            <intent-filter>
49-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:47:13-57:29
50
51                <!-- 系统启动完成后会调用 -->
52                <action android:name="android.intent.action.BOOT_COMPLETED" />
52-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:50:17-79
52-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:50:25-76
53                <action android:name="android.intent.action.DATE_CHANGED" />
53-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:51:17-77
53-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:51:25-74
54                <action android:name="android.intent.action.MEDIA_MOUNTED" />
54-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:52:17-78
54-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:52:25-75
55                <action android:name="android.intent.action.USER_PRESENT" />
55-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:53:17-77
55-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:53:25-74
56                <action android:name="android.intent.action.ACTION_TIME_TICK" />
56-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:54:17-81
56-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:54:25-78
57                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
57-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:55:17-87
57-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:55:25-84
58                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
58-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:56:17-90
58-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:56:25-87
59            </intent-filter>
60        </service>
61
62        <!-- 开机自启动 -->
63        <receiver
63-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:61:9-68:20
64            android:name="com.today.step.receiver.TodayStepBootCompleteReceiver"
64-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:62:13-67
65            android:enabled="true"
65-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:63:13-35
66            android:exported="true" >
66-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:64:13-36
67            <intent-filter>
67-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:65:13-67:29
68                <action android:name="android.intent.action.BOOT_COMPLETED" />
68-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:50:17-79
68-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:50:25-76
69            </intent-filter>
70        </receiver>
71
72        <!-- 关机广播 -->
73        <receiver
73-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:71:9-77:20
74            android:name="com.today.step.receiver.TodayStepShutdownReceiver"
74-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:71:19-69
75            android:exported="true" >
75-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:72:13-36
76            <intent-filter>
76-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:73:13-76:29
77
78                <!-- 关机广播 -->
79                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
79-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:75:17-80
79-->/Users/<USER>/Downloads/TodayStepCount-main/lib-todaystep/src/main/AndroidManifest.xml:75:25-77
80            </intent-filter>
81        </receiver>
82    </application>
83
84</manifest>
