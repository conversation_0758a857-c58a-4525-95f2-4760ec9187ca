<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.today.step" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="30" />

    <!-- Android 12 要使用0微秒的采样率，需要声明正常权限HIGH_SAMPLING_RATE_SENSORS -->
    <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS" />
    <!-- Android 11 存储权限 -->
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <!-- Android 10 运动权限 -->
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
    <!-- Android适配Android 9.0 前台服务 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- 存储 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 开机广播 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />

    <!-- 协处理器计步权限 例如，健身应用可以报告用户需要走多少步才能达到每天的计步目标。 -->
    <uses-feature
        android:name="android.hardware.sensor.stepcounter"
        android:required="true" />
    <!-- 应用使用设备的步测器 例如，健身应用可以利用每步的间隔时间来推测用户正在进行的锻炼类型。 -->
    <uses-feature
        android:name="android.hardware.sensor.stepdetector"
        android:required="true" />

    <application android:requestLegacyExternalStorage="true" >

        <!-- 计步Service -->
        <service
            android:name="com.today.step.service.TodayStepService"
            android:enabled="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:priority="1000"
            android:process=":todaystep" >
            <intent-filter>

                <!-- 系统启动完成后会调用 -->
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.DATE_CHANGED" />
                <action android:name="android.intent.action.MEDIA_MOUNTED" />
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.intent.action.ACTION_TIME_TICK" />
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </service>

        <!-- 开机自启动 -->
        <receiver
            android:name="com.today.step.receiver.TodayStepBootCompleteReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!-- 关机广播 -->
        <receiver
            android:name="com.today.step.receiver.TodayStepShutdownReceiver"
            android:exported="true" >
            <intent-filter>

                <!-- 关机广播 -->
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
            </intent-filter>
        </receiver>
    </application>

</manifest>