plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.huawei.agconnect"
    id "com.hihonor.mcs.asplugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.xtj.person"
    compileSdkVersion 34 // 更新 compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

     signingConfigs {
         release {
             storeFile file('xtjr.keystore') // 确保文件路径正确
             storePassword 'XTJSK2024'
             keyAlias 'xtjrAlias'
             keyPassword 'XTJSK2024'
         }
         debug {
             storeFile file('xtjr.keystore') // 确保文件路径正确
             storePassword 'XTJSK2024'
             keyAlias 'xtjrAlias'
             keyPassword 'XTJSK2024'
         }
     }


    defaultConfig {
        applicationId "com.xtj.person"
        minSdkVersion 26
        targetSdkVersion 34 // 更新 targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        manifestPlaceholders = [
                "VIVO_APPKEY" : "43f58fd05a91b186c51fc5529ae1e4c1",
                "VIVO_APPID" : "105708680",
                "HONOR_APPID" : "104425729"
        ]

        ndk {
            abiFilters 'armeabi', 'arm64-v8a','x86'
        }

        applicationVariants.all { variant ->
            variant.outputs.all {
                outputFileName = "com.xtj.person_v${flutterVersionName}.apk"
            }

        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    packagingOptions {
        dex {
            useLegacyPackaging true
        }
        jniLibs {
            useLegacyPackaging true
        }
    }

}


flutter {
    source '../..'
}

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'

    // Correct the versions to match the actual files in your libs folder
//    implementation(name: "auth_number_product-2.13.14-release", ext:'aar')  // Update to match your aar file
//    implementation(name: "logger-2.2.2-release", ext:'aar')
//    implementation(name: "main-2.2.3-release", ext:'aar')
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation 'androidx.appcompat:appcompat:1.2.0' //1.3.1



    // Huawei
    implementation 'com.tencent.timpush:huawei:8.3.6498'
    // XiaoMi
    implementation 'com.tencent.timpush:xiaomi:8.3.6498'
    // OPPO
    implementation 'com.tencent.timpush:oppo:8.3.6498'
    // vivo
    implementation 'com.tencent.timpush:vivo:8.3.6498'
    // Honor
    implementation 'com.tencent.timpush:honor:8.3.6498'
    // Meizu
//    implementation 'com.tencent.timpush:meizu:VERSION'
    // Google Firebase Cloud Messaging (Google FCM)
//    implementation 'com.tencent.timpush:fcm:VERSION'

}


