<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parent_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_bg"
    android:padding="16dp">

    <!-- <PERSON><PERSON><PERSON> 文本 -->
    <TextView
        android:id="@+id/slogan_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="成为\n受人尊重的教育品牌！     "
        android:textColor="@android:color/white"
        android:textSize="27sp"
        android:textStyle="bold"
        android:layout_marginTop="50dp"
        android:layout_marginStart="13dp"
        android:layout_marginEnd="13dp" />

    <!-- 更换手机号按钮 -->
    <Button
        android:id="@+id/change_phone_button"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:text="更换手机号"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:layout_below="@id/slogan_text"
        android:layout_marginTop="210dp"
        android:background="@drawable/rounded_bg" 
        android:layout_marginStart="13dp"
        android:layout_marginEnd="13dp"
        android:gravity="center" />

    <!-- 黑色透明背景视图 -->
    <View
        android:id="@+id/black_transparent_background"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_below="@id/change_phone_button"
        android:layout_marginTop="20dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_centerHorizontal="true"
        android:background="@drawable/black_transparent_bg" />

    <!-- 其他登录方式文本 -->
    <TextView
        android:id="@+id/other_login_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="其他登录方式"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:layout_below="@id/black_transparent_background"
        android:layout_marginTop="100dp"
        android:layout_centerHorizontal="true" />

    <!-- 微信登录图标 -->
    <ImageView
        android:id="@+id/wechat_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/wechat_login"
        android:layout_below="@id/other_login_text"
        android:layout_marginTop="10dp"
        android:layout_centerHorizontal="true"
        android:contentDescription="WeChat Login" />

</RelativeLayout>
