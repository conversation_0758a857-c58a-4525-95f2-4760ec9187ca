package com.xtj.person;

import android.os.Bundle;

import com.tencent.chat.flutter.push.tencent_cloud_chat_push.application.TencentCloudChatPushApplication;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.umcrash.UMCrash;

public class MyApplication extends TencentCloudChatPushApplication {
    @Override
    public void onCreate() {
        super.onCreate();

       Bundle bundle = new Bundle();

       bundle.putBoolean(UMCrash.KEY_ENABLE_CRASH_JAVA, true);
       bundle.putBoolean(UMCrash.KEY_ENABLE_CRASH_NATIVE, true);
       bundle.putBoolean(UMCrash.KEY_ENABLE_FLUTTER, true);
       bundle.putBoolean(UMCrash.KEY_ENABLE_ANR, false);
       bundle.putBoolean(UMCrash.KEY_ENABLE_PA, false);
       bundle.putBoolean(UMCrash.KEY_ENABLE_LAUNCH, false);
       bundle.putBoolean(UMCrash.KEY_ENABLE_MEM, false);
       bundle.putBoolean(UMCrash.KEY_ENABLE_H5PAGE, false);
       bundle.putBoolean(UMCrash.KEY_ENABLE_POWER, false);
       UMCrash.initConfig(bundle);

       UMConfigure.preInit(getApplicationContext(), "678da7049a16fe6dcd324c8a", "Android");

    }
}
