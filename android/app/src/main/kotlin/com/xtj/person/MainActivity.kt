package com.xtj.person

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.Toast
import com.mobile.auth.gatewayauth.AuthRegisterXmlConfig
import com.mobile.auth.gatewayauth.AuthUIConfig
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper
import com.mobile.auth.gatewayauth.PreLoginResultListener
import com.mobile.auth.gatewayauth.ResultCode
import com.mobile.auth.gatewayauth.TokenResultListener
import com.mobile.auth.gatewayauth.model.TokenRet
import com.mobile.auth.gatewayauth.ui.AbstractPnsViewDelegate
import com.umeng.umeng_apm_sdk.UmengApmSdkPlugin
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.plugins.util.GeneratedPluginRegister
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import org.json.JSONException
import org.json.JSONObject

class MainActivity : FlutterActivity() {
    private val TAG: String = "GeneratedPluginRegistrant"
    private val CHANNEL = "com.npemployee/ali_auth"
    private val FLUTTER_CALL_REGISTER_PLUGIN = "FLUTTER_CALL_REGISTER_PLUGIN"
    private lateinit var phoneNumberAuthHelper: PhoneNumberAuthHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set up communication with Flutter
        setupFlutterMethodChannel()

        // Initialize AliAuth SDK
        setupAliAuth()
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
//        super.configureFlutterEngine(flutterEngine)


        val channel = flutterEngine.dartExecutor.binaryMessenger.let {
            MethodChannel(
                it,
                FLUTTER_CALL_REGISTER_PLUGIN
            )
        }

        channel.setMethodCallHandler { call, result ->
            // 处理来自Flutter的方法调用
            when (call.method) {
                "registerProxyPlugin" -> {
                    // 实现你的方法逻辑
//                    GeneratedPluginRegister.registerGeneratedPlugins(flutterEngine)

                    // 获取 SharedPreferences 实例（要与读取时使用相同的名称）
                    val preferences = getSharedPreferences("xtjr_sp", MODE_PRIVATE)

// 写入值
                    preferences.edit()
                        .putBoolean("KEY_PRIVACY", true)
                        .apply() // 或者用 commit()


                    try {
                        flutterEngine.getPlugins().add(io.bitbunny.daily_pedometer.daily_pedometer.DailyPedometerPlugin())
                    } catch (e: Exception) {
                        Log.e(
                            TAG,
                            "Error registering plugin daily_pedometer, io.bitbunny.daily_pedometer.daily_pedometer.DailyPedometerPlugin",
                            e
                        )
                    }


                    result.success(true)
                }

                else -> {
                    result.notImplemented()
                }
            }
        }

        try {
            flutterEngine.getPlugins().add(com.llfbandit.app_links.AppLinksPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin app_links, com.llfbandit.app_links.AppLinksPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.ryanheise.audio_session.AudioSessionPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin audio_session, com.ryanheise.audio_session.AudioSessionPlugin", e);
        }
//        try {
//            flutterEngine.getPlugins().add(com.jhomlala.better_player.BetterPlayerPlugin());
//        } catch (e: Exception) {
//            Log.e(TAG, "Error registering plugin better_player, com.jhomlala.better_player.BetterPlayerPlugin", e);
//        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.camera.CameraPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin camera_android, io.flutter.plugins.camera.CameraPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(dev.fluttercommunity.plus.connectivity.ConnectivityPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin connectivity_plus, dev.fluttercommunity.plus.connectivity.ConnectivityPlugin", e);
        }

        try {
            flutterEngine.getPlugins().add(one.mixin.desktop.drop.DesktopDropPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin desktop_drop, one.mixin.desktop.drop.DesktopDropPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin device_info_plus, dev.fluttercommunity.plus.device_info.DeviceInfoPlusPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.piccmaq.disk_space_plus.DiskSpacePlusPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin disk_space_plus, com.piccmaq.disk_space_plus.DiskSpacePlusPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.fluttercavalry.fc_native_video_thumbnail.FcNativeVideoThumbnailPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin fc_native_video_thumbnail, com.fluttercavalry.fc_native_video_thumbnail.FcNativeVideoThumbnailPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.mr.flutter.plugin.filepicker.FilePickerPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin file_picker, com.mr.flutter.plugin.filepicker.FilePickerPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(id.flutter.flutter_background_service.FlutterBackgroundServicePlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_background_service_android, id.flutter.flutter_background_service.FlutterBackgroundServicePlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(vn.hunghd.flutterdownloader.FlutterDownloaderPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_downloader, vn.hunghd.flutterdownloader.FlutterDownloaderPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.fluttercandies.flutter_image_compress.ImageCompressPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_image_compress_common, com.fluttercandies.flutter_image_compress.ImageCompressPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.pichillilorenzo.flutter_inappwebview_android.InAppWebViewFlutterPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_inappwebview_android, com.pichillilorenzo.flutter_inappwebview_android.InAppWebViewFlutterPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.jrai.flutter_keyboard_visibility.FlutterKeyboardVisibilityPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_keyboard_visibility, com.jrai.flutter_keyboard_visibility.FlutterKeyboardVisibilityPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_local_notifications, com.dexterous.flutterlocalnotifications.FlutterLocalNotificationsPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_plugin_android_lifecycle, io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(record.wilson.flutter.com.flutter_plugin_record.FlutterPluginRecordPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_plugin_record_plus, record.wilson.flutter.com.flutter_plugin_record.FlutterPluginRecordPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_timezone, net.wolverinebeach.flutter_timezone.FlutterTimezonePlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.tundralabs.fluttertts.FlutterTtsPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin flutter_tts, com.tundralabs.fluttertts.FlutterTtsPlugin", e);
        }

        try {
            flutterEngine.getPlugins().add(com.jarvan.fluwx.FluwxPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin fluwx, com.jarvan.fluwx.FluwxPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.baseflow.geolocator.GeolocatorPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin geolocator_android, com.baseflow.geolocator.GeolocatorPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(cachet.plugins.health.HealthPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin health, cachet.plugins.health.HealthPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(vn.hunghd.flutter.plugins.imagecropper.ImageCropperPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin image_cropper, vn.hunghd.flutter.plugins.imagecropper.ImageCropperPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.example.imagegallerysaver.ImageGallerySaverPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin image_gallery_saver, com.example.imagegallerysaver.ImageGallerySaverPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.example.image_gallery_saver_plus.ImageGallerySaverPlusPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin image_gallery_saver_plus, com.example.image_gallery_saver_plus.ImageGallerySaverPlusPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.imagepicker.ImagePickerPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin image_picker_android, io.flutter.plugins.imagepicker.ImagePickerPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.ryanheise.just_audio.JustAudioPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin just_audio, com.ryanheise.just_audio.JustAudioPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.tencent.mmkv.MMKVPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin mmkv_android, com.tencent.mmkv.MMKVPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(dev.steenbakker.mobile_scanner.MobileScannerPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin mobile_scanner, dev.steenbakker.mobile_scanner.MobileScannerPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.crazecoder.openfile.OpenFilePlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin open_file_android, com.crazecoder.openfile.OpenFilePlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.packageinfo.PackageInfoPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin package_info, io.flutter.plugins.packageinfo.PackageInfoPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin package_info_plus, dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.pathprovider.PathProviderPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin path_provider_android, io.flutter.plugins.pathprovider.PathProviderPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.baseflow.permissionhandler.PermissionHandlerPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin permission_handler_android, com.baseflow.permissionhandler.PermissionHandlerPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.fluttercandies.photo_manager.PhotoManagerPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin photo_manager, com.fluttercandies.photo_manager.PhotoManagerPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.quickactions.QuickActionsPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin quick_actions_android, io.flutter.plugins.quickactions.QuickActionsPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(dev.fluttercommunity.plus.sensors.SensorsPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin sensors_plus, dev.fluttercommunity.plus.sensors.SensorsPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(dev.fluttercommunity.plus.share.SharePlusPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin share_plus, dev.fluttercommunity.plus.share.SharePlusPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin shared_preferences_android, io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.tekartik.sqflite.SqflitePlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin sqflite_android, com.tekartik.sqflite.SqflitePlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.syncfusion.flutter.pdfviewer.SyncfusionFlutterPdfViewerPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin syncfusion_flutter_pdfviewer, com.syncfusion.flutter.pdfviewer.SyncfusionFlutterPdfViewerPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.tencent.cloud.tuikit.tuicall_engine.TUICallEnginePlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin tencent_calls_engine, com.tencent.cloud.tuikit.tuicall_engine.TUICallEnginePlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.tencent.cloud.tuikit.flutter.tuicallkit.TUICallKitPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin tencent_calls_uikit, com.tencent.cloud.tuikit.flutter.tuicallkit.TUICallKitPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.tencent.chat.flutter.push.tencent_cloud_chat_push.TencentCloudChatPushPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin tencent_cloud_chat_push, com.tencent.chat.flutter.push.tencent_cloud_chat_push.TencentCloudChatPushPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.tencent.chat.tencent_cloud_chat_sdk.TencentCloudChatSdkPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin tencent_cloud_chat_sdk, com.tencent.chat.tencent_cloud_chat_sdk.TencentCloudChatSdkPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(com.tencent.cloud.uikit.core.TUICorePlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin tencent_cloud_uikit_core, com.tencent.cloud.uikit.core.TUICorePlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.urllauncher.UrlLauncherPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin url_launcher_android, io.flutter.plugins.urllauncher.UrlLauncherPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.videoplayer.VideoPlayerPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin video_player_android, io.flutter.plugins.videoplayer.VideoPlayerPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin wakelock_plus, dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin", e);
        }
        try {
            flutterEngine.getPlugins().add(io.flutter.plugins.webviewflutter.WebViewFlutterPlugin());
        } catch (e: Exception) {
            Log.e(TAG, "Error registering plugin webview_flutter_android, io.flutter.plugins.webviewflutter.WebViewFlutterPlugin", e);
        }

        val preferences = getSharedPreferences(
            "xtjr_sp",
            MODE_PRIVATE
        )
        if (preferences.getBoolean("KEY_PRIVACY", false)) {
            super.configureFlutterEngine(flutterEngine)
        }




    }

    private fun quitLoginPage() {
        phoneNumberAuthHelper.quitLoginPage()
    }

    // Modify the setupFlutterMethodChannel method to handle the new method call
    private fun setupFlutterMethodChannel() {
        flutterEngine?.let { engine ->
            MethodChannel(engine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
                when (call.method) {
                    "startAliAuth" -> {
                        result.success("pending")
                        startAliAuth()  // Start AliAuth when called from Flutter
                    }
                    "quitAliAuth" -> {
                        quitLoginPage()  // Close AliAuth when called from Flutter
                        result.success("AliAuth page closed")
                    }
                    else -> result.notImplemented()
                }
            }
        }
    }

    // Initialize AliAuth SDK
    private fun setupAliAuth() {
        phoneNumberAuthHelper = PhoneNumberAuthHelper.getInstance(applicationContext, object : TokenResultListener {
            override fun onTokenSuccess(token: String?) {
                val tokenRet = TokenRet.fromJson(token)
                if (tokenRet != null && tokenRet.code == ResultCode.CODE_SUCCESS) {
                    // 成功获取token，发送给Flutter
                    handleLoginSuccess(tokenRet.token)
                } else if (tokenRet != null && tokenRet.code == ResultCode.CODE_START_AUTHPAGE_SUCCESS) {
                    Toast.makeText(applicationContext, "唤起授权页成功", Toast.LENGTH_LONG).show()
                } else {
                    // 处理获取token失败的情况
                    handleAuthFailure(tokenRet?.code, tokenRet?.msg)
                }
            }

            override fun onTokenFailed(error: String?) {
                Log.e("AliAuth", "Token failed with error: $error")

                // 解析 error 并提取 message 显示给用户
                try {
                    val errorJson = JSONObject(error)
                    val errorMessage = errorJson.optString("msg", "发生未知错误")

                    // 显示 Toast 提示
                    Toast.makeText(applicationContext, "$errorMessage", Toast.LENGTH_LONG).show()
                } catch (e: JSONException) {
                    Toast.makeText(applicationContext, "登录失败，发生未知错误", Toast.LENGTH_LONG).show()
                }

                // 通知Flutter切换到其他登录方式，并退出登录页面
                sendEventToFlutter("changePhoneEvent", mapOf("eventCode" to 1001, "description" to "Change Phone Event"))
                phoneNumberAuthHelper.quitLoginPage()
            }

        })

        // Set the SDK key (AliAuth secret)
        phoneNumberAuthHelper.setAuthSDKInfo("DY/gOANrpfmNNXQQy5dwgMyXDw1TuV4QShMLrgPMzGqPC8wYFoF89IEV0HgPkKbnRk9zslVGyyXajcJEwn3pTz2k7geItWeC1C8b9e64Gn6jtN/shbuUpwKmL00kTCzzmfD6gq3hFKQAHgyCKs1KQVk/fYl9NfimXsZd05SFv9Iq6cNDJ5H0Lv0FrR9KNZkVZ1tdNI3QFWRX+WlnQFWgjxBEtqvjgejSh7fZLcb1g2HKbkLj15iS8nGsHMNNpcR8M+16V7CIZeLzJ40OiPUFfgZp6UYicGupBWDul3/gUxA=")  // Replace with your actual secret
    }

    private fun handleAuthFailure(code: String?, message: String?, extra: Map<String, Any>? = null) {
        when (code) {
            // 检查用户是否勾选同意协议
            ResultCode.CODE_ERROR_USER_LOGIN_BTN -> {
                val isChecked = extra?.get("isChecked") as? Boolean
                if (isChecked == false) {
                    // 提示用户需要先勾选协议
                    Toast.makeText(this, "请先勾选同意后登录", Toast.LENGTH_LONG).show()
                }
            }

            // 蜂窝网络未开启
            ResultCode.CODE_ERROR_NO_MOBILE_NETWORK_FAIL -> {
                Toast.makeText(this, "蜂窝网络未开启或不稳定", Toast.LENGTH_LONG).show()
            }

            // 其他错误情况，指定的错误码处理后切换登录方式
            else -> {
                // 根据错误码显示适当的提示信息
                val errorMessage = when (code) {
                    ResultCode.CODE_ERROR_START_AUTHPAGE_FAIL -> "唤起授权页失败"
                    ResultCode.CODE_ERROR_GET_CONFIG_FAIL -> "获取运营商配置信息失败"
                    ResultCode.CODE_ERROR_NO_SIM_FAIL -> "SIM卡无法检测"
                    ResultCode.CODE_ERROR_OPERATOR_UNKNOWN_FAIL -> "无法判定运营商"
                    ResultCode.CODE_ERROR_UNKNOWN_FAIL -> "未知异常"
                    ResultCode.CODE_GET_TOKEN_FAIL -> "获取token失败"
                    ResultCode.CODE_GET_MASK_FAIL -> "预取号失败"
                    ResultCode.CODE_ERROR_FUNCTION_DEMOTE -> "系统维护，功能不可用"
                    ResultCode.CODE_ERROR_FUNCTION_LIMIT -> "该功能已达最大调用次数"
                    ResultCode.CODE_ERROR_FUNCTION_TIME_OUT -> "请求超时"
                    ResultCode.CODE_ERROR_ANALYZE_SDK_INFO -> "AppID Secret解析失败"
                    ResultCode.CODE_ERROR_ANALYZE_SDK_BLACKLIST -> "当前号码已被运营商管控，暂时无法使用该功能，请尝试其他方式"
                    ResultCode.CODE_ERROR_NET_SIM_CHANGE -> "用户已切换上网卡"
                    ResultCode.CODE_ERROR_USER_CANCEL -> "用户取消操作"
                    ResultCode.CODE_ERROR_USER_CONTROL_CANCEL_BYBTN -> "用户点击取消"
                    else -> message ?: "发生未知错误，请稍后再试"
                }

                // 显示错误提示
                Toast.makeText(this, errorMessage, Toast.LENGTH_LONG).show()

                // 仅对指定的错误码切换到其他登录方式
                if (code in listOf(
                        ResultCode.CODE_ERROR_START_AUTHPAGE_FAIL,
                        ResultCode.CODE_ERROR_GET_CONFIG_FAIL,
                        ResultCode.CODE_ERROR_NO_SIM_FAIL,
                        ResultCode.CODE_ERROR_OPERATOR_UNKNOWN_FAIL,
                        ResultCode.CODE_ERROR_UNKNOWN_FAIL,
                        ResultCode.CODE_GET_TOKEN_FAIL,
                        ResultCode.CODE_GET_MASK_FAIL,
                        ResultCode.CODE_ERROR_FUNCTION_DEMOTE,
                        ResultCode.CODE_ERROR_FUNCTION_LIMIT,
                        ResultCode.CODE_ERROR_FUNCTION_TIME_OUT,
                        ResultCode.CODE_ERROR_ANALYZE_SDK_INFO,
                        ResultCode.CODE_ERROR_ANALYZE_SDK_BLACKLIST,
                        ResultCode.CODE_ERROR_NET_SIM_CHANGE
                    )) {
                    // 切换到其他登录方式（例如，调用切换手机号）
                    sendEventToFlutter("changePhoneEvent", mapOf("eventCode" to 1001, "description" to "Change Phone Event"))
                    phoneNumberAuthHelper.quitLoginPage()
                }
            }
        }
    }


    private fun accelerateLoginPage(timeout: Int) {
        phoneNumberAuthHelper.accelerateLoginPage(timeout, object : PreLoginResultListener {
            override fun onTokenSuccess(s: String) {

            }
            override fun onTokenFailed(s: String, s1: String) {

            }
        })
    }

    // Start the AliAuth login page with custom UI
    private fun startAliAuth() {
        setupCustomUILayout()  // Set up the custom UI layout
        configureAuthUI()  // Configure the UI settings

        // Finally, trigger the AliAuth login page
        phoneNumberAuthHelper.getLoginToken(this, 15000)  // 5 seconds timeout
    }

    // Setup custom UI layout for the authorization page
    private fun setupCustomUILayout() {
        val customLayout = AuthRegisterXmlConfig.Builder()
            .setLayout(R.layout.custom_auth_layout, object : AbstractPnsViewDelegate() {
                override fun onViewCreated(view: View) {
                    setupCustomUIListeners(view)  // Handle button click listeners
                }
            })
            .build()

        phoneNumberAuthHelper.addAuthRegisterXmlConfig(customLayout)
    }

    // Set up custom UI listeners
    private fun setupCustomUIListeners(view: View) {
        val changePhoneButton = view.findViewById<Button>(R.id.change_phone_button)
        val wechatIcon = view.findViewById<ImageView>(R.id.wechat_icon)

        changePhoneButton.setOnClickListener {
            sendEventToFlutter("changePhoneEvent", mapOf("eventCode" to 1001, "description" to "Change Phone Event"))
            phoneNumberAuthHelper.quitLoginPage()
        }

        wechatIcon.setOnClickListener {
            sendEventToFlutter("wechatLoginEvent", mapOf("eventCode" to 1002, "description" to "WeChat Login Event"))
            phoneNumberAuthHelper.quitLoginPage()
        }
    }

    // Configure the authorization page UI
    private fun configureAuthUI() {
        val config = AuthUIConfig.Builder()
            .setStatusBarColor(0x00254864)
            .setNavColor(0x00254864)  // Transparent navigation bar
            .setNavHidden(true)
            .setNavText("")
            .setNavReturnImgPath("icon_nav_back_light")  // Return icon
            .setLogoHidden(true)
            .setSloganHidden(true)
            .setLogBtnBackgroundPath("oneclick_login_bg")  // Background image for login button
            .setLogBtnText("一键登录")
            .setLogBtnOffsetY(230)
            .setLogBtnTextColor(0xFF000000.toInt())  // Black text
            .setNumberColor(0xFFFFFFFF.toInt())  // White phone number color
            .setNumberSizeDp(40)
            .setNumberFieldOffsetX(-33)
            .setNumFieldOffsetY(130)
            .setSwitchAccHidden(true)
            .setVendorPrivacyPrefix("《")
            .setVendorPrivacySuffix("》")
            .setPrivacyOperatorColor(Color.WHITE)
            .setAppPrivacyOne("《用户协议》", "https://web.xtjstatic.cn/agreement/protocol.html")
            .setAppPrivacyTwo("《隐私协议》", "https://web.xtjstatic.cn/agreement/privacyAgreement.htm")
            .setPrivacyTextSize(12)
            .setPrivacyState(false)
            .setProtocolGravity(Gravity.LEFT)
            .setProtocolTypeface(Typeface.MONOSPACE)
            .setAppPrivacyColor(0xFFFFFFFF.toInt(),0xFFFFFFFF.toInt())
            .setPrivacyEnd("并同意协议内容")
            .setUncheckedImgPath("check_unselected")
            .setCheckedImgPath("check_selected")
            .setPrivacyOffsetY(380)
            .create()

        phoneNumberAuthHelper.setAuthUIConfig(config)
    }

    // Send event to Flutter
    private fun sendEventToFlutter(eventName: String, eventData: Map<String, Any>) {
        flutterEngine?.let { engine ->
            MethodChannel(engine.dartExecutor.binaryMessenger, CHANNEL).invokeMethod(eventName, eventData)
        } ?: run {
            Log.e("AliAuth", "Flutter Engine is null, cannot send event to Flutter.")
        }
    }

    // Handle login success by sending the token to Flutter
    private fun handleLoginSuccess(token: String?) {
        flutterEngine?.let { engine ->
            MethodChannel(engine.dartExecutor.binaryMessenger, CHANNEL).invokeMethod("onAuthTokenReceived", token)
        }
//        phoneNumberAuthHelper.quitLoginPage()  // 成功后退出登录页面
    }

}
