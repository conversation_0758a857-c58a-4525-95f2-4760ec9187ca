{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9887b5f0262c2be975797e30cda8dbcdaa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Toast/Toast-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Toast/Toast-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Toast/Toast.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Toast", "PRODUCT_NAME": "Toast", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988ef0e9e70bd9aabf876b92fd3ebf42e8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b35927c2d9d6a83a889c4d38d6524852", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Toast/Toast-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Toast/Toast-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Toast/Toast.modulemap", "PRODUCT_MODULE_NAME": "Toast", "PRODUCT_NAME": "Toast", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9823dc923553f7d69bf6c68d2ff214c06b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b35927c2d9d6a83a889c4d38d6524852", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/Toast/Toast-prefix.pch", "INFOPLIST_FILE": "Target Support Files/Toast/Toast-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Toast/Toast.modulemap", "PRODUCT_MODULE_NAME": "Toast", "PRODUCT_NAME": "Toast", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cfa8bd5e7352f91ece66debc44c5cff9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803bcac28a9926ae902cc91bf21528082", "guid": "bfdfe7dc352907fc980b868725387e985901c71ea1f88fee300637641baf98d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885265b113cb056ff606752160d6e4667", "guid": "bfdfe7dc352907fc980b868725387e98aac67407fbb07ae17703c1ec36fb1aa8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ea27750256a3570860191a7e81d81d5", "guid": "bfdfe7dc352907fc980b868725387e98163fe322e65cae288d755e4942fe6134", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989322c98c9f7e0ca25c36ac771f617453", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986c955110821ee41993e02cb0e11d8da7", "guid": "bfdfe7dc352907fc980b868725387e9850b2c9b65d6863dac013576c1f0942b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a962a5926f627a5180103d05ed1373", "guid": "bfdfe7dc352907fc980b868725387e9862e8ade6707814d83e6b46b6ebdafafe"}], "guid": "bfdfe7dc352907fc980b868725387e9874db3126009b3ecf4e3e33fe9f2b26e5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98896fff2b7ecaa033f50516bdf93909a9", "guid": "bfdfe7dc352907fc980b868725387e983129d085f462decb490b630f02a96372"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824eaa57731d5ec6300b9ca2f2867fb73", "guid": "bfdfe7dc352907fc980b868725387e98efa52127360f6569ff8a2b500fc071a1"}], "guid": "bfdfe7dc352907fc980b868725387e98db3267d2b8982339ba112053d15ef7e3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983ac0de92575b11a80523cdd86e7afaa9", "targetReference": "bfdfe7dc352907fc980b868725387e98306ea98533a6c78c50f5382ab243d3bc"}], "guid": "bfdfe7dc352907fc980b868725387e981724b0162fd230e03af6f5a50c30d7b7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98306ea98533a6c78c50f5382ab243d3bc", "name": "Toast-Toast"}], "guid": "bfdfe7dc352907fc980b868725387e98355cff7d758029119bc2218cc297bc97", "name": "Toast", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b70c5ac4727fec6cd373da2227901396", "name": "Toast.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}