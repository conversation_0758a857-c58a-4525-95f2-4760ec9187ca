{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9820bae627aaaf54505a097bbe8aeaf580", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/stable/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/stable/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810bdb3018dba7b3fc87b085e5c7a18ec", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd08f2fa382b7956a09c05359190a9c8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/stable/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/stable/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808213667fea3cf0e28f6feb0339126c5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dd08f2fa382b7956a09c05359190a9c8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/fvm/versions/stable/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/fvm/versions/stable/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892b119b956a581fc1e399b4ce8515719", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984cd9a06dec20b949bb60aafd5ea9aed2", "guid": "bfdfe7dc352907fc980b868725387e98a89744c0f50ce31106d7b9ea9c55c780", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac7a07174c04216e3d9ca3ac9f83e1c", "guid": "bfdfe7dc352907fc980b868725387e9848de2d6d0a5f6df191198844ce5b20cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e380243a644464d6581374b22a3025b2", "guid": "bfdfe7dc352907fc980b868725387e98e29e34bd8c699e053593bc15e4a90aef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b21205e228d875566dee68af0b7324a5", "guid": "bfdfe7dc352907fc980b868725387e983d4ab07e6627297236b6a611b83d4b6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e90d93c3ef0725330c0de307321bc4a9", "guid": "bfdfe7dc352907fc980b868725387e98c6ceb7e149c0a2c017e61b12b30da835", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b4dfc60a82e3032a1a67379835ffa2c", "guid": "bfdfe7dc352907fc980b868725387e983c579d1bfa5598fc58450d3e3adcfb54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983575704d42bb1c0b49a01297c18952f6", "guid": "bfdfe7dc352907fc980b868725387e9885bab72ae69eff05d4d3202965e003ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fb1f7c9f711d1f9e2b1603e7669019a", "guid": "bfdfe7dc352907fc980b868725387e98be4124849b0e7a0f9f9a6befa09b4a7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c0b36fba1ca536d7abbb1b7a15362f7", "guid": "bfdfe7dc352907fc980b868725387e98d6f25a63756f8906bd2bede9a7bd7f4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a67512915e227715ddeb71d2940e05d", "guid": "bfdfe7dc352907fc980b868725387e9876ed2bfff3bbf2e20a4cd2b4f1ec07c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98347b0e5bb4bc56598d198607adfd8524", "guid": "bfdfe7dc352907fc980b868725387e98d11eb859a3c700ee9aaebd20fd7766d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819928f101ab5b50382e269e10935971c", "guid": "bfdfe7dc352907fc980b868725387e98c10c6228e85800524d5eab9c06b8614a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ec84d6ebee9cfead52e25ae5d01918c", "guid": "bfdfe7dc352907fc980b868725387e981c30fa11052baa0af67168fd425b2221", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d76c076b4bf194f8f1f6494cf851a234", "guid": "bfdfe7dc352907fc980b868725387e98da320bebba8f574da88459b1dfe128a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838b4e8c7029263d78dcff611bea0ce2d", "guid": "bfdfe7dc352907fc980b868725387e98f9ef1c3febd6872711d4a27124f06c4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c543ce71607d073d22874f05565ae444", "guid": "bfdfe7dc352907fc980b868725387e98311b03da32b5082e7cce1293ad3cd865", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d1c4820bf4794f1d6749a82175f58dd", "guid": "bfdfe7dc352907fc980b868725387e98916fa9f3a4d651d8a0dde1caf2339f20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2a2da388753f7f96cdce46dfdc7f838", "guid": "bfdfe7dc352907fc980b868725387e986d99309bbdcfa8568be9f354bdbe09cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98942375b92f49f3eb7e3193f3df59ae3e", "guid": "bfdfe7dc352907fc980b868725387e98526291a1b810edf4007f41d354242a1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4a25df7a258cc03358f5125557ee9e5", "guid": "bfdfe7dc352907fc980b868725387e98a673331c361b66c39077952174b6797f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d2b50fe36ca9c832789bd93bb9df3eb", "guid": "bfdfe7dc352907fc980b868725387e98d5736315516da41ebf74c5c0c3d19e32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816c7cd661a4c408e2b9b04f1e57cbd94", "guid": "bfdfe7dc352907fc980b868725387e98f28083f4c0a3c9f46f288b5bccff1e1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849e51d82a18548ec1db43f84deb6630b", "guid": "bfdfe7dc352907fc980b868725387e98a54a375979130197bb1d5a75799737da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b56b18c089e13b7974674e1c1355611f", "guid": "bfdfe7dc352907fc980b868725387e98b4ba4acde9aa66553da201aa39bed0ea", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b809dbfa4c6712e1c19e51590af7fa04", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98069caa2ee6d9011d1954a2b9e6542ef6", "guid": "bfdfe7dc352907fc980b868725387e9808a6bbdd18de21a7196328c38988bdc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981583a99ab84144eb075c18d219de9e52", "guid": "bfdfe7dc352907fc980b868725387e98b9f779c912febdce7e5821874b8fa38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a9637c4a90f969664ba5876a917ba4f", "guid": "bfdfe7dc352907fc980b868725387e98a1ad170c0def52d237e5dabef0d6ee3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1f4f0fa4db7df7940c796462ea8943", "guid": "bfdfe7dc352907fc980b868725387e9821d0e1a0e89e66e2c66e313b883b64c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8c8feeec78f3f96ab24c05bef564ca", "guid": "bfdfe7dc352907fc980b868725387e983471081aeeca7d275652d405b445128b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989288f58af85db740d8b2fac28cbe01e4", "guid": "bfdfe7dc352907fc980b868725387e98d7fc5f086a8d4f64ee3583eb543cfc51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c419813a33cb82d6e09e792f28859213", "guid": "bfdfe7dc352907fc980b868725387e9837829608c0a22e0266eb6caf605502e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae71d0479245bd80dd44edf73598a179", "guid": "bfdfe7dc352907fc980b868725387e983816f8ea6f1ce6b757042fa639080e3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f22d26c1685fcb5065ac558c4f941c6", "guid": "bfdfe7dc352907fc980b868725387e9807f48dcd788ef41a4c0b84c9c79d7e74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980969ae4cdab16d56f4138ba8d2100598", "guid": "bfdfe7dc352907fc980b868725387e984bf4f37c9b30962cbbc39827fc3e7863"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980565bfc4eb1dea2ecd355fe79d13b584", "guid": "bfdfe7dc352907fc980b868725387e98dcb7815b3832c9b0d46ee423d01dbc6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e15439ff7e67141b21db174959b56316", "guid": "bfdfe7dc352907fc980b868725387e987edcecdfb3c5e3b21e9ee4a12d448adf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1b8ebee52521ea9262642eee230c592", "guid": "bfdfe7dc352907fc980b868725387e98823564bc61589ea71cd340c4da7bb1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694556a30ecc801979cc757d5c31c154", "guid": "bfdfe7dc352907fc980b868725387e987356a6ab20a7c1f2f5f3d775ff6509fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0415cafa788f885b010f1c65b8b4fab", "guid": "bfdfe7dc352907fc980b868725387e98f0324d284499444dcfe1df256157b882"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f398d576f7eac7cb396dcacd010573ac", "guid": "bfdfe7dc352907fc980b868725387e9836fb8c4be3b7cfef4ed7aafbe94cb989"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c420b0ad0ef64265a164bf7636bf5767", "guid": "bfdfe7dc352907fc980b868725387e9854965b51282374990756ff01621a3774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809282d6e4d0a180651c43c06a98505ea", "guid": "bfdfe7dc352907fc980b868725387e98990ea28c1981764cf8edaec4e99b2b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c460438ff47501cc902018d59fb09e8", "guid": "bfdfe7dc352907fc980b868725387e98ebd42e09e4dfe4beec6b5d480ae6325b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d824d776e56a9ca508d3645d154d29c", "guid": "bfdfe7dc352907fc980b868725387e98d46730faac40ede3809cc421626491e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834a8829752fe17fd03dcb357db80bc17", "guid": "bfdfe7dc352907fc980b868725387e981b9ac73debff5be3445533206b8c293e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aed02b0cda691b1d84735f14de623afa", "guid": "bfdfe7dc352907fc980b868725387e9832504a5dc877d6457a2d98696044133c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fd1f40e003d03cdbd7745071e0923d9", "guid": "bfdfe7dc352907fc980b868725387e989980e3343a39ecfb8cc0522bda25f465"}], "guid": "bfdfe7dc352907fc980b868725387e98295a6a912ea2f996619108451429afc3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98896fff2b7ecaa033f50516bdf93909a9", "guid": "bfdfe7dc352907fc980b868725387e98d69d3f754ff2570528c4de0715d27ea9"}], "guid": "bfdfe7dc352907fc980b868725387e986ff48b1d20b26e526e96f1291209d3a7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ca04b2f98dda4aa3ea7d695df2288e55", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98b319dc9b9bc94b538d198b41cff1b6d9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}