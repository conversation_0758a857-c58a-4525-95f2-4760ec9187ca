import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_watch_history_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/audio_bloc/audio_event.dart';
import 'package:npemployee/provider/user_service_provider.dart';

class VideoChapterMenu2 extends StatefulWidget {
  final List<ChapterModel> datas;
  final LessonModel? selected; //当前播放的小节
  final int courseId;
  final List<LessonModel> lessons;
  final Function(LessonModel) onChange;
  const VideoChapterMenu2(
      {super.key,
      required this.datas,
      this.selected,
      required this.courseId,
      required this.lessons,
      required this.onChange});

  @override
  State<VideoChapterMenu2> createState() => _VideoChapterMenu2State();
}

class _VideoChapterMenu2State extends State<VideoChapterMenu2> {
  List<ChapterModel> chapterDatas = [];
  final Map<int, GlobalKey> nodeKeys = {};
  List<CourseWatchHistoryModel> courseWatchHistorys = []; //历史学习列表
  LessonModel? selectedLesson;

  void _scrollToSelectedLesson() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (var chapter in chapterDatas) {
        if (chapter.isExpanded) {
          for (var lesson in chapter.lesson) {
            if (lesson.id == (selectedLesson?.id ?? -1)) {
              final key = nodeKeys[lesson.id];
              if (key != null && key.currentContext != null) {
                Scrollable.ensureVisible(key.currentContext!,
                    duration: const Duration(milliseconds: 300),
                    alignment: 0.5,
                    curve: Curves.easeInOut);
              }
              break;
            }
          }
        }
      }
    });
  }

  void updateSelectedLesson(LessonModel? lesson) {
    setState(() {
      selectedLesson = lesson;
      for (var chapter in chapterDatas) {
        chapter.isExpanded =
            chapter.lesson.any((lesson) => lesson.id == selectedLesson?.id);
      }
      _scrollToSelectedLesson();
    });
  }

  void _getCourseWatchHistory() {
    UserServiceProvider().getCourseWatchList(
      course_id: widget.courseId,
      cacheCallBack: (value) {
        // _formatCourseWatchHistoryData(value, false);
      },
      successCallBack: (value) {
        _formatCourseWatchHistoryData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  _formatCourseWatchHistoryData(ResultData? value, bool isCache) {
    courseWatchHistorys.clear();
    for (var e in value?.data ?? []) {
      courseWatchHistorys.add(CourseWatchHistoryModel.formJson(e));
    }
    if (courseWatchHistorys.isNotEmpty) {}
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    chapterDatas = widget.datas;
    selectedLesson = widget.selected;

    _getCourseWatchHistory();

    if (selectedLesson != null) {
      for (var chapter in chapterDatas) {
        chapter.isExpanded =
            chapter.lesson.any((lesson) => lesson.id == selectedLesson?.id);
      }
    }

    if (selectedLesson != null) {
      _scrollToSelectedLesson();
    }
  }

  @override
  void didUpdateWidget(covariant VideoChapterMenu2 oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selected != oldWidget.selected) {
      updateSelectedLesson(widget.selected);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                '课程列表',
                style:
                    TextStyle(color: const Color(0xFF333333), fontSize: 15.sp)
                        .pfMedium,
              ),
              Text(
                ' (共${chapterDatas.length}章${widget.lessons.length}节)',
                style:
                    TextStyle(color: const Color(0xFF999999), fontSize: 11.sp)
                        .pfRegular,
              )
            ],
          ),
          SizedBox(height: 13.5.h),
          Expanded(
              child: SingleChildScrollView(
            child: Column(
              children: List.generate(chapterDatas.length, (index) {
                ChapterModel chapter = chapterDatas[index];
                return _buildChapterItem(chapter);
              }),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildChapterItem(ChapterModel chapter) {
    bool isLastStudyChapter = courseWatchHistorys.isNotEmpty &&
        chapter.lesson
            .any((lesson) => lesson.id == courseWatchHistorys.first.lesson_id);

    return Container(
      margin: EdgeInsets.only(bottom: 15.h),
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 8.h),
      decoration: BoxDecoration(
          color: const Color(0xFFF7F8FD),
          borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              setState(() {
                chapter.isExpanded = !chapter.isExpanded;
              });
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Row(
                children: [
                  Image.asset(
                      chapter.isExpanded
                          ? 'assets/png/xiaoxin/menu_close.png'
                          : 'assets/png/xiaoxin/menu_open.png',
                      width: 15,
                      height: 15),
                  SizedBox(width: 6.5.w),
                  Expanded(
                    child: RichText(
                        text: TextSpan(
                      text: '${chapter.title}  ',
                      style: TextStyle(
                              color: const Color(0xFF333333), fontSize: 15.sp)
                          .pfMedium,
                      children: [
                        if (isLastStudyChapter)
                          WidgetSpan(
                              child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 3.w, vertical: 2.h),
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6.r),
                                      border: Border.all(
                                          color: const Color(0xFF0054FF))),
                                  child: Text('上次学习',
                                      style: TextStyle(
                                              color: const Color(0xFF0054FF),
                                              fontSize: 11.sp)
                                          .pfRegular))),
                      ],
                    )),
                    /* Text(
                      chapter.title,
                      style: TextStyle(
                              color: const Color(0xFF333333), fontSize: 15.sp)
                          .pfMedium,
                    ), */
                  ),
                ],
              ),
            ),
          ),
          if (chapter.isExpanded)
            Padding(
              padding: EdgeInsets.only(left: 15 + 6.5.w),
              child: Column(
                children: chapter.lesson.map((lesson) {
                  final key =
                      nodeKeys.putIfAbsent(lesson.id, () => GlobalKey());
                  bool isLastStudyLesson = courseWatchHistorys.isNotEmpty &&
                      lesson.id == courseWatchHistorys.first.lesson_id;
                  return GestureDetector(
                    key: key,
                    onTap: () {
                      _playAction(lesson);
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 6.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                  child: RichText(
                                      text: TextSpan(
                                          text: '${lesson.title}  ',
                                          style: TextStyle(
                                                  color: lesson.id ==
                                                          selectedLesson?.id
                                                      ? AppTheme.colorBlue
                                                      : const Color(0xFF606266),
                                                  fontSize: 14.sp)
                                              .pfRegular,
                                          children: [
                                    if (isLastStudyLesson)
                                      WidgetSpan(
                                        child: Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 3.w, vertical: 2.h),
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(6.r),
                                                border: Border.all(
                                                    color: const Color(
                                                        0xFF0054FF))),
                                            child: Text('上次学习',
                                                style: TextStyle(
                                                        color: const Color(
                                                            0xFF0054FF),
                                                        fontSize: 11.sp)
                                                    .pfRegular)),
                                      ),
                                  ]))),
                              Image.asset(
                                  lesson.id == selectedLesson?.id
                                      ? 'assets/png/xiaoxin/audio_playing.png'
                                      : 'assets/png/xiaoxin/menu_play.png',
                                  width: 17,
                                  height: 17),
                            ],
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            ValidatorUtils.formatDuration(lesson.total_time),
                            style: TextStyle(
                                    color: const Color(0xFF464B59),
                                    fontSize: 11.sp)
                                .pfRegular,
                          )
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  void _playAction(LessonModel lesson) {
    widget.onChange(lesson);
    setState(() {
      selectedLesson = lesson;
    });
    _getCourseWatchHistory();
    _scrollToSelectedLesson();
  }
}
