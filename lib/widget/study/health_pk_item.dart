import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/study/health_pk_model.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:permission_handler/permission_handler.dart';

class HealthPkItem extends StatefulWidget {
  final HealthPkModel item;
  final int index;
  const HealthPkItem({super.key, required this.item, required this.index});

  @override
  State<HealthPkItem> createState() => _HealthPkItemState();
}

class _HealthPkItemState extends State<HealthPkItem> {
  bool isOpen = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        setState(() {
          isOpen = !isOpen;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 20.h),
        margin: EdgeInsets.only(bottom: 12.sp),
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          children: [
            Row(
              children: [
                if (widget.index == 1)
                  Image.asset('assets/png/health/health_first.png',
                      width: 40, height: 40),
                if (widget.index == 2)
                  Image.asset('assets/png/health/health_second.png',
                      width: 40, height: 40),
                if (widget.index == 3)
                  Image.asset('assets/png/health/health_third.png',
                      width: 40, height: 40),
                if (widget.index > 3)
                  Container(
                    width: 40,
                    height: 40,
                    alignment: Alignment.center,
                    child: Text(
                      '${widget.index}',
                      style: TextStyle(
                          color: const Color(0xFF464B59), fontSize: 16.sp),
                    ),
                  ),
                SizedBox(width: 15.w),
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: SizedBox(
                    width: 46,
                    height: 46,
                    child:
                        CachedNetworkImage(imageUrl: widget.item.user.avatar),
                  ),
                ),
                SizedBox(width: 14.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(widget.item.user.name,
                        style: TextStyle(
                                color: const Color(0xFF000000), fontSize: 16.sp)
                            .pfMedium),
                    Row(
                      children: [
                        Text(
                          '已减',
                          style: TextStyle(
                                  color: const Color(0xFF464B59),
                                  fontSize: 12.sp)
                              .pfRegular,
                        ),
                        Text(
                          ' ${widget.item.health.reduce} ',
                          style: TextStyle(
                                  color: const Color(0xFF00C99F),
                                  fontSize: 14.sp)
                              .pfSemiBold,
                        ),
                        Text(
                          'kg',
                          style: TextStyle(
                                  color: const Color(0xFF464B59),
                                  fontSize: 12.sp)
                              .pfRegular,
                        ),
                      ],
                    )
                  ],
                ),
                const Expanded(child: SizedBox()),
                Row(
                  children: [
                    Column(
                      children: [
                        Text(
                          '当前',
                          style: TextStyle(
                                  color: const Color(0xFF464B59),
                                  fontSize: 14.sp)
                              .pfRegular,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          textBaseline: TextBaseline.alphabetic,
                          children: [
                            Text(
                              widget.item.health.present,
                              style: TextStyle(
                                      color: const Color(0xFFFE5D30),
                                      fontSize: 16.sp)
                                  .phBold,
                            ),
                            Text(
                              'kg',
                              style: TextStyle(
                                      color: const Color(0xFF7B7F8A),
                                      fontSize: 12.sp)
                                  .pfMedium,
                            )
                          ],
                        ),
                      ],
                    ),
                    SizedBox(width: 18.w),
                    Column(
                      children: [
                        Text(
                          '目标',
                          style: TextStyle(
                                  color: const Color(0xFF464B59),
                                  fontSize: 14.sp)
                              .pfRegular,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          textBaseline: TextBaseline.alphabetic,
                          children: [
                            Text(
                              widget.item.health.target,
                              style: TextStyle(
                                      color: const Color(0xFF3F88F1),
                                      fontSize: 16.sp)
                                  .phBold,
                            ),
                            Text(
                              'kg',
                              style: TextStyle(
                                      color: const Color(0xFF7B7F8A),
                                      fontSize: 12.sp)
                                  .pfMedium,
                            )
                          ],
                        ),
                      ],
                    ),
                    SizedBox(width: 13.w),
                    GestureDetector(
                      onTap: () {},
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: Icon(
                            isOpen
                                ? Icons.keyboard_arrow_up
                                : Icons.keyboard_arrow_down,
                            color: Color(0xFFD8D8D8)),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (isOpen) SizedBox(height: 10.h),
            if (isOpen)
              SizedBox(
                width: ScreenUtil().screenWidth,
                child: Wrap(
                  spacing: 8.w,
                  children: [
                    ...widget.item.health_log.map((e) =>
                        _dropView(e.date_month, weight: e.weight, url: e.img)),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _dropView(String month, {String? url, String? weight}) {
    double width = (ScreenUtil().screenWidth - 44.w - 32.w) / 4;

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (widget.item.health.user_id ==
            GlobalPreferences().userInfo?.user.id) {
          _showSubmitDialog(context);
        } else {
          NavigatorUtils.push(context, CommonRouter.photoView,
              arguments: {'url': url});
        }
      },
      child: Container(
        width: width,
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 8.w),
        decoration: BoxDecoration(
            color: const Color.fromRGBO(246, 248, 254, 1),
            borderRadius: BorderRadius.circular(16.r)),
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: Container(
                    width: 60,
                    height: 60,
                    color: const Color.fromRGBO(230, 237, 249, 1),
                    child: url == null
                        ? Icon(Icons.image_not_supported_outlined,
                            color: '#FFFFFF'.toColor())
                        : CachedNetworkImage(
                            imageUrl: url,
                            fit: BoxFit.fill,
                          ),
                  ),
                ),
                Text(
                  '${month.split('-').last}月',
                  style: TextStyle(
                      color: url == null
                          ? const Color.fromRGBO(177, 186, 205, 1)
                          : Colors.white),
                ),
              ],
            ),
            Text(weight == null ? '未上传' : '${weight}kg',
                style: TextStyle(fontSize: 12.sp)),
          ],
        ),
      ),
    );
  }

  Future<void> _showSubmitDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false, // 点击遮罩是否关闭对话框
      builder: (BuildContext _) => SubmitDialog(item: widget.item),
    );
  }
}

class SubmitDialog extends StatefulWidget {
  final HealthPkModel item;
  const SubmitDialog({super.key, required this.item});

  @override
  State<SubmitDialog> createState() => _SubmitDialogState();
}

class _SubmitDialogState extends State<SubmitDialog> {
  TextEditingController _weightController = TextEditingController();
  XFile? _pickedImage;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('提交体重'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            TextField(
              controller: _weightController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(hintText: '请输入体重(kg)'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                PermissionStatus status = await Permission.camera.status;
                if (status != PermissionStatus.granted) {
                  await Permission.camera.request();
                }
                final ImagePicker _picker = ImagePicker();
                final pickedFile = await _picker.pickImage(
                    source: ImageSource.camera,
                    maxWidth: 1024,
                    maxHeight: 1024,
                    imageQuality: 80);
                if (pickedFile != null) {
                  setState(() {
                    _pickedImage = pickedFile;
                  });
                }
              },
              child: const Text('拍照'),
            ),
            if (_pickedImage != null)
              Image.file(
                File(_pickedImage!.path),
                height: 100,
              ),
          ],
        ),
      ),
      actions: <Widget>[
        TextButton(
          child: const Text('取消'),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        TextButton(
          child: const Text('提交'),
          onPressed: () {
            String weight = _weightController.text;
            if (weight.isEmpty) {
              ToastUtils.show('请输入体重');
              return;
            }
            if (_pickedImage == null) {
              ToastUtils.show('请拍一张体重照片');
              return;
            }
            UserServiceProvider()
                .uploadWeightImg(_pickedImage!.path)
                .then((value) {
              if (value?.code == 0) {
                //  这里需要根据你的后端接口进行调整，获取 `widget` 需要从上下文获取
                //  假设 `_showSubmitDialog` 方法在 `_HealthPkItemState` 中调用，则可以使用 `context.findAncestorStateOfType<_HealthPkItemState>()?.widget` 获取
                final healthPkItemState =
                    context.findAncestorStateOfType<_HealthPkItemState>();
                if (healthPkItemState != null) {
                  UserServiceProvider().addHealthWeight(
                      healthPkItemState.widget.item.health.user_id,
                      healthPkItemState.widget.item.health.id,
                      double.parse(weight),
                      value?.data);
                }
                Navigator.of(context).pop();
              } else {
                EasyLoading.showError(value?.msg ?? '提交失败');
              }
            });
          },
        ),
      ],
    );
  }
}
