import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_watch_history_model.dart';
import 'package:npemployee/provider/audio_bloc/audio_event.dart';

class VideoCataLogItem extends StatefulWidget {
  final List<LessonModel> lessons;
  final List<CourseWatchHistoryModel> watchs; //看课记录
  final ChapterModel model;
  final bool hidden;
  final LessonModel? playLesson;
  final int? initialSelectedLessonIndex;
  const VideoCataLogItem(
      {super.key,
      required this.lessons,
      required this.model,
      required this.watchs,
      required this.hidden,
      this.playLesson,
      this.initialSelectedLessonIndex});

  @override
  State<VideoCataLogItem> createState() => _VideoCataLogItemState();
}

class _VideoCataLogItemState extends State<VideoCataLogItem> {
  // bool isOpen = false;
  bool _hidden = true;

  // 新增的变量，用于记录初始选中的lesson索引
  int? initialSelectedLessonIndex;

  @override
  void initState() {
    super.initState();
    _hidden = widget.hidden;
    initialSelectedLessonIndex = widget.initialSelectedLessonIndex;
    if (initialSelectedLessonIndex != null) {
      LessonModel l = widget.model.lesson[initialSelectedLessonIndex ?? 0];
      _playAction(l);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(top: 13.h),
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 20.h),
        decoration: BoxDecoration(
            color: const Color(0xFFF7F8FD),
            borderRadius: BorderRadius.circular(10.r)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                setState(() {
                  // isOpen = !isOpen;
                  _hidden = !_hidden;
                });
              },
              child: Row(
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: 30,
                    height: 30,
                    child: Image.asset(
                        !_hidden
                            ? 'assets/png/xiaoxin/menu_close.png'
                            : 'assets/png/xiaoxin/menu_open.png',
                        width: 15,
                        height: 15),
                  ),
                  Expanded(
                      child: Text(widget.model.title,
                          style: TextStyle(
                                  color: const Color(0xFF333333),
                                  fontSize: 15.sp)
                              .pfMedium)),
                  if (widget.watchs.isNotEmpty &&
                      widget.watchs.first.chapter_id == widget.model.id)
                    SizedBox(width: 10.w),
                  if (widget.watchs.isNotEmpty &&
                      widget.watchs.first.chapter_id == widget.model.id)
                    Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 3.w, vertical: 2.h),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.r),
                            border: Border.all(color: const Color(0xFF0054FF))),
                        child: Text('上次学习',
                            style: TextStyle(
                                    color: const Color(0xFF0054FF),
                                    fontSize: 11.sp)
                                .pfRegular)),
                ],
              ),
            ),

            // if (isOpen) SizedBox(height: 15.h),
            Offstage(
              offstage: _hidden,
              child: MediaQuery.removePadding(
                removeTop: true,
                context: context,
                child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: widget.model.lesson.length,
                    itemBuilder: (c, index) {
                      LessonModel lesson = widget.model.lesson[index];
                      int watchIndex = widget.watchs
                          .indexWhere((watch) => watch.lesson_id == lesson.id);
                      int watchDuration = 0;
                      if (watchIndex != -1) {
                        watchDuration = widget.watchs[watchIndex].duration;
                      }
                      return GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          _playAction(lesson);
                        },
                        child: Padding(
                            padding: EdgeInsets.only(left: 16.w, top: 16.h),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                            constraints: BoxConstraints(
                                                maxWidth:
                                                    widget.watchs.isNotEmpty &&
                                                            lesson.id ==
                                                                widget
                                                                    .watchs
                                                                    .first
                                                                    .lesson_id
                                                        ? 180.w
                                                        : 240.w),
                                            child: Text(
                                              lesson.title,
                                              style: TextStyle(
                                                      color: widget.playLesson
                                                                  ?.id ==
                                                              lesson.id
                                                          ? const Color(
                                                              0xFF0054FF)
                                                          : const Color(
                                                              0xFF333333),
                                                      fontSize: 14.sp)
                                                  .pfRegular,
                                            )),
                                        if (widget.watchs.isNotEmpty &&
                                            lesson.id ==
                                                widget.watchs.first.lesson_id)
                                          SizedBox(width: 10.w),
                                        if (widget.watchs.isNotEmpty &&
                                            lesson.id ==
                                                widget.watchs.first.lesson_id)
                                          Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 3.w,
                                                  vertical: 2.h),
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          6.r),
                                                  border: Border.all(
                                                      color: const Color(
                                                          0xFF0054FF))),
                                              child: Text('上次学习',
                                                  style: TextStyle(
                                                      color: const Color(
                                                          0xFF0054FF),
                                                      fontSize: 11.sp))),
                                      ],
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      ValidatorUtils.formatDuration(
                                          lesson.total_time),
                                      style: TextStyle(
                                              color: const Color(0xFF464B59),
                                              fontSize: 11.sp)
                                          .pfRegular,
                                    )
                                  ],
                                ),
                                Container(
                                  alignment: Alignment.center,
                                  width: 40,
                                  height: 40,
                                  child: widget.playLesson?.id == lesson.id
                                      ? Image.asset(
                                          'assets/png/xiaoxin/audio_playing.png',
                                          width: 17,
                                          height: 17)
                                      : Image.asset(
                                          'assets/png/xiaoxin/menu_play.png',
                                          width: 17,
                                          height: 17,
                                        ),
                                ),
                              ],
                            )),
                      );
                    }),
              ),
            )
          ],
        ));
  }

  void _playAction(LessonModel lesson) {
    BlocManager().audioBloc.add(AudioPlayingEvent(lesson));
    if (BlocManager().audioBloc.audioPlayer != null) {
      int seekToIndex = widget.lessons.indexWhere((e) => e.id == lesson.id);
      BlocManager()
          .audioBloc
          .audioPlayer!
          .seek(Duration.zero, index: seekToIndex);
      if (!BlocManager().audioBloc.audioPlayer!.playing) {
        BlocManager().audioBloc.audioPlayer?.play();
      }
    }
    // if (widget.hidden) {
    //   NavigatorUtils.pop(context);
    // }
  }
}
