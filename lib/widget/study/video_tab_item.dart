import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';

class VideoTabItem extends StatelessWidget {
  final String title;
  final bool isSelect;
  final VoidCallback onTap;
  const VideoTabItem(
      {super.key,
      required this.title,
      required this.isSelect,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        height: 40.h,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              title,
              style: isSelect
                  ? TextStyle(color: const Color(0xFF222222), fontSize: 16.sp)
                      .pfSemiBold
                  : TextStyle(color: const Color(0xFF999999), fontSize: 16.sp)
                      .pfMedium,
            ),
            SizedBox(height: 5.h),
            if (isSelect)
              Container(
                width: 15.w,
                height: 4.h,
                decoration: BoxDecoration(
                    color: const Color(0xFF0054FF),
                    borderRadius: BorderRadius.circular(2.r)),
              ),
          ],
        ),
      ),
    );
  }
}
