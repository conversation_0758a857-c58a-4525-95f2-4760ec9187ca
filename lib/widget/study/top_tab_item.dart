import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TopTabItem extends StatelessWidget {
  final String svgPath;
  final bool isSelect;
  final VoidCallback onTap;

  const TopTabItem({
    super.key,
    required this.onTap,
    required this.svgPath,
    required this.isSelect,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Image.asset(
        svgPath,
        width: 156.w,
        height: isSelect ? 54.h : 44.h,
      ),
    );
  }
}
