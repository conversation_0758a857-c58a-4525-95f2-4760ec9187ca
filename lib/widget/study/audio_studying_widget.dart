import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/model/study/course_watching_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';

class AudioStudyingWidget extends StatefulWidget {
  final int courseId;
  const AudioStudyingWidget({super.key, required this.courseId});

  @override
  State<AudioStudyingWidget> createState() => _AudioStudyingWidgetState();
}

class _AudioStudyingWidgetState extends State<AudioStudyingWidget> {
  List<CourseWatchingModel> watchings = [];
  Timer? timer;

  void _getWatchingList() {
    UserServiceProvider().getCourseWatchingList(widget.courseId,
        cacheCallBack: (value) {
      // _formatCourseWatchingData(value, true);
    }, successCallBack: (value) {
      _formatCourseWatchingData(value, false);
    }, errorCallBack: (value) {});
  }

  _formatCourseWatchingData(ResultData? value, bool isCache) {
    watchings.clear();
    for (var element in value?.data) {
      watchings.add(CourseWatchingModel.formJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _startTimer() {
    timer = Timer.periodic(const Duration(seconds: 10), (t) {
      _getWatchingList();
    });
  }

  @override
  void initState() {
    super.initState();
    _getWatchingList();
    _startTimer();
  }

  @override
  void dispose() {
    timer?.cancel();
    timer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        watchings.isEmpty
            ? Container()
            : GestureDetector(
                onTap: () => NavigatorUtils.push(
                    context, StudyRouter.studyingPage,
                    arguments: {'courseId': widget.courseId}),
                child: Row(
                  children: [
                    SizedBox(
                        width: watchings.length == 1
                            ? 25
                            : (watchings.length == 2 ? 45 : 65),
                        height: 25,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            if (watchings.isNotEmpty)
                              Positioned(
                                top: 0,
                                left: 0,
                                child: _buildAvatar(watchings[0].user.avatar),
                              ),
                            if (watchings.length > 1)
                              Positioned(
                                left: 20,
                                child: _buildAvatar(watchings[1].user.avatar),
                              ),
                            if (watchings.length > 2)
                              Positioned(
                                left: 40,
                                child: _buildAvatar(watchings[2].user.avatar),
                              ),
                            if (watchings.length > 3)
                              Positioned(
                                left: 60,
                                child: _buildAvatarWithEllipsis(),
                              ),
                          ],
                        )),
                    const SizedBox(width: 5),
                    Text(
                      '${watchings.length}人在学习',
                      style: TextStyle(
                          fontSize: 12.sp, color: const Color(0xFF929292)),
                    ),
                    const Icon(Icons.arrow_forward_ios,
                        size: 12, color: Color(0xFF929292)),
                  ],
                ),
              ),
        const Expanded(child: SizedBox()),
        // 右侧按钮
        SizedBox(
          width: 113.w,
          height: 40.h,
          child: ElevatedButton.icon(
            onPressed: () => NavigatorUtils.push(
                context, StudyRouter.capabilityStudyRank,
                arguments: {'course_id': widget.courseId}),
            icon: const Icon(Icons.bar_chart, color: Colors.black, size: 15),
            label: Text('学习排名',
                style:
                    TextStyle(color: const Color(0xFF222222), fontSize: 13.sp)
                        .pfMedium),
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.zero,
              backgroundColor: Colors.grey[200], // 按钮背景颜色
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20), // 圆角按钮
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 创建头像
  Widget _buildAvatar(String url) {
    return CircleAvatar(
      backgroundImage: NetworkImage(url),
      radius: 12.5,
    );
  }

  // 创建带省略号的头像
  Widget _buildAvatarWithEllipsis() {
    return Container(
      width: 25,
      height: 25,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12.5),
      ),
      child: Text(
        '...',
        style: TextStyle(color: Colors.grey[700], fontSize: 18),
      ),
    );
  }
}
