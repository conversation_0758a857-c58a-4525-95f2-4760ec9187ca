import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/model/knowledge_contest/paper_category_model.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';

class KnowledgeContestItem extends StatelessWidget {
  final PaperModel item;
  final Function(PaperModel) refresh;
  const KnowledgeContestItem(
      {super.key, required this.item, required this.refresh});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: const [
            BoxShadow(
                color: Color.fromRGBO(0, 42, 169, 0.03),
                offset: Offset(0, 4),
                blurRadius: 10,
                spreadRadius: 0)
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.paper_name,
            style: TextStyle(color: const Color(0xFF000000), fontSize: 16.sp)
                .pfMedium,
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Text('${item.question_total}道题',
                  style:
                      TextStyle(color: const Color(0xFF898B97), fontSize: 13.sp)
                          .pfRegular),
              if (item.people_total != null)
                Text(' | ${item.people_total}人参加',
                    style: TextStyle(
                            color: const Color(0xFF898B97), fontSize: 13.sp)
                        .pfRegular),
              if (item.paper_status == 'CONTINUE')
                Text(' | 剩余${item.question_remain_total}道题',
                    style: TextStyle(
                            color: const Color(0xFF0054FF), fontSize: 13.sp)
                        .pfRegular),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                  "时间：${DateTimeUtils.formatDataTomdhm(item.start_time, item.end_time)}",
                  style:
                      TextStyle(color: const Color(0xFF909090), fontSize: 13.sp)
                          .pfMedium),
              if (item.paper_status == 'NOT')
                GestureDetector(
                  // onTap: () => onTap(item.paper_status),
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 7.h),
                    decoration: BoxDecoration(
                        color: const Color(0x50AAC6FF),
                        borderRadius: BorderRadius.circular(10.r)),
                    child: Text(
                      '暂未开始',
                      style: TextStyle(
                              color: const Color(0x500054FF), fontSize: 12.sp)
                          .pfMedium,
                    ),
                  ),
                ),
              if (item.paper_status == 'READY')
                GestureDetector(
                  onTap: () => _onTap(context),
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 7.h),
                    decoration: BoxDecoration(
                        color: const Color(0x50AAC6FF),
                        borderRadius: BorderRadius.circular(10.r)),
                    child: Text(
                      '开始答题',
                      style: TextStyle(
                              color: const Color(0xFF0054FF), fontSize: 12.sp)
                          .pfMedium,
                    ),
                  ),
                ),
              if (item.paper_status == 'CONTINUE')
                GestureDetector(
                  onTap: () => _onTap(context),
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 7.h),
                    decoration: BoxDecoration(
                        color: const Color(0x50AAC6FF),
                        borderRadius: BorderRadius.circular(10.r)),
                    child: Text(
                      '继续答题',
                      style: TextStyle(
                              color: const Color(0xFF0054FF), fontSize: 12.sp)
                          .pfMedium,
                    ),
                  ),
                ),
              if (item.paper_status == 'OVER')
                GestureDetector(
                  onTap: () => _onTap(context),
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 15.w, vertical: 7.h),
                    decoration: BoxDecoration(
                        color: const Color(0x50AAC6FF),
                        borderRadius: BorderRadius.circular(10.r)),
                    child: Text(
                      '查看结果',
                      style: TextStyle(
                              color: const Color(0xFF0054FF), fontSize: 12.sp)
                          .pfMedium,
                    ),
                  ),
                ),
            ],
          )
        ],
      ),
    );
  }

  void _onTap(BuildContext cxt) {
    if (item.paper_status == 'READY' || item.paper_status == 'CONTINUE') {
      //READY
      NavigatorUtils.push(cxt, StudyRouter.answerQuestion, arguments: {
        'paperId': item.paper_id,
        'paperStatus': item.paper_status,
        'limitTime': item.limit_time,
      }).then((value) {
        refresh(item);
      });
    } else if (item.paper_status == 'OVER') {
      NavigatorUtils.push(cxt, StudyRouter.transcript,
          arguments: {'paperId': item.paper_id});
    }
  }
}
