import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import '../../common/app_theme.dart';

class GradientIconItem extends StatelessWidget {
  final String title;
  final String svgPath;
  final Color startColor;
  final Color endColor;
  final VoidCallback? onTap;

  const GradientIconItem({
    Key? key,
    required this.title,
    required this.svgPath,
    required this.startColor,
    required this.endColor,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap, // Add the callback for the tap event
      child: Container(
        height: 85,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(9),
          gradient: LinearGradient(
            begin: Alignment.topCenter, // Start the gradient at the top
            end: Alignment.bottomCenter, // End the gradient at the bottom
            colors: [startColor, endColor],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 0),
              child: CachedNetworkImage(
                imageUrl: svgPath,
                width: 40,
                height: 40,
                placeholder: (context, url) {
                  return Container(
                    decoration: BoxDecoration(
                        color: const Color(0xFFEEEEEE),
                        borderRadius: BorderRadius.circular(10.r)),
                    width: 40,
                    height: 40,
                  );
                },
              ),
              /* SvgPicture.asset(
                svgPath,
                height: 40, // Adjust the size of the SVG
              ), */
            ),
            const SizedBox(height: 5),
            Text(
              title,
              style: TextStyle(fontSize: 12.sp, color: AppTheme.colorBlackTitle)
                  .pfSemiBold,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
