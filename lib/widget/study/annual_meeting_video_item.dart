import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:share_plus/share_plus.dart';
// import 'package:share_plus/share_plus.dart';

class AnnualMeetingVideoItem extends StatelessWidget {
  final CourseListModel item;
  const AnnualMeetingVideoItem({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(16.w, 15.h, 16.w, 0),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15.r),
          boxShadow: const [
            BoxShadow(
                color: Color.fromRGBO(0, 42, 169, 0.03),
                offset: Offset(0, 4),
                spreadRadius: 0,
                blurRadius: 10)
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(item.name,
              style: TextStyle(color: const Color(0xFF323640), fontSize: 15.sp)
                  .pfMedium),
          SizedBox(height: 10.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.r),
                child: CachedNetworkImage(
                    imageUrl: item.image,
                    height: 66.h,
                    width: 122.w,
                    fit: BoxFit.fill),
              ),
              SizedBox(
                height: 66.h,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(DateTimeUtils.formatDataToymdmd(item.start_time, null),
                        style: TextStyle(
                                color: const Color(0xFF9B9FAD), fontSize: 12.sp)
                            .pfRegular),
                    Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            Share.share(item.name, subject: item.introduction);
                          },
                          child: SvgPicture.asset(
                              'assets/svg/xiaoxin/share.svg',
                              width: 35.w,
                              height: 30.h),
                        ),
                        SizedBox(width: 10.w),
                        GestureDetector(
                          onTap: () {
                            NavigatorUtils.push(context, StudyRouter.videoPlay,
                                arguments: {'model': item});
                          },
                          child: Container(
                            alignment: Alignment.center,
                            width: 64.w,
                            height: 30.h,
                            decoration: BoxDecoration(
                                color: const Color(0xFF0054FF),
                                borderRadius: BorderRadius.circular(8.r)),
                            child: Text(
                              '查看',
                              style: TextStyle(
                                      color: const Color(0xFFFFFFFF),
                                      fontSize: 12.sp)
                                  .pfMedium,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
