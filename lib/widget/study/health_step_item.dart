import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/study/health_rank_model.dart';

class HealthStepItem extends StatelessWidget {
  final HealthRankModel item;
  final int index;
  const HealthStepItem({super.key, required this.item, required this.index});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 20.h),
      margin: EdgeInsets.only(bottom: 12.sp),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          if (index == 1)
            Image.asset('assets/png/health/health_first.png',
                width: 40, height: 40),
          if (index == 2)
            Image.asset('assets/png/health/health_second.png',
                width: 40, height: 40),
          if (index == 3)
            Image.asset('assets/png/health/health_third.png',
                width: 40, height: 40),
          if (index > 3)
            Container(
              width: 40,
              height: 40,
              alignment: Alignment.center,
              child: Text(
                '$index',
                style:
                    TextStyle(color: const Color(0xFF464B59), fontSize: 16.sp)
                        .pfRegular,
              ),
            ),
          SizedBox(width: 15.w),
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: SizedBox(
                width: 46,
                height: 46,
                child: CachedNetworkImage(imageUrl: item.user.avatar)),
          ),
          SizedBox(width: 14.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  (item.walk.user_id == GlobalPreferences().userInfo?.user.id)
                      ? "${item.user.name}（我）"
                      : item.user.name,
                  style:
                      TextStyle(color: const Color(0xFF000000), fontSize: 16.sp)
                          .pfMedium),
              Text(
                item.user.department.name,
                style:
                    TextStyle(color: const Color(0xFF999999), fontSize: 11.sp)
                        .pfRegular,
              ),
            ],
          ),
          const Expanded(child: SizedBox()),
          Row(
            children: [
              Text(
                "今日",
                style:
                    TextStyle(color: const Color(0xFF464B59), fontSize: 12.sp)
                        .pfRegular,
              ),
              Text(
                " ${item.walk.walk_num} ",
                style:
                    TextStyle(color: const Color(0xFF00C99F), fontSize: 22.sp)
                        .phBold,
              ),
              Text(
                "步",
                style:
                    TextStyle(color: const Color(0xFF464B59), fontSize: 12.sp)
                        .pfRegular,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class HealthStepMeItem extends StatelessWidget {
  final HealthRankModel? item;
  final int sort;
  final bool? stepPermission;
  final Function() onTap;
  const HealthStepMeItem(
      {super.key,
      required this.item,
      required this.sort,
      this.stepPermission,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 20.h),
      margin: EdgeInsets.only(bottom: 12.sp),
      decoration: BoxDecoration(
        color: const Color(0xFFD7E9FA),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: SizedBox(
                width: 46,
                height: 46,
                child: CachedNetworkImage(
                    imageUrl:
                        item?.user.avatar ?? ValidatorUtils.testImageUrl)),
          ),
          SizedBox(width: 14.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${GlobalPreferences().userInfo?.user.name}（我）',
                  style:
                      TextStyle(color: const Color(0xFF000000), fontSize: 16.sp)
                          .pfMedium),
              if (stepPermission != null && !stepPermission!)
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => onTap(),
                  child: Row(
                    children: [
                      Text(
                        '您暂未授权步数权限，',
                        style: TextStyle(
                                color: const Color(0xFF999999), fontSize: 11.sp)
                            .pfRegular,
                      ),
                      Text(
                        '去授权>',
                        style: TextStyle(
                                color: const Color(0xFF0054FF), fontSize: 11.sp)
                            .pfRegular,
                      )
                    ],
                  ),
                )
            ],
          ),
          const Expanded(child: SizedBox()),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    "今日",
                    style: TextStyle(
                            color: const Color(0xFF464B59), fontSize: 12.sp)
                        .pfRegular,
                  ),
                  Text(
                    stepPermission != null && !stepPermission!
                        ? ' 0 '
                        : ' ${item?.walk.walk_num ?? 0} ',
                    style: TextStyle(
                            color: const Color(0xFF00C99F), fontSize: 22.sp)
                        .phBold,
                  ),
                  Text(
                    "步",
                    style: TextStyle(
                            color: const Color(0xFF464B59), fontSize: 12.sp)
                        .pfRegular,
                  ),
                ],
              ),
              Text(
                  stepPermission != null && !stepPermission!
                      ? '--'
                      : '排名 ${sort + 1}',
                  style:
                      TextStyle(color: const Color(0xFF464B59), fontSize: 12.sp)
                          .pfRegular)
            ],
          ),
        ],
      ),
    );
  }
}
