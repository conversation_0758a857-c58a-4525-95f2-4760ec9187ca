import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/manager/preferences_manager.dart';
import 'package:npemployee/model/download/task_info.dart';
import 'package:npemployee/model/study/annual_meeting_PPT_model.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:share_plus/share_plus.dart';

class MyDownloadAnnualMeetingPPTItem extends StatefulWidget {
  final TaskInfo task;

  const MyDownloadAnnualMeetingPPTItem({super.key, required this.task});

  @override
  State<MyDownloadAnnualMeetingPPTItem> createState() =>
      MyDownloadAnnualMeetingPPTItemState();
}

class MyDownloadAnnualMeetingPPTItemState
    extends State<MyDownloadAnnualMeetingPPTItem> {
  int? progress;
  String downloadBtnText = '下载';
  DownloadTaskStatus? status;

  void _refreshUI(TaskInfo task) {
    progress = task.progress;
    status = task.status;
    switch (task.status) {
      case DownloadTaskStatus.undefined:
      case null:
        downloadBtnText = '下载';
        break;
      case DownloadTaskStatus.enqueued:
        downloadBtnText = '待下载';
        break;
      case DownloadTaskStatus.running:
        downloadBtnText = '已下载 $progress%';
        break;
      case DownloadTaskStatus.complete:
        break;
      case DownloadTaskStatus.failed:
        downloadBtnText = '下载失败';
        break;
      case DownloadTaskStatus.paused:
        downloadBtnText = '暂停';
        break;

      case DownloadTaskStatus.canceled:
        downloadBtnText = '下载失败';
        break;
      default:
    }
  }

  @override
  void initState() {
    super.initState();

    // _refreshUI(widget.task);
  }

  @override
  Widget build(BuildContext context) {
    _refreshUI(widget.task);
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      padding: EdgeInsets.symmetric(vertical: 17.h),
      decoration: const BoxDecoration(
          border:
              Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 0.5))),
      child: Row(
        children: [
          Image.asset('assets/png/xiaoxin/ic_pdf.png', width: 27, height: 27),
          SizedBox(width: 10.w),
          Expanded(
              child: Text(widget.task.name ?? "",
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 15.sp)
                          .pfMedium)),
          if (status == DownloadTaskStatus.complete)
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () async {
                if (widget.task.link != null) {
                  String fileType = getFileTypeFromUrl(widget.task.link!);
                  if (fileType != "unknown") {
                    if (fileType == "pdf") {
                      NavigatorUtils.push(context, CommonRouter.pdfPreviewPage,
                          arguments: {
                            'filePath':
                                '${await AppInfo().pptDownloadDir}/${widget.task.name}.pdf',
                            'title': widget.task.name,
                          });
                    } else {
                      XFile xFile = XFile(
                          '${await AppInfo().pptDownloadDir}/${widget.task.name}.$fileType');
                      Share.shareXFiles([xFile]);
                    }
                  }
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 7.h),
                decoration: BoxDecoration(
                    color: const Color(0xFFEFF4FF),
                    borderRadius: BorderRadius.circular(10.r)),
                child: Text('预览',
                    style: TextStyle(
                            color: const Color(0xFF0054FF), fontSize: 14.sp)
                        .pfRegular),
              ),
            ),
          SizedBox(width: 15.w),
          GestureDetector(
            onTap: () async {
              if (status == DownloadTaskStatus.complete) {
                if (widget.task.link != null) {
                  String fileType = getFileTypeFromUrl(widget.task.link!);
                  if (fileType != "unknown") {
                    XFile xFile = XFile(
                        '${await AppInfo().pptDownloadDir}/${widget.task.name}.$fileType');
                    Share.shareXFiles([xFile]);
                  }
                }
              } else {
                if (status == DownloadTaskStatus.undefined || status == null) {
                  _requestDownload(widget.task);
                } else if (status == DownloadTaskStatus.running ||
                    status == DownloadTaskStatus.enqueued) {
                  _pauseDownload(widget.task);
                } else if (status == DownloadTaskStatus.paused) {
                  if (widget.task.taskId != null) {
                    _resumeDownload(widget.task);
                  } else {
                    _requestDownload(widget.task);
                  }
                } else if (status == DownloadTaskStatus.failed) {
                  _retryDownload(widget.task);
                } else if (status == DownloadTaskStatus.canceled) {
                  _requestDownload(widget.task);
                }
              }
            },
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 7.h),
              decoration: BoxDecoration(
                color: const Color(0xFFFFF4ED),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: status == DownloadTaskStatus.complete
                  ? Image.asset('assets/png/xiaoxin/ppt_share.png',
                      width: 19.5.w)
                  : Text(
                      downloadBtnText,
                      style: TextStyle(
                        color: const Color(0xFFFF7000),
                        fontSize: 14.sp,
                      ).pfRegular,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _requestDownload(TaskInfo task) async {
    task.taskId = await FlutterDownloader.enqueue(
      url: task.link!,
      fileName: '${task.name}.${task.link!.split('.').last}',
      savedDir: await AppInfo().pptDownloadDir,
      saveInPublicStorage: false,
      openFileFromNotification: false,
    );
    await PreferencesManager().addToMeetingPptDownloads(task);
  }

  Future<void> _pauseDownload(TaskInfo task) async {
    await FlutterDownloader.pause(taskId: task.taskId!);
  }

  Future<void> _resumeDownload(TaskInfo task) async {
    final newTaskId = await FlutterDownloader.resume(taskId: task.taskId!);
    task.taskId = newTaskId;
  }

  Future<void> _retryDownload(TaskInfo task) async {
    final newTaskId = await FlutterDownloader.retry(taskId: task.taskId!);
    task.taskId = newTaskId;
  }

  String getFileTypeFromUrl(String url) {
    // 解析URL并提取路径部分
    Uri uri = Uri.parse(url);
    String path = uri.path;

    // 查找最后一个点号的位置
    int dotIndex = path.lastIndexOf('.');

    // 如果找不到点号，表示没有扩展名
    if (dotIndex == -1) {
      return 'unknown';
    }

    // 提取扩展名并返回
    String extension = path.substring(dotIndex + 1);
    return extension;
  }
}
