import 'dart:async';

import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/study/chewie/custom_control.dart';
import 'package:video_player/video_player.dart';

class ChewiePlayer extends StatefulWidget {
  final double width;
  final double height;
  final LessonModel? lesson;
  final bool? playStatus; //控制是否播放
  const ChewiePlayer({
    super.key,
    required this.lesson,
    required this.width,
    required this.height,
    this.playStatus,
  });

  @override
  State<ChewiePlayer> createState() => _ChewiePlayerState();
}

class _ChewiePlayerState extends State<ChewiePlayer>
    with WidgetsBindingObserver {
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  LessonModel? playingLesson;
  int currentPosition = -1; //当前播放进度

  Timer? _timer;
  int _timerSeconds = 0;
  bool wasPlaying = false; // 添加一个状态变量来跟踪播放状态

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_timerSeconds >= 60) {
        timer.cancel();
        _timerSeconds = 0;
        await _addWatchSaveDate();
        _startTimer();
        return;
      }
      _timerSeconds++;
    });
  }

  void _cancelTimer() {
    _timer?.cancel();
    _timerSeconds = 0;
    _timer = null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    playingLesson = widget.lesson;
    _initializePlayer();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('App 在前台');
        break;
      case AppLifecycleState.inactive:
        debugPrint('App 非活动状态，比如打电话');
        break;
      case AppLifecycleState.paused:
        debugPrint('App 退到后台');
        _videoPlayerController?.pause();
        break;
      case AppLifecycleState.detached:
        debugPrint('App 应用完全分离');
        break;
      default:
    }
  }

  @override
  void didUpdateWidget(covariant ChewiePlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.lesson != oldWidget.lesson) {
      _addWatchSave();
      playingLesson = widget.lesson;
      _initializePlayer();
    }
    if (widget.playStatus != null &&
        widget.playStatus != oldWidget.playStatus) {
      if (widget.playStatus!) {
        _resumeVideo();
      } else {
        _pauseVideo();
      }
    }
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose().then((v) {
      _videoPlayerController = null;
    });
    _chewieController?.dispose();
    _chewieController = null;
    _timer?.cancel();
    _timer = null;
    WidgetsBinding.instance.removeObserver(this);
    _addWatchSave();
    super.dispose();
  }

  void _initializePlayer() async {
    if (playingLesson == null) {
      return;
    }
    if (_videoPlayerController != null) {
      await _videoPlayerController!.dispose();
      _videoPlayerController = null;
      _chewieController?.dispose();
      _chewieController = null;
      setState(() {});
    }
    _videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(playingLesson!.media.first.media_url));
    await _videoPlayerController?.initialize();
    _onPlayerEvent();

    ResultData? res = await UserServiceProvider().getCourseWatchListAsync(
        course_id: playingLesson!.course_id,
        chapter_id: playingLesson!.chapter_id,
        lesson_id: playingLesson!.id);
    int lastDuration = 0;
    if (res?.data != null) {
      //从上次观看历史开始
      lastDuration = res?.data.first['duration'];
      if (lastDuration >= (playingLesson!.total_time)) {
        //上次已看完，重新开始
        lastDuration = 0;
      }
    }

    _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: true,
        autoInitialize: true,
        startAt: Duration(seconds: lastDuration),
        showControlsOnInitialize: false,
        bufferingBuilder: (context) => Container(),
        customControls: CustomControl(),
        allowMuting: false,
        showOptions: false,
        materialProgressColors: ChewieProgressColors(
            playedColor: Colors.white,
            bufferedColor: Colors.white.withOpacity(0.4),
            handleColor: Colors.white,
            backgroundColor: Colors.white.withOpacity(0.3)));

    _initWatchSave();
    setState(() {});
  }

  void _onPlayerEvent() {
    _videoPlayerController!.addListener(() {
      final position = _videoPlayerController!.value.position;
      currentPosition = position.inSeconds;
      bool isPlaying = _videoPlayerController!.value.isPlaying;
      if (isPlaying != wasPlaying) {
        if (isPlaying) {
          _cancelTimer();
          _startTimer();
        } else {
          _cancelTimer();
          _addWatchSave();
        }
        wasPlaying = isPlaying;
      }
    });
  }

  //提交看课记录
  Future<void> _addWatchSave() async {
    if (currentPosition > 0) {
      debugPrint('** 上传看课记录:${playingLesson?.id} -- $currentPosition');
      await UserServiceProvider().addCourseWatchSave(playingLesson!.chapter_id,
          playingLesson!.course_id, currentPosition, playingLesson!.id);
    }
  }

  //初始化看课时长
  void _initWatchSave() {
    debugPrint('** 初始化看课时长:${playingLesson?.id} -- $currentPosition');
    UserServiceProvider().initCourseWatchSaveDate(playingLesson!.course_id);
  }

  //添加看课时长
  Future<ResultData?> _addWatchSaveDate() async {
    debugPrint('** 上传看课时长:${playingLesson?.id} -- $currentPosition');
    ResultData? data = await UserServiceProvider().addCourseWatchSaveDate(
        playingLesson!.chapter_id,
        playingLesson!.course_id,
        currentPosition,
        playingLesson!.id);
    return data;
  }

  void _pauseVideo() {
    if (_videoPlayerController != null &&
        _videoPlayerController!.value.isPlaying) {
      _videoPlayerController?.pause();
    }
  }

  void _resumeVideo() {
    if (_videoPlayerController != null &&
        !_videoPlayerController!.value.isPlaying) {
      _videoPlayerController?.play();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.black,
      child: _chewieController != null &&
              _chewieController!.videoPlayerController.value.isInitialized
          ? Chewie(
              controller: _chewieController!,
            )
          : const Center(
              child: SizedBox(
                width: 30,
                height: 30,
                child: CircularProgressIndicator(
                    color: Colors.white, strokeWidth: 3),
              ),
            ),
    );
  }
}
