import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_info.dart';

import '../../common/app_theme.dart';

class SimpleIconItem extends StatelessWidget {
  final String title;
  final String svgPath;
  final bool? isDev;
  final VoidCallback? onTap;

  const SimpleIconItem({
    Key? key,
    required this.title,
    required this.svgPath,
    required this.isDev,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (isDev ?? false) {
          EasyLoading.showInfo('功能开发中，敬请期待');
        } else {
          if (onTap != null) {
            onTap!();
          }
        }
      }, // Add the callback for the tap event
      child: Stack(
        children: [
          Container(
            width: (ScreenUtil().screenWidth - 32) / 4,
            // height: 70,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 16),
                CachedNetworkImage(
                  imageUrl: svgPath,
                  width: 42,
                  height: 42,
                  placeholder: (context, url) {
                    return Container(
                      decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(10.r)),
                      width: 42,
                      height: 42,
                    );
                  },
                ),
                /*  SvgPicture.asset(
                  svgPath,
                  height: 42,
                  width: 42,
                ), */
                const SizedBox(height: 10),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                          fontSize: 12.sp, color: AppTheme.colorBlackTitle)
                      .pfRegular,
                ),
              ],
            ),
          ),
          if (isDev ?? false)
            Positioned(
              right: 15,
              child: SvgPicture.asset(
                'assets/svg/study/developing.svg',
                height: 15,
              ),
            ),
        ],
      ),
    );
  }
}
