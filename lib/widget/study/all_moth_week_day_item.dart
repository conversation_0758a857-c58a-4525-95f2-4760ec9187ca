import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';

class AllMonthWeekDayItem extends StatelessWidget {
  final VoidCallback onTap;
  final String title;
  final bool isSelect;
  final double? itemWith;

  const AllMonthWeekDayItem({
    super.key,
    required this.onTap,
    required this.title,
    required this.isSelect,
    this.itemWith,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(vertical: 7.h),
        width: itemWith ?? 246.w / 4,
        decoration: BoxDecoration(
            color: isSelect ? Colors.white : Colors.transparent,
            borderRadius: BorderRadius.circular(17.5.r)),
        child: Text(
          title,
          style: TextStyle(
            color: isSelect ? const Color(0xFF242424) : const Color(0xFF999999),
            fontSize: 13.sp,
          ).pfMedium,
        ),
      ),
    );
  }
}
