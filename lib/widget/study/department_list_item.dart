import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/model/study/depart_rank_model.dart';

class DepartmentListItem extends StatelessWidget {
  final int index;
  final DepartRankModel item;
  final bool isDepartment;
  const DepartmentListItem(
      {super.key,
      required this.item,
      required this.isDepartment,
      required this.index});

  String formatDuration(int duration) {
    double hours = duration / 3600;
    String formattedDuration = hours.toStringAsFixed(2);
    if (formattedDuration.endsWith(".00")) {
      formattedDuration = hours.toStringAsFixed(0);
    }
    return formattedDuration;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
      decoration: const BoxDecoration(
          border: Border(
        bottom:
            BorderSide(color: Color.fromRGBO(230, 230, 230, 0.5), width: 0.5),
      )),
      child: Row(
        children: [
          Container(
            width: 30.w,
            alignment: Alignment.center,
            child: Text(
              '$index',
              style: TextStyle(color: _rankingColor(index), fontSize: 16.sp)
                  .phHeavy,
            ),
          ),
          Container(
            width: isDepartment ? 230.w : 100.w,
            alignment: Alignment.center,
            child: Text(item.name,
                style:
                    TextStyle(color: const Color(0xFF232323), fontSize: 14.sp)
                        .pfRegular),
          ),
          if (!isDepartment)
            Container(
              width: 60.w,
              alignment: Alignment.center,
              child: Text('${item.num}',
                  style:
                      TextStyle(color: const Color(0xFF232323), fontSize: 14.sp)
                          .pfRegular),
            ),
          if (isDepartment)
            SizedBox(
              width: 80.w,
              child: Text(
                  textAlign: TextAlign.end,
                  '${formatDuration(item.total_duration)}小时',
                  style:
                      TextStyle(color: const Color(0xFF232323), fontSize: 14.sp)
                          .pfRegular),
            ),
          if (!isDepartment)
            SizedBox(
              width: 150.w,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        '总时长',
                        style: TextStyle(
                                color: const Color(0xFF464B59), fontSize: 14.sp)
                            .pfRegular,
                      ),
                      Text(
                        ' ${formatDuration(item.total_duration)} ',
                        style: TextStyle(
                                color: const Color(0xFFFE5D30), fontSize: 16.sp)
                            .phBold,
                      ),
                      Text(
                        '小时',
                        style: TextStyle(
                                color: const Color(0xFF7B7F8A), fontSize: 12.sp)
                            .pfMedium,
                      )
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        '人均时长',
                        style: TextStyle(
                                color: const Color(0xFF464B59), fontSize: 14.sp)
                            .pfRegular,
                      ),
                      Text(
                        ' ${formatDuration(item.avg_duration.toInt())} ',
                        style: TextStyle(
                                color: const Color(0xFF3F88F1), fontSize: 16.sp)
                            .phBold,
                      ),
                      Text(
                        '小时',
                        style: TextStyle(
                                color: const Color(0xFF7B7F8A), fontSize: 12.sp)
                            .pfMedium,
                      )
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Color _rankingColor(int rank) {
    if (rank == 1) {
      return const Color(0xFFFF4713);
    } else if (rank == 2) {
      return const Color(0xFFFDA052);
    } else if (rank == 3) {
      return const Color(0xFF6E9DFF);
    } else {
      return const Color(0xFFBABABA);
    }
  }
}

class DepartmentListFirstItem extends StatelessWidget {
  final String departmentName;
  final bool isDepartment;
  const DepartmentListFirstItem(
      {super.key, required this.isDepartment, required this.departmentName});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      height: 40.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
          color: const Color(0xFFE2EEFB),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r), topRight: Radius.circular(16.r))),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 30.w,
            child: Text('排名', style: _titleStyle()),
          ),
          Container(
            alignment: Alignment.center,
            width: isDepartment ? 230.w : 100.w,
            child: Text(isDepartment ? departmentName : '部门',
                style: _titleStyle()),
          ),
          if (!isDepartment)
            Container(
              alignment: Alignment.center,
              width: 60.w,
              child: Text('学习人数', style: _titleStyle()),
            ),
          Container(
            alignment: Alignment.centerRight,
            width: isDepartment ? 70.w : 130.w,
            child: Text('学习时长', style: _titleStyle()),
          ),
        ],
      ),
    );
  }

  TextStyle _titleStyle() {
    return TextStyle(color: const Color(0xFF808080), fontSize: 13.sp).pfRegular;
  }
}
