import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class CommonNav extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? rightWidget;
  final Color? bacColor;
  final bool? centerTitle;
  final void Function()? onBack;
  final bool? enableBack;
  const CommonNav(
      {super.key,
      required this.title,
      this.rightWidget,
      this.bacColor,
      this.centerTitle = true,
      this.onBack,
      this.enableBack = true});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: bacColor ?? Colors.white,
      title: Text(
        title,
        style: TextStyle(
                color: const Color(0xFF333333),
                fontSize:
                    MediaQuery.of(context).orientation == Orientation.portrait
                        ? 18.sp
                        : 10.sp) // 根据方向调整高度 18.sp)
            .pfSemiBold,
      ),
      toolbarTextStyle:
          TextStyle(color: const Color(0xFF000000), fontSize: 18.sp).pfSemiBold,
      centerTitle: centerTitle ?? true,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: enableBack!
          ? IconButton(
              onPressed: onBack ??
                  () {
                    NavigatorUtils.pop(context);
                  },
              icon: const Icon(
                Icons.arrow_back_ios,
                color: Color(0xFF000000),
              ),
              iconSize: 18,
            )
          : const Text(' '),
      actions: rightWidget,
    );
  }

  @override
  Size get preferredSize =>
      Size.fromHeight(88.h - ScreenUtil().statusBarHeight);
}
