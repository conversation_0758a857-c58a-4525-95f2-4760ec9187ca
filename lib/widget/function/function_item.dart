import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';

class FunctionItem extends StatelessWidget {
  final String title;
  final String image;
  final bool? isDev;
  final bool? isOpen;
  final VoidCallback? onTap;
  const FunctionItem(
      {super.key,
      required this.title,
      required this.image,
      this.isDev,
      this.isOpen,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (isDev ?? false) {
          EasyLoading.showInfo('功能开发中，敬请期待');
        } else if (!isOpen!) {
          EasyLoading.showInfo('活动暂未开启');
        } else {
          if (onTap != null) {
            onTap!();
          }
        }
      }, // Add the callback for the tap event
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF8F8F8),
              borderRadius: BorderRadius.circular(8.r),
            ),

            width: (ScreenUtil().screenWidth - 32.w - (14.w * 3)) / 4,
            // height: 70,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 16),
                CachedNetworkImage(
                  imageUrl: image,
                  width: 25,
                  height: 25,
                  placeholder: (context, url) {
                    return Container(
                      decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(10.r)),
                      width: 25,
                      height: 25,
                    );
                  },
                ),
                /* Image.asset(
                  image,
                  height: 25,
                  width: 25,
                ), */
                const SizedBox(height: 10),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                          fontSize: 11.sp, color: AppTheme.colorBlackTitle)
                      .pfRegular,
                ),
                SizedBox(height: 11.h),
              ],
            ),
          ),
          if (isDev ?? false)
            Positioned(
              right: 0,
              child: SvgPicture.asset(
                'assets/svg/study/developing.svg',
                height: 15,
              ),
            )
          else if (!isOpen!)
            Positioned(
              right: 0,
              child: SvgPicture.asset(
                'assets/png/function/ic_noopen.svg', // Assuming you have a different asset for open state
                height: 15,
              ),
            ),
        ],
      ),
    );
  }
}
