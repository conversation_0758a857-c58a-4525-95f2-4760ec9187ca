// 自定义倍速下拉框

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/common/dialog/input_dialog.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/Utils/extensions.dart';

class CustomDropdown extends StatefulWidget {
  final double? value;
  final Function(double)? onChange; //下拉框值改变

  const CustomDropdown({super.key, this.onChange, this.value = 1.0});

  @override
  _CustomDropdownState createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  final List<double> _speedList = [0.75, 1.0, 1.25, 1.5, 1.75, 2, 3];
  double _selectedItem = 1.0; // 当前选中的项目
  OverlayEntry? _overlayEntry; // 下拉框的浮层
  final LayerLink _layerLink =
      LayerLink(); // 用于将 CompositedTransformTarget 和 Follower 链接在一起
  bool _isDropdownOpen = false; // 下拉框是否打开

  // 切换下拉框的显示和隐藏
  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _closeDropdown(); // 如果打开则关闭
    } else {
      _openDropdown(); // 如果关闭则打开
    }
  }

  // 打开下拉框
  void _openDropdown() {
    _overlayEntry = _createOverlayEntry(); // 创建浮层
    Overlay.of(context).insert(_overlayEntry!); // 插入浮层
    _isDropdownOpen = true; // 更新状态
  }

  // 关闭下拉框
  void _closeDropdown() {
    _overlayEntry?.remove(); // 移除浮层
    _overlayEntry = null;
    _isDropdownOpen = false; // 更新状态
  }

  // 创建下拉框的浮层内容
  OverlayEntry _createOverlayEntry() {
    // 获取触发按钮的大小，用于定位下拉框
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Positioned(
          width: 100.w, // 下拉框宽度与按钮一致
          child: CompositedTransformFollower(
              link: _layerLink, // 关联到目标按钮
              offset: Offset(-30.w, size.height + 5), // 设置下拉框的偏移位置
              child: Material(
                borderRadius: BorderRadius.circular(16.r),
                child: SizedBox(
                    height: 200.h,
                    child: MediaQuery.removePadding(
                      removeTop: true,
                      context: context,
                      child: ListView.builder(
                          itemCount: _speedList.length,
                          itemBuilder: (_, index) {
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _selectedItem = _speedList[index];
                                });
                                if (widget.onChange != null) {
                                  widget.onChange!(_selectedItem);
                                }
                                _closeDropdown();
                              },
                              child: Container(
                                alignment: Alignment.center,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20.w, vertical: 9.h),
                                child: Text('${_speedList[index]}x',
                                    style: TextStyle(
                                        color:
                                            _selectedItem == _speedList[index]
                                                ? const Color(0xFF0054FF)
                                                : const Color(0xFF606266),
                                        fontSize: 13.sp)),
                              ),
                            );
                          }),
                    )),
              ))),
    );
  }

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.value ?? 1.0;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink, // 关联目标，用于对齐浮层
      child: GestureDetector(
        onLongPress: _toggleDropdown, // 点击时切换下拉框的显示状态
        onTap: () => _showBottomSheet(),
        child: Column(
          children: [
            SizedBox(
                height: 21,
                child: _selectedItem == 1
                    ? Image.asset('assets/png/xiaoxin/audio_speed.png')
                    : Text(
                        '${_selectedItem}x',
                        style: TextStyle(
                            color: const Color(0xFF4A4B54),
                            fontSize: 13.sp,
                            fontWeight: FontWeight.w600),
                      )),
            const SizedBox(height: 5),
            Text('倍速',
                style:
                    TextStyle(color: const Color(0xFF737373), fontSize: 10.sp)),
          ],
        ),
      ),
    );
  }

  void _showBottomSheet() {
    showModalBottomSheet(
        isDismissible: false,
        context: context,
        builder: (_) {
          return ClipRRect(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r)),
            child: Container(
              color: Colors.white,
              width: ScreenUtil().screenWidth,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 35.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Stack(
                        children: [
                          Positioned(
                              top: 14.h,
                              child: Image.asset(
                                  'assets/png/xiaoxin/sort_bac.png',
                                  width: 125.w)),
                          Text('倍速播放',
                              style: TextStyle(
                                      color: const Color(0xFF333333),
                                      fontSize: 18.sp)
                                  .pfSemiBold)
                        ],
                      ),
                      IconButton(
                          onPressed: () => NavigatorUtils.pop(_),
                          icon: Icon(Icons.close))
                    ],
                  ),
                  SizedBox(height: 14.h),
                  Expanded(
                    child: ListView.builder(
                        itemCount: _speedList.length,
                        itemBuilder: (c, index) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedItem = _speedList[index];
                              });
                              if (widget.onChange != null) {
                                widget.onChange!(_selectedItem);
                              }
                              NavigatorUtils.pop(context);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 15.w, vertical: 15.w),
                              decoration: const BoxDecoration(
                                  border: Border(
                                      bottom: BorderSide(
                                          color: Color(0xFFE6E6E6),
                                          width: 0.5))),
                              child: Text(
                                '${_speedList[index]}x',
                                style: TextStyle(
                                    color: _selectedItem == _speedList[index]
                                        ? const Color(0xFF0054FF)
                                        : const Color(0xFF606266),
                                    fontSize: 15.sp),
                              ),
                            ),
                          );
                        }),
                  )
                ],
              ),
            ),
          );
        });
  }
}
