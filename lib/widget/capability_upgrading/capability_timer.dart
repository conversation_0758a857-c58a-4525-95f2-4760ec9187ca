import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/manager/close_timer_manager.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class CapabilityTimer extends StatefulWidget {
  const CapabilityTimer({super.key});

  @override
  State<CapabilityTimer> createState() => _CapabilityTimerState();
}

class _CapabilityTimerState extends State<CapabilityTimer> {
  final List<int> _timers = [
    0,
    1 * 60,
    5 * 60,
    10 * 60,
    15 * 60,
    20 * 60,
    25 * 60,
    30 * 60,
    35 * 60,
    40 * 60
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      CloseTimerManager.onChange = (value) {
        setState(() {});
      };
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  String _formatTime() {
    int minutes = CloseTimerManager.seconds ~/ 60; // 计算分钟数
    int remainingSeconds = CloseTimerManager.seconds % 60; // 计算剩余的秒数

    // 格式化成 mm:ss 格式
    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = remainingSeconds.toString().padLeft(2, '0');

    return "$formattedMinutes:$formattedSeconds";
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showBottomSheet(),
      child: Column(
        children: [
          SizedBox(
              height: 21,
              child: Image.asset('assets/png/xiaoxin/audio_timer.png')),
          const SizedBox(height: 5),
          Text(CloseTimerManager.selected > 0 ? _formatTime() : '定时',
              style:
                  TextStyle(color: const Color(0xFF737373), fontSize: 10.sp)),
        ],
      ),
    );
  }

  void _showBottomSheet() {
    showModalBottomSheet(
        isDismissible: false,
        context: context,
        builder: (_) {
          return ClipRRect(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r)),
            child: Container(
              color: Colors.white,
              width: ScreenUtil().screenWidth,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 35.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Stack(
                        children: [
                          Positioned(
                              top: 14.h,
                              child: Image.asset(
                                  'assets/png/xiaoxin/sort_bac.png',
                                  width: 125.w)),
                          Text('定时播放',
                              style: TextStyle(
                                color: const Color(0xFF333333),
                                fontSize: 18.sp,
                              ).pfSemiBold)
                        ],
                      ),
                      IconButton(
                          onPressed: () => NavigatorUtils.pop(_),
                          icon: const Icon(Icons.close))
                    ],
                  ),
                  SizedBox(height: 14.h),
                  Expanded(
                    child: ListView.builder(
                        itemCount: _timers.length,
                        itemBuilder: (c, index) {
                          return GestureDetector(
                            onTap: () {
                              CloseTimerManager.cancel();
                              CloseTimerManager.seconds = _timers[index];
                              CloseTimerManager.selected = _timers[index];
                              if (_timers[index] > 0) {
                                CloseTimerManager.start();
                              }
                              setState(() {});
                              NavigatorUtils.pop(context);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 15.w, vertical: 15.w),
                              decoration: const BoxDecoration(
                                  border: Border(
                                      bottom: BorderSide(
                                          color: Color(0xFFE6E6E6),
                                          width: 0.5))),
                              child: Text(
                                _timers[index] == 0
                                    ? '关闭定时'
                                    : '${(_timers[index] / 60).truncate()}分钟后',
                                style: TextStyle(
                                    color: CloseTimerManager.selected ==
                                            _timers[index]
                                        ? const Color(0xFF0054FF)
                                        : const Color(0xFF606266),
                                    fontSize: 15.sp),
                              ),
                            ),
                          );
                        }),
                  )
                ],
              ),
            ),
          );
        });
  }
}
