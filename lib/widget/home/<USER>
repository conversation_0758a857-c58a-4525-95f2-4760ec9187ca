import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';

class HomeTabItem extends StatelessWidget {
  final String title;
  final bool isSelect;
  final VoidCallback onTap;
  const HomeTabItem({
    super.key,
    required this.title,
    required this.isSelect,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: SizedBox(
        height: 40.h,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              title,
              style: isSelect
                  ? TextStyle(color: const Color(0xFF222222), fontSize: 15.sp)
                      .pfSemiBold
                  : TextStyle(color: const Color(0xFF999999), fontSize: 15.sp)
                      .pfRegular,
            ),
            SizedBox(height: 4.h),
            if (isSelect)
              Column(
                children: [
                  Container(
                    width: 24.w,
                    height: 4.h,
                    decoration: BoxDecoration(
                        color: const Color(0xFF0054FF),
                        borderRadius: BorderRadius.circular(2.r)),
                  ),
                  Container(
                    width: 24.w,
                    height: 4.h,
                    decoration: BoxDecoration(
                        color: const Color(0xFF0054FF).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(2.r)),
                  ),
                ],
              )
          ],
        ),
      ),
    );
  }
}
