import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class SingleSelectionDialog extends StatefulWidget {
  final String title; // 外部传入的标题
  final List<String> options; // 外部传入的选项列表
  final bool showSearch; // 外部参数决定是否显示搜索框
  final Function(String) onSelectedOptionChanged; // 单选回调函数

  SingleSelectionDialog({
    required this.title,
    required this.options,
    required this.showSearch,
    required this.onSelectedOptionChanged,
  });

  @override
  _SingleSelectionDialogState createState() => _SingleSelectionDialogState();
}

class _SingleSelectionDialogState extends State<SingleSelectionDialog> {
  String selectedOption = ''; // 存储当前选中的选项
  String searchQuery = ''; // 搜索框输入的内容

  @override
  Widget build(BuildContext context) {
    // 过滤选项列表（如果启用搜索功能）
    List<String> filteredOptions = widget.showSearch
        ? widget.options
            .where((option) => option.contains(searchQuery))
            .toList()
        : widget.options;

    return LayoutBuilder(
      builder: (context, constraints) {
        double dialogHeight = constraints.maxHeight;

        return Dialog(
          insetPadding: EdgeInsets.all(0),
          backgroundColor: Colors.transparent,
          child: Container(
            height: dialogHeight,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
            ),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标题和关闭按钮
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            widget.title,
                            style: TextStyle(fontSize: 17, color: Colors.black)
                                .pfSemiBold,
                          ),
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              NavigatorUtils.pop(context);
                            },
                            child: Container(
                              width: 30,
                              height: 30,
                              alignment: Alignment.center,
                              child: SvgPicture.asset(
                                'assets/svg/mine/close_icon.svg',
                                width: 12,
                                height: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24),

                      // 搜索框 (根据 showSearch 参数决定是否显示)
                      if (widget.showSearch)
                        TextField(
                          onChanged: (value) {
                            setState(() {
                              searchQuery = value;
                            });
                          },
                          decoration: InputDecoration(
                            hintText: '请输入搜索内容',
                            hintStyle: TextStyle(
                              color: Color(0xFFCCCCCC),
                              fontSize: 15,
                            ).pfMedium,
                            contentPadding: EdgeInsets.symmetric(
                                vertical: 0, horizontal: 16),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Color(0xFF333333),
                                width: 1.5,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Color(0xFF333333),
                                width: 1.5,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Color(0xFF333333),
                                width: 1.5,
                              ),
                            ),
                            suffixIcon: Icon(Icons.search),
                          ),
                          style: TextStyle(
                            fontSize: 15,
                          ),
                        ),
                      SizedBox(height: 16),

                      // 列表实现单选
                      Expanded(
                        child: Padding(
                          padding:
                              const EdgeInsets.only(bottom: 70.0), // 底部留出空间
                          child: ListView.builder(
                            itemCount: filteredOptions.length,
                            itemBuilder: (context, index) {
                              String option = filteredOptions[index];
                              bool isSelected = selectedOption == option;
                              return ListTile(
                                title: Text(
                                  option,
                                  style: TextStyle(
                                          color: isSelected
                                              ? AppTheme.colorBlue
                                              : Color(0xFF222222),
                                          fontSize: 15)
                                      .pfRegular,
                                ),
                                trailing: isSelected
                                    ? Icon(Icons.check,
                                        color: AppTheme.colorBlue)
                                    : null,
                                onTap: () {
                                  setState(() {
                                    selectedOption = option;
                                  });
                                },
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // 确定按钮
                Positioned(
                  bottom: ScreenUtil().bottomBarHeight,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                    color: Colors.white,
                    child: ElevatedButton(
                      onPressed: () {
                        // 回调选中的单项
                        widget.onSelectedOptionChanged(selectedOption);
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextStyle(color: Colors.white, fontSize: 16)
                            .pfMedium,
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.colorBlue,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        minimumSize: Size(double.infinity, 50),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
