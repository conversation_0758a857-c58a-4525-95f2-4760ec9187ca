import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/model/study/department_tree_model.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class DepartTreeDialog extends StatefulWidget {
  final List datas; //部门数据
  final DepartmentModel? selected; //当前选择部门数据
  final void Function()? onDismiss;
  final void Function(DepartmentModel?)? onDepartChange;

  const DepartTreeDialog({
    super.key,
    required this.datas,
    this.selected,
    this.onDismiss,
    this.onDepartChange,
  });

  @override
  State<DepartTreeDialog> createState() => _DepartTreeDialogState();
}

class _DepartTreeDialogState extends State<DepartTreeDialog> {
  List<DepartmentTreeModel> departDatas = [];
  Map<int, bool> expandedNodes = {};
  DepartmentModel? selectedNode;
  List<DepartmentTreeModel> filteredDepartDatas = [];
  String searchQuery = '';
  final Map<int, GlobalKey> nodeKeys = {};

  get isSearching => searchQuery.isNotEmpty;

  late StreamSubscription<bool> keyboardSubscription;

  bool keyBoardHidden = true;

  void _keyboardListener() {
    var keyboardVisibilityController = KeyboardVisibilityController();
    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      setState(() {
        keyBoardHidden = !visible;
      });
    });
  }

  List<DepartmentTreeModel> convertToTreeNode(List departments,
      {int level = 0}) {
    return departments.map<DepartmentTreeModel>((data) {
      DepartmentModel department = DepartmentModel.formJson(data['department']);
      List childrens = data['children'] ?? [];
      List<DepartmentTreeModel> children =
          convertToTreeNode(childrens, level: level + 1);
      return DepartmentTreeModel(
          department: department, children: children, level: level);
    }).toList();
  }

  void _filterDepartments(String query) {
    setState(() {
      searchQuery = query;
      if (query.isEmpty) {
        filteredDepartDatas = convertToTreeNode(widget.datas);
      } else {
        filteredDepartDatas =
            _searchDepartments(convertToTreeNode(widget.datas), query);
      }
    });
  }

  List<DepartmentTreeModel> _searchDepartments(
      List<DepartmentTreeModel> nodes, String query) {
    List<DepartmentTreeModel> result = [];
    for (var node in nodes) {
      String name = node.department.name ?? '';
      List<DepartmentTreeModel> childrens = node.children ?? [];
      if (name.toLowerCase().contains(query.toLowerCase())) {
        if (childrens.isNotEmpty) {
          for (var i = 0; i < childrens.length; i++) {
            int index = childrens
                .indexWhere((e) => !(e.department.name ?? '').contains(query));
            if (index != -1) {
              childrens.removeAt(index);
            }
          }
        }
        result.add(node);
      } else {
        List<DepartmentTreeModel> childrenResult =
            _searchDepartments(node.children ?? [], query);
        if (childrenResult.isNotEmpty) {
          result.add(DepartmentTreeModel(
              department: node.department,
              children: childrenResult,
              level: node.level));
        }
      }
    }
    return result;
  }

  bool _expandToSelectedNode(
      List<DepartmentTreeModel> nodes, DepartmentModel selected) {
    for (var node in nodes) {
      if (node.department.id != null && node.department.id == selected.id) {
        if (node.department.parent_id != null) {
          setState(() {
            expandedNodes[node.department.parent_id!] = true;
            selectedNode = node.department;
          });
          return true;
        }
      }
      if (_expandToSelectedNode(node.children ?? [], selected)) {
        if (node.department.parent_id != null) {
          setState(() {
            expandedNodes[node.department.parent_id!] = true;
          });
          return true;
        }
      }
    }
    return false;
  }

  void _scrollToSelectedNode() {
    if (selectedNode != null) {
      final key = nodeKeys[selectedNode!.id];
      if (key != null && key.currentContext != null) {
        Scrollable.ensureVisible(key.currentContext!,
            duration: const Duration(milliseconds: 100),
            alignment: 0.5,
            curve: Curves.easeInOut);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _keyboardListener();
    departDatas = convertToTreeNode(widget.datas);
    filteredDepartDatas = departDatas;
    selectedNode = widget.selected;
    if (selectedNode != null) {
      _expandToSelectedNode(departDatas, selectedNode!);
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedNode();
    });
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop) {
            widget.onDismiss!();
            Navigator.pop(context);
          }
        },
        child: LayoutBuilder(
          builder: (context, constraints) {
            double dialogHeight = constraints.maxHeight;
            return Dialog(
              insetPadding: const EdgeInsets.all(0),
              backgroundColor: Colors.transparent,
              child: Container(
                width: ScreenUtil().screenWidth,
                height: dialogHeight,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                ),
                child: Stack(
                  children: [
                    Padding(
                      padding: EdgeInsets.fromLTRB(
                          24.w, 24.h, 24.w, ScreenUtil().bottomBarHeight),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 标题和关闭按钮
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "选择部门",
                                style:
                                    TextStyle(fontSize: 17, color: Colors.black)
                                        .pfSemiBold,
                              ),
                              GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  widget.onDismiss!();
                                  NavigatorUtils.pop(context);
                                },
                                child: Container(
                                  width: 30,
                                  height: 30,
                                  alignment: Alignment.center,
                                  child: SvgPicture.asset(
                                    'assets/svg/mine/close_icon.svg',
                                    width: 12,
                                    height: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 16.h),
                          TextField(
                            onChanged: _filterDepartments,
                            decoration: InputDecoration(
                              hintText: '请输入搜索内容',
                              hintStyle: TextStyle(
                                color: Color(0xFFCCCCCC),
                                fontSize: 15,
                              ).pfMedium,
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 0, horizontal: 16),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: Color(0xFF333333),
                                  width: 1.5,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: Color(0xFF333333),
                                  width: 1.5,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(
                                  color: Color(0xFF333333),
                                  width: 1.5,
                                ),
                              ),
                              suffixIcon: Icon(Icons.search),
                            ),
                            style: TextStyle(
                              fontSize: 15,
                            ),
                          ),
                          SizedBox(height: 16.h),
                          Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: filteredDepartDatas
                                    .map((data) => _buildTreeNode(data))
                                    .toList(),
                              ),
                            ),
                          ),
                          if (keyBoardHidden) SizedBox(height: 16.h),
                          if (keyBoardHidden)
                            Row(
                              children: [
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () {
                                      widget.onDismiss!();
                                      Navigator.pop(context);
                                    },
                                    child: Container(
                                      height: 44.h,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFECECEC),
                                        borderRadius:
                                            BorderRadius.circular(16.r),
                                      ),
                                      child: Text(
                                        '取消',
                                        style: TextStyle(
                                          color: const Color(0xFFB3B3B3),
                                          fontSize: 15.sp,
                                        ).pfMedium,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 12.w),
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () {
                                      widget.onDismiss!();
                                      widget.onDepartChange!(selectedNode);
                                      Navigator.pop(context);
                                    },
                                    child: Container(
                                      height: 44.h,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(16.r),
                                        color: const Color(0xFF0054FF),
                                      ),
                                      child: Text(
                                        '确定',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 15.sp,
                                        ).pfMedium,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            );
          },
        ));
  }

  Widget _buildTreeNode(DepartmentTreeModel node) {
    String name = node.department.name ?? '';
    List<DepartmentTreeModel> childrens = node.children ?? [];
    bool isExpanded = expandedNodes[node.department.id] ?? false;
    bool isSelected = selectedNode?.id == node.department.id;
    final key =
        nodeKeys.putIfAbsent(node.department.id ?? 0, () => GlobalKey());

    return Container(
      key: key,
      padding: EdgeInsets.only(left: node.level * 10.0),
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(
                  color: node.level == 0
                      ? AppTheme.colorDivider
                      : Colors.transparent))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                selectedNode = node.department;
              });
              print('点击了部门: $name');
            },
            child: Row(
              children: [
                !isSearching && childrens.isNotEmpty
                    ? GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          setState(() {
                            if (childrens.isNotEmpty) {
                              expandedNodes[node.department.id!] = !isExpanded;
                            }
                          });
                        },
                        child: Container(
                          alignment: Alignment.center,
                          width: 30,
                          height: 30,
                          child: Image.asset(
                            isExpanded
                                ? 'assets/png/xiaoxin/menu_close.png'
                                : 'assets/png/xiaoxin/menu_open.png',
                            width: 15,
                            height: 15,
                          ),
                        ),
                      )
                    : const SizedBox(height: 30, width: 30),
                Expanded(
                  flex: 5,
                  child: Container(
                    padding: EdgeInsetsDirectional.symmetric(vertical: 12.h),
                    child: Text(
                      name,
                      style: TextStyle(
                        fontSize: 14,
                        color: isSelected ? AppTheme.colorBlue : Colors.black,
                      ).pfMedium,
                    ),
                  ),
                ),
                if (isSelected) const Expanded(child: SizedBox()),
                if (isSelected)
                  SvgPicture.asset(
                    'assets/svg/mine/profile/check.svg',
                    width: 20,
                    height: 20,
                  )
              ],
            ),
          ),
          if (isSearching && childrens.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children:
                  childrens.map((child) => _buildTreeNode(child)).toList(),
            ),
          if (isExpanded)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children:
                  childrens.map((child) => _buildTreeNode(child)).toList(),
            ),
        ],
      ),
    );
  }
}
