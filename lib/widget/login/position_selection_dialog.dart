import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';

class PositionSelectionDialog extends StatefulWidget {
  final Function(List<String>) onSelectedPositionsChanged; // 回调函数
  final List<String> options;
  PositionSelectionDialog(
      {required this.onSelectedPositionsChanged, required this.options});

  @override
  _PositionSelectionDialogState createState() =>
      _PositionSelectionDialogState();
}

class _PositionSelectionDialogState extends State<PositionSelectionDialog> {
  List<String> positions = [];
  List<String> selectedPositions = [];
  String searchQuery = '';

  @override
  void initState() {
    super.initState();
    positions = widget.options;
  }

  @override
  Widget build(BuildContext context) {
    // 过滤职位列表
    List<String> filteredPositions = positions.where((position) {
      return position.contains(searchQuery);
    }).toList();

    return LayoutBuilder(
      builder: (context, constraints) {
        double dialogHeight = constraints.maxHeight; // 设置为屏幕高度的 0.7

        return Dialog(
          insetPadding: EdgeInsets.all(0),
          backgroundColor: Colors.transparent,
          child: Container(
            height: dialogHeight, // 限制 Dialog 高度
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius:
                  BorderRadius.vertical(top: Radius.circular(24)), // 顶部圆角
            ),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标签和关闭按钮
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Stack(
                            alignment: Alignment.center, // 使文本居中对齐
                            children: [
                              SvgPicture.asset(
                                'assets/svg/mine/color_emphasize.svg', // 背景SVG图片
                                width: 87,
                                height: 20,
                              ),
                              Text(
                                '请选择岗位',
                                style: TextStyle(
                                        fontSize: 17,
                                        color:
                                            Colors.black // 设置文本颜色，确保能与背景颜色区分开
                                        )
                                    .pfSemiBold,
                              ),
                            ],
                          ),
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              width: 30,
                              height: 30,
                              alignment: Alignment.center,
                              child: SvgPicture.asset(
                                'assets/svg/mine/close_icon.svg', // 关闭按钮SVG
                                width: 12,
                                height: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '如无对应岗位可选普通职员，审批后联系客服修改。',
                        style: TextStyle(
                                color: const Color(0xFFFF9000), fontSize: 13.sp)
                            .pfMedium,
                      ),
                      SizedBox(height: 14),
                      // 搜索框
                      TextField(
                        onChanged: (value) {
                          setState(() {
                            searchQuery = value;
                          });
                        },
                        decoration: InputDecoration(
                          hintText: '请输入搜索内容',
                          hintStyle: TextStyle(
                            color: Color(0xFFCCCCCC), // 设置 placeholder 的颜色
                            fontSize: 15, // placeholder 的字体大小
                          ).pfMedium,
                          contentPadding: EdgeInsets.symmetric(
                              vertical: 0, horizontal: 16), // 设置内边距
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12), // 圆角边框
                            borderSide: BorderSide(
                              color: Color(0xFF333333), // 边框颜色
                              width: 1.5, // 边框宽度
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Color(0xFF333333), // 边框颜色
                              width: 1.5, // 边框宽度
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: Color(0xFF333333), // 边框颜色
                              width: 1.5, // 边框宽度
                            ),
                          ),
                          suffixIcon: Icon(Icons.search),
                        ),
                        style: TextStyle(
                          fontSize: 15, // 输入文本的字体大小
                        ),
                      ),
                      SizedBox(height: 16),
                      // 已选职位显示，使用 Wrap 换行
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: selectedPositions.map((position) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedPositions.remove(position);
                              });
                            },
                            child: Stack(
                              clipBehavior: Clip.none, // 允许关闭按钮超出背景边界
                              children: [
                                // 背景和标签
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: Color(0xFFEFF3FD), // 背景颜色
                                    borderRadius:
                                        BorderRadius.circular(8), // 8个像素的圆角
                                  ),
                                  child: Text(
                                    position,
                                    style: TextStyle(
                                      color: Colors.black, // 文本颜色，根据需求调整
                                    ).pfRegular,
                                  ),
                                ),
                                // 右上角的关闭按钮 (重叠1/4)
                                Positioned(
                                  top: -6, // 使关闭按钮与背景重叠1/4
                                  right: -6, // 使关闭按钮与背景重叠1/4
                                  child: SvgPicture.asset(
                                    'assets/svg/mine/selected_position_close.svg', // 关闭按钮SVG
                                    width: 14,
                                    height: 14,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                      SizedBox(height: 16),
                      // 滚动职位列表
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              bottom: 70.0), // 确保底部预留足够的空间以防止被按钮遮挡
                          child: ListView.builder(
                            itemCount: filteredPositions.length,
                            itemBuilder: (context, index) {
                              String position = filteredPositions[index];
                              bool isSelected =
                                  selectedPositions.contains(position);
                              return ListTile(
                                title: Text(
                                  position,
                                  style: TextStyle(
                                    color: isSelected
                                        ? AppTheme.colorBlue
                                        : Color(0xFF222222),
                                    fontSize: 15,
                                  ).pfRegular,
                                ),
                                trailing: isSelected
                                    ? Icon(Icons.check,
                                        color: AppTheme.colorBlue)
                                    : null,
                                onTap: () {
                                  setState(() {
                                    if (isSelected) {
                                      selectedPositions.remove(position);
                                    } else {
                                      selectedPositions.add(position);
                                    }
                                  });
                                },
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // 确定按钮
                Positioned(
                  bottom: ScreenUtil().bottomBarHeight,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                    color: Colors.white,
                    child: ElevatedButton(
                      onPressed: () {
                        // 回调选择的岗位
                        widget.onSelectedPositionsChanged(selectedPositions);
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        '确定',
                        style: TextStyle(color: Colors.white, fontSize: 16)
                            .pfMedium,
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.colorBlue,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        minimumSize: Size(double.infinity, 50),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
