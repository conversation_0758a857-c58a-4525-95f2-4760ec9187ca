import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import '../../common/page/webview_screen_page.dart';

class BottomPrivacySection extends StatelessWidget {
  final bool isAgreed;
  final VoidCallback onAgreementChanged;
  final VoidCallback onSubmitForm;
  final String? submitText;

  const BottomPrivacySection(
      {Key? key,
      required this.isAgreed,
      required this.onAgreementChanged,
      required this.onSubmitForm,
      this.submitText})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // height: 126,
      padding: EdgeInsets.symmetric(horizontal: 11.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.05),
            spreadRadius: 0,
            blurRadius: 3.5,
            offset: Offset(0, -0.5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 15.h),
            child: Row(
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: onAgreementChanged,
                  child: Container(
                    alignment: Alignment.center,
                    width: 30,
                    height: 30,
                    child: Image.asset(
                      isAgreed
                          ? 'assets/png/check_selected.png'
                          : 'assets/png/check_unselected.png',
                      fit: BoxFit.cover,
                      width: 13,
                      height: 13,
                    ),
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      text: '我已阅读并同意 ',
                      style:
                          TextStyle(color: Color(0xFF808080), fontSize: 12.sp)
                              .pfRegular,
                      children: <TextSpan>[
                        TextSpan(
                          text: '《用户协议》',
                          style: TextStyle(
                                  color: AppTheme.colorBlue, fontSize: 12.sp)
                              .pfRegular,
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              NavigatorUtils.push(
                                context,
                                CommonRouter.webview,
                                arguments: {
                                  'title': '用户协议',
                                  'url':
                                      'https://web.xtjstatic.cn/agreement/protocol.html',
                                  'keep_alive': false,
                                },
                              );
                            },
                        ),
                        TextSpan(
                          text: ' 和 ',
                          style: TextStyle(
                                  color: Color(0xFF808080), fontSize: 12.sp)
                              .pfRegular,
                        ),
                        TextSpan(
                          text: '《隐私政策》',
                          style: TextStyle(
                                  color: AppTheme.colorBlue, fontSize: 12.sp)
                              .pfRegular,
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              NavigatorUtils.push(
                                context,
                                CommonRouter.webview,
                                arguments: {
                                  'title': '隐私政策',
                                  'url':
                                      'https://web.xtjstatic.cn/agreement/privacyAgreement.htm',
                                  'keep_alive': false,
                                },
                              );
                            },
                        ),
                        // TextSpan(
                        //   text: '并同意协议内容',
                        //   style: TextStyle(
                        //       color: Color(0xFF808080), fontSize: 12.sp),
                        // ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(
            width: double.infinity,
            height: 49.h,
            child: ElevatedButton(
              onPressed: isAgreed ? onSubmitForm : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: isAgreed ? AppTheme.colorBlue : Colors.grey,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16.r),
                ),
              ),
              child: Text(
                submitText ?? '提交审批',
                style: TextStyle(color: Colors.white, fontSize: 16.sp).pfMedium,
              ),
            ),
          ),
          SizedBox(height: ScreenUtil().bottomBarHeight)
        ],
      ),
    );
  }
}
