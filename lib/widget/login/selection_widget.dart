import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';

import '../../common/app_theme.dart';

class SelectionWidget extends StatelessWidget {
  final String title;
  final String description;
  final String clickableText;
  final String placeholder;
  final Map<String, List<dynamic>> selectedPositionsMap;
  final VoidCallback onClickableTextTap;
  final VoidCallback onTap; // 新增的回调，用于触发弹框
  final bool? enable;

  const SelectionWidget({
    Key? key,
    required this.title,
    required this.description,
    required this.clickableText,
    required this.placeholder,
    required this.selectedPositionsMap,
    required this.onClickableTextTap,
    required this.onTap,
    this.enable = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    int clickableStartIndex = description.indexOf(clickableText);
    int clickableEndIndex = clickableStartIndex + clickableText.length;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          title == '选择岗位'
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$title ',
                      style: AppTheme.getTextStyle(
                              baseSize: 17.sp, color: AppTheme.colorBlack2)
                          .pfSemiBold,
                    ),
                    SizedBox(height: 7.5.h),
                    Text(
                      '(岗位涉及功能权限，榜单统计等，请按实际情况选择)',
                      style: TextStyle(
                              color: const Color(0xFFFF9000), fontSize: 12.sp)
                          .pfMedium,
                    ),
                  ],
                )
              : RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: '$title ',
                        style: AppTheme.getTextStyle(
                                baseSize: 17.sp, color: AppTheme.colorBlack2)
                            .pfSemiBold,
                      ),
                      if (description.isNotEmpty)
                        TextSpan(
                          text:
                              ' (${description.substring(0, clickableStartIndex)}',
                          style: TextStyle(
                                  fontSize: 12.sp, color: Color(0xFF333333))
                              .pfRegular,
                        ),
                      if (clickableText.isNotEmpty)
                        TextSpan(
                          text: clickableText,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Color(0xFF0054FF),
                            decoration: TextDecoration.underline,
                          ).pfRegular,
                          recognizer: TapGestureRecognizer()
                            ..onTap = onClickableTextTap,
                        ),
                      if (description.isNotEmpty)
                        TextSpan(
                          text: '${description.substring(clickableEndIndex)})',
                          style: TextStyle(
                                  fontSize: 12.sp, color: Color(0xFF333333))
                              .pfRegular,
                        ),
                    ],
                  ),
                ),
          SizedBox(height: 16.h),
          // Placeholder or selected items
          GestureDetector(
            onTap: onTap, // Triggers the dialog selection logic
            child: Container(
              // padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      selectedPositionsMap[title]?.isNotEmpty == true
                          ? (title == '选择部门'
                              ? selectedPositionsMap[title]?.first['name']
                              : selectedPositionsMap[title]!
                                  .join('、')) // Show selected positions
                          : placeholder, // Show placeholder if nothing is selected
                      style: TextStyle(
                              color: selectedPositionsMap[title]?.isNotEmpty ==
                                      true
                                  ? Color(0xFF606266)
                                  : Color(0xFFBABABA),
                              fontSize: 15.sp // Grey if no selection
                              )
                          .pfRegular,
                      overflow:
                          TextOverflow.ellipsis, // Ensure text doesn't overflow
                    ),
                  ),
                  // Arrow icon
                  if (enable!)
                    SvgPicture.asset(
                      'assets/svg/mine/down_arrow.svg',
                      width: 13,
                      height: 13,
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
