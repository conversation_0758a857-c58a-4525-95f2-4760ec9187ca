import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:npemployee/common/app_theme.dart';

import '../../manager/mini_program_manager.dart';

class WeChatLoginDialog extends StatefulWidget {
  final String loginImg;
  final int statusCode;
  final Function onClose;
  final Function onRefresh;

  WeChatLoginDialog({
    required this.loginImg,
    required this.statusCode,
    required this.onClose,
    required this.onRefresh,
  });

  @override
  _WeChatLoginDialogState createState() => _WeChatLoginDialogState();
}

class _WeChatLoginDialogState extends State<WeChatLoginDialog> {
  late String _loginImg; // Track the current loginImg
  late int _currentStatusCode;

  @override
  void initState() {
    super.initState();
    _loginImg = widget.loginImg; // Initialize with the initial loginImg
    _currentStatusCode = widget.statusCode;

    // Listen to status changes from MiniProgramManager
    MiniProgramManager().onStatusChange = (newStatusCode) {
      if (_currentStatusCode != newStatusCode) {
        setState(() {
          _currentStatusCode = newStatusCode;
          if (_currentStatusCode == 203) {
            Navigator.of(context).pop(); // Close the dialog
            widget.onClose(); // Trigger the onClose callback to reset flags
          }
        });
      }
    };

  }

  @override
  Widget build(BuildContext context) {
    Uint8List imageBytes = base64Decode(_loginImg.split(',')[1]); // Use updated loginImg

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20), // Set the corner radius to 20
      ),
      backgroundColor: Colors.white, // Ensure Dialog background color is white
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white, // Set the background color of the Container to white
          borderRadius: BorderRadius.circular(20), // Apply the same 20px border radius
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader(),
                  _buildQRCode(imageBytes),
                  SizedBox(height: 20),
                  _buildStatusMessage(),
                ],
              ),
            ),
            if (_currentStatusCode == 204) _buildOverlay(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          "微信扫码登录",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.normal,
            color: AppTheme.colorPrimaryBlack,
          ),
        ),
        IconButton(
          icon: Image.asset(
            'assets/png/login/mini_program_close.png',
            width: 20,
            height: 20,
          ),
          onPressed: () {
            widget.onClose();
          },
        ),
      ],
    );
  }

  Widget _buildQRCode(Uint8List imageBytes) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Image.memory(imageBytes, width: 200, height: 200),
    );
  }

  Widget _buildStatusMessage() {
    String message = '';
    Widget icon = SizedBox.shrink(); // Default empty widget

    switch (_currentStatusCode) {
      case 201:
        message = "请用微信扫码登录";
        break;
      case 202:
        message = "扫码完成，需在手机上确认登录";
        icon = Padding(
          padding: const EdgeInsets.only(right: 10.0),
          child: Image.asset(
            'assets/png/login/mini_program_complete.png',
            width: 20,
            height: 20,
          ),
        );
        break;
      case 204:
        message = "登录码已过期，请点击刷新";
        break;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 40), // Add 40px padding at the bottom
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon,
          Text(
            message,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.normal,
              color: AppTheme.colorPrimaryBlack,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverlay() {
    return Positioned(
      child: Container(
        width: 240,
        height: 240,
        color: Colors.white.withOpacity(0.9),
        child: Center(
          child: IconButton(
            icon: Image.asset(
              'assets/png/login/mini_program_refresh.png',
              width: 80,
              height: 80,
            ),
            onPressed: () async {
              String? newLoginImg = await widget.onRefresh();
              if (newLoginImg != null) {
                setState(() {
                  _loginImg = newLoginImg; // Update the QR code
                  _currentStatusCode = 201; // Reset status code to not scanned
                });
              }
            },
          ),
        ),
      ),
    );
  }
}
