import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class TreeSelectionDialog extends StatefulWidget {
  final List departments;
  final void Function(Map)? onSelected;
  const TreeSelectionDialog(
      {super.key, required this.departments, this.onSelected});

  @override
  State<TreeSelectionDialog> createState() => _TreeSelectionDialogState();
}

class _TreeSelectionDialogState extends State<TreeSelectionDialog> {
  int? _selectedDepartmentId;
  Map? _selectedMap;
  List parentDeparts = [];
  List allDepartments = [];

  String searchQuery = ''; // 搜索框输入的内容

  List departs = [];

  late StreamSubscription<bool> keyboardSubscription;

  bool keyBoardHidden = true;

  void _keyboardListener() {
    var keyboardVisibilityController = KeyboardVisibilityController();
    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      setState(() {
        keyBoardHidden = !visible;
      });
    });
  }

  @override
  void initState() {
    super.initState();
    departs = widget.departments;
    allDepartments = widget.departments;
    initDepartments();
    _keyboardListener();
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    super.dispose();
  }

  void initDepartments() {
    for (var element in widget.departments) {
      element['department']['selected'] = false;
      if (element['children'].isNotEmpty) {
        for (var e1 in element['children']) {
          e1['department']['selected'] = false;
          if (e1['children'].isNotEmpty) {
            for (var e2 in e1['children']) {
              e2['department']['selected'] = false;
              if (e2['children'].isNotEmpty) {
                for (var e3 in e2['children']) {
                  e3['department']['selected'] = false;
                }
              }
            }
          }
        }
      }
    }
  }

  void _searchDepartments(String query) {
    if (query.isEmpty) {
      setState(() {
        departs = allDepartments;
      });
    } else {
      List result = [];
      void search(List departments, List parentChain) {
        for (var department in departments) {
          if (department['department']['name'].contains(query)) {
            String cascadeName = parentChain.isEmpty
                ? department['department']['name']
                : parentChain.map((e) => e['department']['name']).join(' > ') +
                    ' > ' +
                    department['department']['name'];
            result.add({
              'department': {
                'name': cascadeName,
                'id': department['department']['id'],
                'selected': department['department']['selected']
              },
              'children': department['children']
            });
          }
          if (department['children'].isNotEmpty) {
            search(department['children'], [...parentChain, department]);
          }
        }
      }

      search(allDepartments, []);
      setState(() {
        searchQuery = query;
        departs = result.isEmpty ? [] : result.toSet().toList(); // 去重
      });
    }
  }

  void _handleSwipeEnd(DragEndDetails details) {
    // Check the velocity of the drag to determine direction
    if (details.primaryVelocity != null) {
      if (details.primaryVelocity! > 0) {
        if (parentDeparts.isNotEmpty) {
          setState(() {
            departs = parentDeparts.removeLast();
          });
        }
      } else if (details.primaryVelocity! < 0) {
        // Swipe left: Go to next department
        print('object');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double dialogHeight = constraints.maxHeight;

        return Dialog(
          insetPadding: EdgeInsets.all(0),
          backgroundColor: Colors.transparent,
          child: GestureDetector(
            onHorizontalDragEnd: _handleSwipeEnd,
            child: Container(
              height: dialogHeight,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
              ),
              child: Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题和关闭按钮
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "选择部门",
                              style:
                                  TextStyle(fontSize: 17, color: Colors.black)
                                      .pfSemiBold,
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                NavigatorUtils.pop(context);
                              },
                              child: Container(
                                width: 30,
                                height: 30,
                                alignment: Alignment.center,
                                child: SvgPicture.asset(
                                  'assets/svg/mine/close_icon.svg',
                                  width: 12,
                                  height: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),

                        // 搜索框 (根据 showSearch 参数决定是否显示)
                        TextField(
                          onChanged: _searchDepartments,
                          decoration: InputDecoration(
                            hintText: '请输入搜索内容',
                            hintStyle: TextStyle(
                              color: Color(0xFFCCCCCC),
                              fontSize: 15,
                            ).pfMedium,
                            contentPadding: EdgeInsets.symmetric(
                                vertical: 0, horizontal: 16),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Color(0xFF333333),
                                width: 1.5,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Color(0xFF333333),
                                width: 1.5,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Color(0xFF333333),
                                width: 1.5,
                              ),
                            ),
                            suffixIcon: Icon(Icons.search),
                          ),
                          style: TextStyle(
                            fontSize: 15,
                          ),
                        ),
                        SizedBox(height: 6),

                        Expanded(
                          child: ListView.builder(
                              itemBuilder: _itemBuilder,
                              itemCount: departs.length),
                        ),
                        if (keyBoardHidden)
                          SizedBox(height: ScreenUtil().bottomBarHeight),
                      ],
                    ),
                  ),
                  // 确定按钮
                  if (keyBoardHidden)
                    Positioned(
                      bottom: ScreenUtil().bottomBarHeight,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                        color: Colors.white,
                        child: ElevatedButton(
                          onPressed: () {
                            // 回调选中的单项
                            if (_selectedMap != null) {
                              widget.onSelected!(_selectedMap!);
                            }
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            '确定',
                            style: TextStyle(color: Colors.white, fontSize: 16)
                                .pfMedium,
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.colorBlue,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            minimumSize: Size(double.infinity, 50),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _itemBuilder(BuildContext _, int index) {
    Map model = departs[index];
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (model['children'].isEmpty) {
          return;
        }
        setState(() {
          // departs = model['children'];
          _goToSubDepartment(model['children']);
        });
      },
      child: Container(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(model['department']['name'],
                    style: TextStyle(
                            color: const Color(0xFF181818), fontSize: 15.sp)
                        .pfRegular),
              ),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedDepartmentId = model['department']['id'];
                    _selectedMap = model['department'];
                    _refreshSelectStatus();
                  });
                },
                child: Container(
                  padding: EdgeInsets.all(8),
                  child: model['department']['selected']
                      ? Image.asset('assets/png/check_selected.png',
                          width: 18, height: 18)
                      : Image.asset('assets/png/check_unselected.png',
                          width: 18, height: 18),
                ),
              )
            ],
          )),
    );
  }

  void _goToSubDepartment(List subDepartments) {
    setState(() {
      parentDeparts.add(departs);
      departs = subDepartments;
    });
  }

  void _refreshSelectStatus() {
    for (var element in departs) {
      if (element['department']['id'] == _selectedDepartmentId) {
        element['department']['selected'] = true;
      } else {
        element['department']['selected'] = false;
      }

      if (element['children'].isNotEmpty) {
        for (var e1 in element['children']) {
          if (e1['department']['id'] == _selectedDepartmentId) {
            e1['department']['selected'] = true;
          } else {
            e1['department']['selected'] = false;
          }
          if (e1['children'].isNotEmpty) {
            for (var e2 in e1['children']) {
              if (e2['department']['id'] == _selectedDepartmentId) {
                e2['department']['selected'] = true;
              } else {
                e2['department']['selected'] = false;
              }
              if (e2['children'].isNotEmpty) {
                for (var e3 in e2['children']) {
                  if (e3['department']['id'] == _selectedDepartmentId) {
                    e3['department']['selected'] = true;
                  } else {
                    e3['department']['selected'] = false;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
