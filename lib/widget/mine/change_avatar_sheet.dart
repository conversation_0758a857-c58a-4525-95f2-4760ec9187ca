import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:npemployee/Utils/image_utils.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/provider/avatar_bloc/avatar_event.dart';
import 'package:npemployee/provider/tab_bloc/tab_event.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../common/app_theme.dart';
import '../../common/dialog/custom_dialog.dart';

class ChangeAvatarSheet extends StatelessWidget {
  const ChangeAvatarSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 16.h),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => _takePhotoAction(context),
            child: Container(
              alignment: Alignment.center,
              width: ScreenUtil().screenWidth - 32.w,
              height: 49.h,
              child: const Text('拍照'),
            ),
          ),
          const Divider(
            color: Color(0xFFE6E6E6),
            thickness: 0.5, // 设置高度为0.5
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => _galleryAction(context),
            child: Container(
              alignment: Alignment.center,
              width: ScreenUtil().screenWidth - 32.w,
              height: 49.h,
              child: const Text('相册'),
            ),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  void _takePhotoAction(BuildContext _) async {
    var cameraStatus = await Permission.camera.status;
    if (!cameraStatus.isGranted) {
      if (Platform.isAndroid) {
        showDialog(
          context: _,
          builder: (context) => CustomDialog(
            title: "提示",
            content: "拍照获取头像需要相机权限",
            cancelButtonText: "取消",
            confirmButtonText: "确定",
            cancelButtonColor: AppTheme.colorButtonGrey,
            confirmButtonColor: AppTheme.colorBlue,
            onCancel: () {
              Navigator.of(context).pop();
            },
            onConfirm: () async {
              Navigator.of(context).pop();
              _handleCameraPermission(_);
            },
          ),
        );
      } else {
        _handleCameraPermission(_);
      }
    } else {
      _handleCameraPermission(_);
    }
  }

  Future<void> _handleCameraPermission(BuildContext _) async {
    PermissionStatus status = await Permission.camera.status;
    // if (status != PermissionStatus.granted) {
    //   ToastUtils.show('请允许拍照权限，用于设置头像');
    //   // await Permission.camera.request();
    // }
    final ImagePicker _picker = ImagePicker();
    final pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80);
    if (pickedFile != null) {
      // NavigatorUtils.push(_, CommonRouter.imageEditPage,
      //     arguments: {'imagePath': pickedFile.path});
      _cropImage(pickedFile.path, _);
    }
  }

  void _galleryAction(BuildContext _) async {
    PermissionStatus status = await Permission.photos.status;
    if (status != PermissionStatus.granted) {
      await Permission.photos.request();
    }
    final ImagePicker _picker = ImagePicker();
    final pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80);
    if (pickedFile != null) {
      // NavigatorUtils.push(_, CommonRouter.imageEditPage,
      //     arguments: {'imagePath': pickedFile.path});
      _cropImage(pickedFile.path, _);
    }
  }

  Future<void> _cropImage(String imagePath, BuildContext c) async {
    final navigatorContext = Navigator.of(c);
    String path = await ImageUtils.cropImage(imagePath);

    debugPrint('-------------------- 图片文件路径 = $path');
    if (path.isNotEmpty) {
      EasyLoading.show();
      UserServiceProvider().uploadAvatar(path).then((value) {
        if (value?.code == 0) {
          UserServiceProvider().setAvatar(value?.data).then((value) {
            if (value?.code == 0) {
              EasyLoading.dismiss();
              BlocManager().tabBloc.add(TabChangeEvent(4));
              BlocManager().avatarBloc.add(AvatarChangeEvent(path));
              navigatorContext.popUntil(
                  ModalRoute.withName(MineRouter.personalSettingsPage));
            } else {
              EasyLoading.dismiss();
              EasyLoading.showError(value?.msg ?? '上传失败');
            }
          });
        } else {
          EasyLoading.dismiss();
          EasyLoading.showError(value?.msg ?? '上传失败');
        }
      });
    } else {
      navigatorContext
          .popUntil(ModalRoute.withName(MineRouter.personalSettingsPage));
    }
  }
}
