import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../common/app_theme.dart';
import 'employee_info_section.dart';

class DepartmentListModal extends StatelessWidget {
  final List<DepartmentModel> departments;
  final int selectedDepartmentIndex;
  final Function(int) onDepartmentSelected;
  final Offset position; // Pass the position where the modal should appear

  const DepartmentListModal({
    required this.departments,
    required this.selectedDepartmentIndex,
    required this.onDepartmentSelected,
    required this.position,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate the modal height based on the number of departments, ensuring each cell is 60 pixels
    final double modalHeight = departments.length * 60.0 + 20;

    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          // This GestureDetector covers the entire screen to capture taps outside the modal
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop(); // Dismiss the modal
            },
            child: Container(
              color: Colors.transparent, // Ensure the container is transparent
            ),
          ),
          Positioned(
            left: position.dx,
            top: position.dy,
            child: GestureDetector(
              onTap: () {}, // Prevents taps inside the modal from dismissing it
              child: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 2,
                      blurRadius: 10,
                    ),
                  ],
                ),
                width: MediaQuery.of(context).size.width * 0.6,
                height: modalHeight, // Set modal height
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: departments.asMap().entries.map((entry) {
                    int index = entry.key;
                    DepartmentModel department = entry.value;
                    bool isSelected = index == selectedDepartmentIndex;

                    return GestureDetector(
                      onTap: () {
                        Navigator.pop(context); // Close modal
                        onDepartmentSelected(index); // Update selected department
                      },
                      child: Container(
                        height: 60, // Set the height of each cell
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey[300]!, // Set the bottom border color
                              width: 0.5,
                            ),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Wrap the department name with Expanded to take the remaining available space
                            Expanded(
                              child: Text(
                                department.name,
                                style:
                                AppTheme.getTextStyle(
                                  baseSize: 15.0, // 传入基础字号
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                  color: isSelected ? AppTheme.colorBlue : Colors.black,
                                ),
                                overflow: TextOverflow.ellipsis, // Ensure text doesn't overflow
                              ),
                            ),
                            if (isSelected)
                              Padding(
                                padding: const EdgeInsets.only(left: 8.0), // Add some spacing from the text
                                child: SvgPicture.asset(
                                  'assets/svg/mine/profile/check.svg', // Replace with your correct SVG path
                                  width: 16,
                                  height: 16,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
