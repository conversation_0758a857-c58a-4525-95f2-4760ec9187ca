import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../common/app_theme.dart';

class ReusableTab extends StatelessWidget {
  final String svgPath;
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const ReusableTab({
    Key? key,
    required this.svgPath,
    required this.title,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double tabWidth = (screenWidth - 16 * 2 - 15) / 2; // Calculate tab width

    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            width: tabWidth,
            height: tabWidth * 195 / 494, // Ratio 494:195
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.white, // White background for the tab
            ),
            child: SvgPicture.asset(
              svgPath,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: 20,
            left: 18,
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          if (isSelected)
            Positioned(
              bottom: 0,
              left: 18,
              child: Container(
                width: 20,
                height: 4,
                decoration: BoxDecoration(
                  color: AppTheme.colorBlue,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
