import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluwx/fluwx.dart' as fluwx;
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/page/login/login_event.dart';

import '../model/mine/user_login_model.dart';
import '../network/result_data.dart';
import '../provider/user_service_provider.dart';
import '../service/wechat_service.dart';

class LoginData {
  final String loginId;
  late final String? loginImg;

  LoginData({required this.loginId, this.loginImg});
}

class MiniProgramManager {
  static final MiniProgramManager _singleton = MiniProgramManager._internal();
  Timer? _checkTimer; // 定时器
  bool _isChecking = false; // 防止重复请求
  late final WeChatService weChatService;
  // Callback for updating the dialog state
  Function(int)? onStatusChange;

  factory MiniProgramManager() {
    return _singleton;
  }

  MiniProgramManager._internal() {
    weChatService = WeChatService();
  }

  /// 获取login_id
  Future<LoginData?> getMiniProgramLoginId() async {
    ResultData? resultData = await UserServiceProvider().getMiniProgramCode();
    if (resultData != null && resultData.data != null) {
      String? loginId = resultData.data["login_id"];
      String? loginImg = resultData.data["login_img"];

      if (loginId != null) {
        return LoginData(loginId: loginId, loginImg: loginImg);
      }
    }
    return null;
  }

  /// 打开微信小程序
  Future<void> openWeChatMiniProgramWithLoginId(String loginId) async {
    // String userName = "gh_e4ce8f40af72"; // 新途径人小程序的原始ID
    // String path = "/pages/login/app?scene=$loginId"; // 根据login_id构建路径
    String userName = "gh_4f6ec9b9d6a2"; // 新途径在线小程序的原始ID
    String path = "pages/login/login?scene=$loginId"; // 根据login_id构建路径

    await weChatService.launchWeChatMiniProgram(
      userName: userName,
      path: path,
      type: 0, // 使用正式版小程序
    );
  }

  /// 定时检查是否扫码
  void startCheckingLoginStatus(String loginId) {
    if (_isChecking) return; // 防止重复开始
    _isChecking = true;

    _checkTimer = Timer.periodic(Duration(seconds: 1), (timer) async {
      try {
        ResultData? result =
            await UserServiceProvider().checkMiniProgramLogin(loginId);

        // 检查 result 是否为 null
        if (result == null) {
          print("Result is null, cannot check login status.");
          return;
        }

        // 直接使用 result.code
        String code = result.code;
        print("Received code: $code");
        if (code == "201") {
          print("用户未扫码");
          // 处理已扫码逻辑
          // if (onStatusChange != null) onStatusChange!(201);
        } else if (code == "202") {
          print("用户已扫码");
          // 处理已扫码逻辑
          if (onStatusChange != null) onStatusChange!(202);
        } else if (code == "203") {
          print("用户登录成功");
          stopChecking(); // 停止定时器
          if (onStatusChange != null) onStatusChange!(203);

          // 处理登录成功的逻辑
          if (result.data != null && result.data is Map<String, dynamic>) {
            UserLoginModel data =
                UserLoginModel.fromJson(result.data as Map<String, dynamic>);

            // 在这里处理 weChatLoginResponse，例如保存 token 或更新 UI
            GlobalPreferences().tokenValue = data.token;
            GlobalPreferences().userLoginModel = data;
            BlocManager().loginBloc.add(LoginSuccessEvent(true));
            print("用户信息: ${data.nickName}");
            // 其他逻辑
          }
        } else if (code == "204") {
          print("二维码已过期");
          stopChecking(); // 停止定时器
          if (onStatusChange != null) onStatusChange!(204);
          // 处理二维码过期逻辑
        } else {
          print("未知的响应码: $code");
        }

        // 处理 result.data
        if (result.data != null) {
          if (result.data is Map<String, dynamic>) {
            print("Data contains: ${result.data}");
            // 处理数据逻辑
          } else {
            print("Data is not a Map, it's a ${result.data.runtimeType}");
          }
        } else {
          print("Response data is null.");
        }
      } catch (e) {
        print("Error while checking login status: $e");
      }
    });
  }

  /// 停止检查扫码状态
  void stopChecking() {
    if (_checkTimer != null) {
      _checkTimer?.cancel();
      _isChecking = false;
    }
  }
}
