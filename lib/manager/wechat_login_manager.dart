import 'package:flutter/material.dart';
import '../../manager/mini_program_manager.dart';
import '../../widget/login/wechat_login_dialog.dart';

class WeChatLoginManager {
  bool _isDialogOpen = false;

  Future<void> handleWeChatLogin(BuildContext context) async {
    // Prevent multiple dialogs from opening
    if (_isDialogOpen) return;

    MiniProgramManager miniProgramManager = MiniProgramManager();

    // 获取login_id和login_img
    LoginData? loginData = await miniProgramManager.getMiniProgramLoginId();

    if (loginData == null) {
      print("获取login_id失败");
      return;
    }

    if (loginData.loginImg != null && loginData.loginImg!.isNotEmpty) {
      _isDialogOpen = true; // Mark the dialog as open

      // Show the WeChatLoginDialog and set `barrierDismissible: true` to allow clicking outside to close
      showDialog(
        context: context,
        barrierDismissible:
            false, // Allows clicking outside the dialog to close it
        builder: (BuildContext context) {
          return WeChatLoginDialog(
            loginImg: loginData.loginImg!,
            statusCode: 201, // 默认显示二维码未扫描
            onClose: () {
              Navigator.of(context).pop(); // 关闭弹框
              miniProgramManager.stopChecking(); // 停止计时器
              _isDialogOpen = false; // Mark the dialog as closed
            },
            onRefresh: () async {
              // 重新请求login_id和login_img并更新界面
              LoginData? refreshedLoginData =
                  await miniProgramManager.getMiniProgramLoginId();
              if (refreshedLoginData != null) {
                // Restart the checking process and return the new loginImg
                miniProgramManager
                    .startCheckingLoginStatus(refreshedLoginData.loginId);
                return refreshedLoginData.loginImg;
              }
              return null;
            },
          );
        },
      ).then((_) {
        // Ensure the flag is reset when the dialog is closed
        _isDialogOpen = false;
      });

      miniProgramManager
          .startCheckingLoginStatus(loginData.loginId); // Start checking status
    } else {
      // 如果login_img为空，直接打开小程序
      await miniProgramManager
          .openWeChatMiniProgramWithLoginId(loginData.loginId);
      // 开始检查扫码状态
      miniProgramManager.startCheckingLoginStatus(loginData.loginId);
    }
  }
}
