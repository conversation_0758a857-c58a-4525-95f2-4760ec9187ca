import 'package:npemployee/model/download/download_local_model.dart';
import 'package:npemployee/model/download/task_info.dart';
import 'package:npemployee/model/mine/guest_model.dart';
import 'package:npemployee/model/mine/me_nodel.dart';
import 'package:npemployee/model/mine/old_person_model.dart';
import 'package:npemployee/model/mine/user_login_model.dart';
import 'package:npemployee/model/mine/user_sub_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class PreferencesManager {
  static final PreferencesManager _instance = PreferencesManager._internal();
  factory PreferencesManager() => _instance;

  PreferencesManager._internal();

  Future<SharedPreferences> get _prefs async =>
      await SharedPreferences.getInstance();

  Future<String?> getBaseCurrency() async {
    return (await _prefs).getString(SharedPreferencesKeys.baseCurrency);
  }

  Future<bool> setBaseCurrency(String value) async {
    return (await _prefs).setString(SharedPreferencesKeys.baseCurrency, value);
  }

  // 其他 SharedPreferences 方法

  Future<int?> getLanguageOption() async {
    return (await _prefs).getInt(SharedPreferencesKeys.languageOption);
  }

  Future<bool> setLanguageOption(int index) async {
    return (await _prefs).setInt(SharedPreferencesKeys.languageOption, index);
  }

  Future<String?> getLastLogin() async {
    return (await _prefs).getString(SharedPreferencesKeys.lastLogin);
  }

  Future<bool> setLastLogin(String emailOrPhone) async {
    return (await _prefs)
        .setString(SharedPreferencesKeys.lastLogin, emailOrPhone);
  }

  Future<String?> getTokenValue() async {
    return (await _prefs).getString(SharedPreferencesKeys.tokenValue);
  }

  Future<bool> setTokenValue(String token) async {
    return (await _prefs).setString(SharedPreferencesKeys.tokenValue, token);
  }

  Future<bool> removeTokenValue() async {
    return (await _prefs).remove(SharedPreferencesKeys.tokenValue);
  }

  Future<String?> getLastSelectedCompany() async {
    return (await _prefs).getString(SharedPreferencesKeys.lastSelectedCompany);
  }

  Future<bool> setLastSelectedCompany(Map selectedCompany) async {
    return (await _prefs).setString(SharedPreferencesKeys.lastSelectedCompany,
        json.encode(selectedCompany));
  }

  Future<String?> getBrokerageCompanies() async {
    return (await _prefs).getString(SharedPreferencesKeys.brokerageCompanies);
  }

  Future<bool> setBrokerageCompanies(List<Map> companies) async {
    return (await _prefs).setString(
        SharedPreferencesKeys.brokerageCompanies, json.encode(companies));
  }

  Future<bool> setSearchHistory(List<String> searchHistory) async {
    return (await _prefs)
        .setStringList(SharedPreferencesKeys.searchHistory, searchHistory);
  }

  Future<List<String>> getSearchHistory() async {
    return (await _prefs).getStringList(SharedPreferencesKeys.searchHistory) ??
        [];
  }

  Future<bool> clearSearchHistory() async {
    return (await _prefs).remove(SharedPreferencesKeys.searchHistory);
  }

  Future<int?> getSelectedLanguage() async {
    return (await _prefs).getInt(SharedPreferencesKeys.selectedLanguage);
  }

  Future<bool> setSelectedLanguage(int index) async {
    return (await _prefs).setInt(SharedPreferencesKeys.selectedLanguage, index);
  }

  Future<bool> setAccountExpansionStatus(Map<String, bool> statuses) async {
    return (await _prefs).setString(
        SharedPreferencesKeys.accountExpansionStatus, jsonEncode(statuses));
  }

  Future<Map<String, bool>> getAccountExpansionStatus() async {
    String? statusString =
        (await _prefs).getString(SharedPreferencesKeys.accountExpansionStatus);
    if (statusString != null) {
      return Map<String, bool>.from(jsonDecode(statusString));
    }
    // Default values if not set before
    return {
      "asset_accounts": true,
      "credit_accounts": true,
      "installment_loans": true
    };
  }

  Future<bool> setAssetsPageExpansionStatus(Map<String, bool> statuses) async {
    String statusString = jsonEncode(statuses);
    return (await _prefs).setString(
        SharedPreferencesKeys.assetsPageExpansionStatus, statusString);
  }

  Future<Map<String, bool>> getAssetsPageExpansionStatus() async {
    String? statusString = (await _prefs)
        .getString(SharedPreferencesKeys.assetsPageExpansionStatus);
    if (statusString != null) {
      return Map<String, bool>.from(jsonDecode(statusString));
    }
    // Default values if not set before
    return {
      'asset_accounts': true,
      'investment_accounts': true,
      'fixed_assets': true,
      'accounts_receivable': true,
      'credit_accounts': true,
      'installmentLoans': true
    };
  }

  Future<bool> setPortfolioExpansionStatus(List<bool> statuses) async {
    String statusString = statuses.join(',');
    return (await _prefs).setString(
        SharedPreferencesKeys.portfolioExpansionStatus, statusString);
  }

  Future<List<bool>> getPortfolioExpansionStatus() async {
    String? statusString = (await _prefs)
        .getString(SharedPreferencesKeys.portfolioExpansionStatus);
    if (statusString != null) {
      return statusString.split(',').map((s) => s == 'true').toList();
    }
    // 默认情况下，如果以前没有设置过，则所有ExpansionTile都为展开状态。
    return List.filled(10, true); // 你可以根据需要更改这里的默认值
  }

  Future<String?> getSelectedLanguageModel() async {
    return (await _prefs)
        .getString(SharedPreferencesKeys.selectedLanguageModel);
  }

  Future<bool> setSelectedLanguageModel(String lang) async {
    return (await _prefs)
        .setString(SharedPreferencesKeys.selectedLanguageModel, lang);
  }

  Future<bool> setNeedsSync(bool value) async {
    return (await _prefs).setBool(SharedPreferencesKeys.needsSync, value);
  }

  Future<bool?> getNeedsSync() async {
    return (await _prefs).getBool(SharedPreferencesKeys.needsSync);
  }

  Future<bool?> getShowAmountInLargeUnit() async {
    return (await _prefs).getBool(SharedPreferencesKeys.showAmountInLargeUnit);
  }

  Future<bool> setShowAmountInLargeUnit(bool value) async {
    return (await _prefs)
        .setBool(SharedPreferencesKeys.showAmountInLargeUnit, value);
  }

  Future<bool?> getShowScreenshotImport() async {
    return (await _prefs).getBool(SharedPreferencesKeys.showScreenshotImport);
  }

  Future<bool> setShowScreenshotImport(bool value) async {
    return (await _prefs)
        .setBool(SharedPreferencesKeys.showScreenshotImport, value);
  }

  Future<bool?> getShowSavingModule() async {
    return (await _prefs).getBool(SharedPreferencesKeys.showSavingModule);
  }

  Future<bool> setShowSavingModule(bool value) async {
    return (await _prefs)
        .setBool(SharedPreferencesKeys.showSavingModule, value);
  }

  // 存储生物解锁状态
  Future<bool?> getSecurityProtection() async {
    return (await _prefs).getBool(SharedPreferencesKeys.securityProtection);
  }

  Future<bool> setSecurityProtection(bool value) async {
    return (await _prefs)
        .setBool(SharedPreferencesKeys.securityProtection, value);
  }

  // 存储数字密码
  Future<String?> getDigitalPassword() async {
    return (await _prefs).getString(SharedPreferencesKeys.digitalPassword);
  }

  Future<bool> setDigitalPassword(String? value) async {
    return (await _prefs)
        .setString(SharedPreferencesKeys.digitalPassword, value ?? '');
  }

  Future<String?> getNoRecommendVersion() async {
    return (await _prefs).getString(SharedPreferencesKeys.noRecommendVersion);
  }

  Future<bool> setNoRecommendVersion(String version) async {
    return (await _prefs)
        .setString(SharedPreferencesKeys.noRecommendVersion, version);
  }

  // Method to save the asset visibility to SharedPreferences
  Future<bool> setShowAssetValue(bool value) async {
    return (await _prefs).setBool(SharedPreferencesKeys.showAssetValue, value);
  }

// Method to get the asset visibility from SharedPreferences
  Future<bool?> getShowAssetValue() async {
    return (await _prefs).getBool(SharedPreferencesKeys.showAssetValue);
  }

  Future<bool> setShowPortfolioValue(bool value) async {
    return (await _prefs)
        .setBool(SharedPreferencesKeys.showPortfolioValue, value);
  }

// Method to get the asset visibility from SharedPreferences
  Future<bool?> getShowPortfolioValue() async {
    return (await _prefs).getBool(SharedPreferencesKeys.showPortfolioValue);
  }

  Future<bool> setShowExpenseValue(bool value) async {
    return (await _prefs)
        .setBool(SharedPreferencesKeys.showExpenseValue, value);
  }

  Future<bool?> getShowExpenseValue() async {
    return (await _prefs).getBool(SharedPreferencesKeys.showExpenseValue);
  }

  Future<bool> setUserLoginModel(String data) async {
    return (await _prefs).setString(SharedPreferencesKeys.userLoginModel, data);
  }

  Future<UserLoginModel?> getUserLoginModel() async {
    String? jsonStr =
        (await _prefs).getString(SharedPreferencesKeys.userLoginModel);
    if (jsonStr == null) {
      return null;
    }
    Map<String, dynamic> dMap = jsonDecode(jsonStr);
    UserLoginModel data = UserLoginModel.fromJson(dMap);
    return data;
  }

  Future<bool> setUserInfo(MeModel userInfo) async {
    return (await _prefs).setString(
        SharedPreferencesKeys.userInfo, jsonEncode(userInfo.toJson()));
  }

  Future<MeModel?> getUserInfo() async {
    String? jsonStr = (await _prefs).getString(SharedPreferencesKeys.userInfo);
    if (jsonStr == null) {
      return null;
    }
    Map dMap = jsonDecode(jsonStr);
    MeModel me = MeModel.fromJson(dMap);
    return me;
  }

  Future<bool> removeUserInfo() async {
    return (await _prefs).remove(SharedPreferencesKeys.userInfo);
  }

  Future<bool> setUserSubInfo(UserSubModel model) async {
    return (await _prefs).setString(
        SharedPreferencesKeys.userSubInfo, jsonEncode(model.toJson()));
  }

  Future<UserSubModel?> getUserSubInfo() async {
    String? jsonStr =
        (await _prefs).getString(SharedPreferencesKeys.userSubInfo);
    if (jsonStr == null) {
      return null;
    }
    Map dMap = jsonDecode(jsonStr);
    UserSubModel me = UserSubModel.fromJson(dMap);
    return me;
  }

  Future<bool> removeUserSubInfo() async {
    return (await _prefs).remove(SharedPreferencesKeys.userSubInfo);
  }

  Future<bool> setGuestInfo(GuestModel model) async {
    return (await _prefs)
        .setString(SharedPreferencesKeys.guestInfo, jsonEncode(model.toJson()));
  }

  Future<GuestModel?> getGuestInfo() async {
    String? jsonStr = (await _prefs).getString(SharedPreferencesKeys.guestInfo);
    if (jsonStr == null) {
      return null;
    }
    Map dMap = jsonDecode(jsonStr);
    GuestModel me = GuestModel.fromJson(dMap);
    return me;
  }

  Future<bool> removeGuestInfo() async {
    return (await _prefs).remove(SharedPreferencesKeys.guestInfo);
  }

  Future<bool> setOldUserInfo(OldPersonModel old) async {
    return (await _prefs)
        .setString(SharedPreferencesKeys.oldUserInfo, jsonEncode(old.toJson()));
  }

  Future<OldPersonModel?> getOldUserInfo() async {
    String? jsonStr =
        (await _prefs).getString(SharedPreferencesKeys.oldUserInfo);
    if (jsonStr == null) {
      return null;
    }
    Map dMap = jsonDecode(jsonStr);
    OldPersonModel me = OldPersonModel.fromJson(dMap);
    return me;
  }

  Future<bool> removeOldUserInfo() async {
    return (await _prefs).remove(SharedPreferencesKeys.oldUserInfo);
  }

  Future<bool> setImLoginInfo(ImLoginModel model) async {
    return (await _prefs).setString(
        SharedPreferencesKeys.imLoginInfo, jsonEncode(model.toJson()));
  }

  Future<ImLoginModel?> getIMLoginInfo() async {
    String? jsonStr =
        (await _prefs).getString(SharedPreferencesKeys.imLoginInfo);
    if (jsonStr == null) {
      return null;
    }
    Map dMap = jsonDecode(jsonStr);
    ImLoginModel model = ImLoginModel.fromJson(dMap);
    return model;
  }

  Future<bool> removeImLoginInfo() async {
    return (await _prefs).remove(SharedPreferencesKeys.imLoginInfo);
  }

  Future<bool> setDepartmentName(String name) async {
    return (await _prefs).setString(SharedPreferencesKeys.departmentName, name);
  }

  Future<String?> getDepartmentName() async {
    return (await _prefs).getString(SharedPreferencesKeys.departmentName);
  }

  Future<bool> removeDepartmentName() async {
    return (await _prefs).remove(SharedPreferencesKeys.departmentName);
  }

  Future<bool> setDepartmentId(int id) async {
    return (await _prefs).setInt(SharedPreferencesKeys.departmentId, id);
  }

  Future<String?> getDepartmentId() async {
    return (await _prefs).getString(SharedPreferencesKeys.departmentId);
  }

  Future<bool> removeDepartmentId() async {
    return (await _prefs).remove(SharedPreferencesKeys.departmentId);
  }


  Future<bool> addToMeetingPptDownloads(TaskInfo param) async {
    List<TaskInfo> local =
        await PreferencesManager().getMeetingPptDownloads() ?? [];
    int index = local.indexWhere((e) => e.name == param.name);
    if (index == -1) {
      local.add(param);
    }
    return (await _prefs).setString(
        SharedPreferencesKeys.annualMeetingPPTDownloads,
        jsonEncode(local.map((e) => e.toJson()).toList()));
  }

  Future<bool> removeMeetingPptDownload(TaskInfo param) async {
    List<TaskInfo> local = await getMeetingPptDownloads() ?? [];
    local.removeWhere((e) => e.name == param.name);
    return (await _prefs).setString(
        SharedPreferencesKeys.annualMeetingPPTDownloads,
        jsonEncode(local.map((e) => e.toJson()).toList()));
  }

  Future<bool> setMeetingPptDownloads(List<TaskInfo> params) async {
    return (await _prefs).setString(
        SharedPreferencesKeys.annualMeetingPPTDownloads,
        jsonEncode(params.map((e) => e.toJson()).toList()));
  }

  Future<List<TaskInfo>?> getMeetingPptDownloads() async {
    String? jsonStr = (await _prefs)
        .getString(SharedPreferencesKeys.annualMeetingPPTDownloads);
    if (jsonStr == null) {
      return null;
    }
    List? downloadMaps = jsonDecode(jsonStr);
    List<TaskInfo>? downloads =
        downloadMaps?.map((e) => TaskInfo.formJson(e)).toList();
    return downloads;
  }

  Future<bool> setCapabilityDownloadsLocal(
      List<DownloadLocalModel> params) async {
    return (await _prefs).setString(
        SharedPreferencesKeys.capabilityDownloadsLocal,
        jsonEncode(params.map((e) => e.toJson()).toList()));
  }

  Future<List<DownloadLocalModel>> getCapabilityDownloadsLocal() async {
    String? jsonStr = (await _prefs)
        .getString(SharedPreferencesKeys.capabilityDownloadsLocal);
    if (jsonStr == null) {
      return [];
    }
    List downloadMaps = jsonDecode(jsonStr);
    List<DownloadLocalModel>? downloads =
        downloadMaps.map((e) => DownloadLocalModel.fromJson(e)).toList();
    return downloads;
  }

  Future<bool> removeCapabilityDownloadLocal(DownloadLocalModel model) async {
    List<DownloadLocalModel> local = await getCapabilityDownloadsLocal();
    int index = local.indexWhere((e) => e.course.id == model.course.id);
    if (index != -1) {
      local[index].chapters = model.chapters;
    }
    return (await _prefs).setString(
        SharedPreferencesKeys.capabilityDownloadsLocal,
        jsonEncode(local.map((e) => e.toJson()).toList()));
  }

  Future<bool> saveOrUpdateCapabilityDownloadLocal(
      DownloadLocalModel model) async {
    List<DownloadLocalModel> local = await getCapabilityDownloadsLocal();
    int index = local.indexWhere((e) => e.course.id == model.course.id);
    if (index == -1) {
      local.add(model);
    } else {
      // 遍历新的章节
      for (var newChapter in model.chapters) {
        // 在已有课程中查找对应章节
        int chapterIndex =
            local[index].chapters.indexWhere((c) => c.id == newChapter.id);

        if (chapterIndex == -1) {
          // 章节不存在,直接添加
          local[index].chapters.add(newChapter);
        } else {
          // 章节存在,更新小节
          for (var newLesson in newChapter.lesson) {
            // 检查小节是否已存在
            bool lessonExists = local[index]
                .chapters[chapterIndex]
                .lesson
                .any((l) => l.id == newLesson.id);

            if (!lessonExists) {
              // 小节不存在则添加
              local[index].chapters[chapterIndex].lesson.add(newLesson);
            } else {
              // 检查视频清晰度，不一致的话更换
              for (var l in local[index].chapters[chapterIndex].lesson) {
                if (l.id == newLesson.id) {
                  for (var media in l.media) {
                    if (media.media_type == 'mp4') {
                      media = newLesson.media.first;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    // 对每个model中的chapters和lessons进行排序
    for (var model in local) {
      // 按id从小到大排序chapters
      model.chapters.sort((a, b) => a.id.compareTo(b.id));
      // 对每个chapter中的lessons按id排序
      for (var chapter in model.chapters) {
        chapter.lesson.sort((a, b) => a.id.compareTo(b.id));
      }
    }
    return (await _prefs).setString(
        SharedPreferencesKeys.capabilityDownloadsLocal,
        jsonEncode(local.map((e) => e.toJson()).toList()));
  }

  Future<bool> setProxyOn(bool value) async {
    return (await _prefs).setBool(SharedPreferencesKeys.proxyOn, value);
  }

  Future<bool> getProxyOn() async {
    return (await _prefs).getBool(SharedPreferencesKeys.proxyOn) ?? false;
  }

  Future<bool> removeProxyOn() async {
    return (await _prefs).remove(SharedPreferencesKeys.proxyOn);
  }

  Future<bool> setProxyIp(String value) async {
    return (await _prefs).setString(SharedPreferencesKeys.proxyIp, value);
  }

  Future<String?> getProxyIp() async {
    return (await _prefs).getString(SharedPreferencesKeys.proxyIp);
  }

  Future<bool> removeProxyIp() async {
    return (await _prefs).remove(SharedPreferencesKeys.proxyIp);
  }

  Future<bool> setProxyPort(String value) async {
    return (await _prefs).setString(SharedPreferencesKeys.proxyPort, value);
  }

  Future<String?> getProxyPort() async {
    return (await _prefs).getString(SharedPreferencesKeys.proxyPort);
  }

  Future<bool> removeProxyPort() async {
    return (await _prefs).remove(SharedPreferencesKeys.proxyPort);
  }



  // Method to get the asset visibility from SharedPreferences
  Future<bool?> getShouldShowWelcomeProxyValue() async {
    return (await _prefs).getBool(SharedPreferencesKeys.hasShowedProxy);
  }

  Future<bool> setShouldShowWelcomeProxyValue(bool value) async {
    return (await _prefs)
        .setBool(SharedPreferencesKeys.hasShowedProxy, value);
  }


}

class SharedPreferencesKeys {
  static const String baseCurrency = 'baseCurrency';
  static const String languageOption = 'languageOption';
  static const String lastLogin = 'lastLogin';
  static const String tokenValue = 'tokenValue';
  static const String userMobile = 'user_mobile';
  static const String userId = 'user_id';
  static const String userName = 'user_name';
  static const String capabilityDownloadsLocal =
      'capability_downloads_local'; //能力提升下载列表本地
  static const String annualMeetingPPTDownloads =
      'annual_meeting_PPT_downloads';
  static const String lastSelectedCompany = 'last_selected_company';
  static const String brokerageCompanies = 'brokerage_companies';
  static const String searchHistory = 'searchHistory';
  static const String selectedLanguage = 'selectedLanguage';
  static const String userInfo = 'userInfo';
  static const String userSubInfo = 'userSubInfo';
  static const String guestInfo = 'guest_info';
  static const String oldUserInfo = 'oldUserInfo';
  static const String imLoginInfo = 'im_login_info';
  static const String departmentName = 'department_name';
  static const String departmentId = 'department_id';
  static const String userLoginModel = 'userLoginModel';
  static const String statisticUserAssetInfo = 'statisticUserAssetInfo';
  // static const String portfolioList = 'portfolioList';
  static const String name = 'name';
  static const String accountExpansionStatus = 'accountExpansionStatus';
  static const String assetsPageExpansionStatus = 'assetsPageExpansionStatus';
  static const String portfolioExpansionStatus = 'portfolioExpansionStatus';
  static const String selectedLanguageModel = 'selectedLanguageModel';
  static const String needsSync = 'needsSync'; // 新增的同步标志键
  static const String decisionCriteria = 'decisionCriteria';
  static const String rebalanceCriteria = 'rebalanceCriteria';
  static const String showAmountInLargeUnit = 'showAmountInLargeUnit';
  static const String showScreenshotImport = 'showScreenshotImport';
  static const String showSavingModule = 'showSavingModule';
  static const String digitalPassword = 'digitalPassword';
  static const String securityProtection = 'securityProtection';

  static const String noRecommendVersion = 'noRecommendVersion';
  static const String showAssetValue = 'showAssetValue';
  static const String showPortfolioValue = 'showPortfolioValue';
  static const String showExpenseValue = 'showExpenseValue';
  static const String selectedPortfolio = 'selectedPortfolio';

  static const String proxyOn = 'testProxyOn';
  static const String proxyIp = 'testProxyIp';
  static const String proxyPort = 'testProxyPort';

  static const String hasShowedProxy = 'hasShowedProxy';
}
