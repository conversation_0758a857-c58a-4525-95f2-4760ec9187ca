import 'dart:async';

import 'package:npemployee/manager/bloc_manager.dart';

class CloseTimerManager {
  static final CloseTimerManager _instance = CloseTimerManager._internal();

  static Timer? timer;
  static int selected = 0;
  static int seconds = 0;

  static Function(int) onChange = (int seconds) {};

  factory CloseTimerManager() {
    return _instance;
  }

  CloseTimerManager._internal();

  static void start() {
    timer = Timer.periodic(const Duration(seconds: 1), (t) {
      seconds--;
      onChange(seconds);
      if (seconds == 0) {
        BlocManager().audioBloc.audioPlayer?.stop();
        CloseTimerManager.cancel();
      }
    });
  }

  static void cancel() {
    selected = 0;
    seconds = 0;
    timer?.cancel();
    timer = null;
  }
}
