import 'dart:convert';

import 'package:mmkv/mmkv.dart';

class LocalCacheManager {
  static LocalCacheManager shared = LocalCacheManager.__();
  LocalCacheManager.__();

  MMKV get mmkv => MMKV.defaultMMKV(cryptKey: '\u{2}U');

  bool flag = false;

  Future<String> init() async {
    flag = true;
    try {
      return await MMKV.initialize();
    } catch (e) {
      print('init mmkv error: $e');
      return '';
    }
  }

  bool checkInitialize() {
    if (flag == false) {
      print("MMKV Must be inited");
      return true;
    }
    return false;
  }

  bool putMapByKey(String key, Map<String, dynamic> val, {int maxAge = 0}) {
    if (checkInitialize()) return false;
    String el = json.encode(val);
    return mmkv.encodeString(key, el);
  }

  Map<String, dynamic>? getMapWithKey(String key,
      {Map<String, dynamic>? nullValue}) {
    if (checkInitialize()) return nullValue;
    String? dl = mmkv.decodeString(key);
    if (dl == null) {
      return nullValue;
    }
    return json.decode(dl);
  }

  void removeValuesForKeys(List<String> keys) {
    if (checkInitialize()) return;
    mmkv.removeValues(keys);
  }

  void clearAll() {
    if (checkInitialize()) return;
    mmkv.clearAll();
  }

  void clearMemoryCache() {
    if (checkInitialize()) return;
    mmkv.clearMemoryCache();
  }
}

class LocalCachekeys {
  //通讯录缓存
  static const String contactsKey = 'contacts_key';
}
