import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:just_audio/just_audio.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/provider/audio_bloc/audio_event.dart';
import 'package:npemployee/provider/audio_bloc/audio_state.dart';

class AudioBloc extends Bloc<AudioEvent, AudioState> {
  AudioBloc() : super(AudioState().init()) {
    on<AudioProgressEvent>(_audioProgressEvent);
    on<AudioPlayingEvent>(_audioPlayingEvent);
    on<AudioInitEvent>(_audioInitEvent);
  }

  void _audioInitEvent(event, emit) {
    state.audioPlayer = event.audioPlayer;
    emit(state.clone());
  }

  void _audioProgressEvent(event, emit) {
    state.position = event.position;
    state.type = AudioEventType.position;
    emit(state.clone());
  }

  void _audioPlayingEvent(event, emit) {
    state.playingLesson = event.playingLesson;
    state.type = AudioEventType.playingLesson;
    emit(state.clone());
  }

  AudioPlayer? get audioPlayer => state.audioPlayer;
  LessonModel? get playingLesson => state.playingLesson;
}
