import 'package:just_audio/just_audio.dart';
import 'package:npemployee/model/study/chapter_model.dart';

abstract class AudioEvent {}

class AudioInitEvent extends AudioEvent {
  final AudioPlayer audioPlayer;

  AudioInitEvent(this.audioPlayer);
}

class AudioProgressEvent extends AudioEvent {
  final int position;

  AudioProgressEvent(this.position);
}

class AudioPlayingEvent extends AudioEvent {
  final LessonModel playingLesson;

  AudioPlayingEvent(this.playingLesson);
}
