import 'package:just_audio/just_audio.dart';
import 'package:npemployee/model/study/chapter_model.dart';

enum AudioEventType {
  none,
  position,
  playingLesson,
}

class AudioState {
  LessonModel? playingLesson; //正在播放的
  int? position;
  AudioEventType? type;
  AudioPlayer? audioPlayer;

  AudioState init() {
    return AudioState()
      ..playingLesson
      ..position = 0
      ..type = AudioEventType.none
      ..audioPlayer;
  }

  AudioState clone() {
    return AudioState()
      ..playingLesson = playingLesson
      ..position = position
      ..type = type
      ..audioPlayer = audioPlayer;
  }
}
