import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:npemployee/provider/avatar_bloc/avatar_event.dart';
import 'package:npemployee/provider/avatar_bloc/avatar_state.dart';

class AvatarBloc extends Bloc<AvatarEvent, AvatarState> {
  AvatarBloc() : super(AvatarState().init()) {
    on<AvatarChangeEvent>(_avatarChangeEvent);
  }

  void _avatarChangeEvent(event, emit) {
    state.avatar = event.avatar;
    emit(state.clone());
  }
}
