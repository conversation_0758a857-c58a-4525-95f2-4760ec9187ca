import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:npemployee/provider/tab_bloc/tab_event.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';

class TabBloc extends Bloc<TabEvent, TabState> {
  TabBloc() : super(TabState().init()) {
    on<TabChangeEvent>(_tabChangeEvent);
    on<TabUnreadEvent>(_tabUnreadEvent);
  }

  void _tabChangeEvent(event, emit) {
    state.page = event.page;
    state.type = TabEventType.change;
    emit(state.clone());
  }

  void _tabUnreadEvent(event, emit) {
    state.mineUnread = event.unread;
    state.type = TabEventType.unread;
    emit(state.clone());
  }
}
