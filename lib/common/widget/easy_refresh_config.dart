import 'package:easy_refresh/easy_refresh.dart';

class EasyRefreshConfig {
  static void init() {
    const classicHeaderChinese = ClassicHeader(
      dragText: '下拉刷新',
      armedText: '释放刷新',
      readyText: '加载中...',
      processingText: '加载中...',
      processedText: '加载完成',
      noMoreText: '没有更多',
      failedText: '加载失败',
      messageText: '最后更新于 %T',
      safeArea: false,
    );

    const classicFooterChinese = ClassicFooter(
      dragText: '上拉加载',
      armedText: '释放刷新',
      readyText: '加载中...',
      processingText: '加载中...',
      processedText: '加载完成',
      noMoreText: '没有更多',
      failedText: '加载失败',
      messageText: '最后更新于 %T',
      showMessage: true,
      infiniteOffset: null,
    );

    EasyRefresh.defaultHeaderBuilder = () => classicHeaderChinese;
    EasyRefresh.defaultFooterBuilder = () => classicFooterChinese;
  }
}
