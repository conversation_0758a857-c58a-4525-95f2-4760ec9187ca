import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';

class SubmitButton extends StatelessWidget {
  final void Function() onTap;
  final bool enable;
  final String name;
  final Color? nameColor;
  const SubmitButton(
      {super.key,
      required this.onTap,
      this.enable = false,
      required this.name,
      this.nameColor});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ElevatedButton(
        onPressed: enable ? onTap : null,
        style: ButtonStyle(
            backgroundColor: WidgetStatePropertyAll(enable
                ? AppTheme.colorBlue
                : AppTheme.colorBlue.withOpacity(0.2)),
            minimumSize: WidgetStatePropertyAll(Size(343.w, 49.h)),
            shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.r)))),
        child: Text(name,
            style: TextStyle(color: nameColor ?? Colors.white, fontSize: 16.sp)
                .pfMedium),
      ),
    );
  }
}
