import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';

class CustomDialog extends StatelessWidget {
  final String title;
  final String content;
  final String leftButtonText;
  final String rightButtonText;
  final Color leftButtonColor;
  final Color rightButtonColor;
  final Color leftTextColor;
  final Color rightTextColor;
  final Function onLeft;
  final Function onRight;
  final String? backgroundImage;

  const CustomDialog({
    super.key,
    required this.title,
    required this.content,
    required this.leftButtonText,
    required this.rightButtonText,
    required this.leftButtonColor,
    required this.rightButtonColor,
    required this.onLeft,
    required this.onRight,
    this.backgroundImage,
    required this.leftTextColor,
    required this.rightTextColor,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          image: backgroundImage != null
              ? DecorationImage(
                  image: AssetImage(backgroundImage!),
                  fit: BoxFit.cover,
                )
              : null,
        ),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 17.sp,
                color: '333333'.toColor(),
              ).pfSemiBold,
            ),
            const SizedBox(height: 20),
            Text(
              content,
              style: TextStyle(
                      fontSize: 15.sp,
                      color: '333333'.toColor(),
                      height: 25.sp / 15.sp)
                  .pfRegular,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: leftButtonColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                      ),
                      elevation: 0,
                    ),
                    onPressed: () => onLeft(),
                    child: Text(
                      leftButtonText,
                      style: TextStyle(color: leftTextColor, fontSize: 15.sp)
                          .pfMedium,
                    ),
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: rightButtonColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16.0),
                      ),
                      elevation: 0,
                    ),
                    onPressed: () => onRight(),
                    child: Text(
                      rightButtonText,
                      style: TextStyle(color: rightTextColor, fontSize: 15.sp)
                          .pfMedium,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
