import 'package:flutter/material.dart';

class AppTheme {
  // Default font size for scaling
  static double fontSizeScale = 1.0; // Global font size scale

  // Define your theme colors
  static const Color primaryColor = Color(0xFF0054FF); // Brand Green
  static const Color backgroundColor = Color(0xFFF7F8FD); // Background
  static const Color colorWhite = Color(0xFFFFFFFF);
  static const Color colorBlack = Color(0xFF000000);
  static const Color colorBlack2 = Color(0xFF222222);
  static const Color colorPrimaryBlack = Color(0xFF333333);
  static const Color colorGrey = Color(0xFF777777);
  static const Color colorBlackTitle = Color(0xFF1D1C1F);

  static const Color colorRed = Color(0xFFF33920);
  static const Color colorBlue = Color(0xFF0054FF);
  static const Color colorLightGrey = Color(0xFF808080);
  static const Color colorLightGrey2 = Color(0xFF999999);
  static const Color colorButtonGrey = Color(0xFFECECEC);
  static const Color colorDivider = Color(0xFFE6E6E6);

  static ThemeData lightTheme = ThemeData(
    primaryColor: primaryColor,
    scaffoldBackgroundColor: backgroundColor,
    appBarTheme: AppBarTheme(
      color: colorWhite,
      elevation: 0,
      iconTheme: IconThemeData(color: colorBlack),
    ),
    textTheme: _buildTextTheme(),
  );

  static ThemeData darkTheme = ThemeData(
    primaryColor: primaryColor,
    scaffoldBackgroundColor: colorBlack,
    appBarTheme: AppBarTheme(
      color: colorBlack,
      elevation: 0,
      iconTheme: IconThemeData(color: colorWhite),
    ),
    textTheme: _buildTextTheme(isDark: true),
  );

  static TextTheme _buildTextTheme({bool isDark = false}) {
    return TextTheme(
      displayLarge: TextStyle(
        fontSize: 24.0 * fontSizeScale,
        fontWeight: FontWeight.bold,
        color: isDark ? colorWhite : colorBlack,
      ),
      displayMedium: TextStyle(
        fontSize: 22.0 * fontSizeScale,
        fontWeight: FontWeight.w600,
        color: isDark ? colorWhite : colorBlack,
      ),
      bodyLarge: TextStyle(
        fontSize: 16.0 * fontSizeScale,
        color: isDark ? colorWhite : colorBlack,
      ),
      bodyMedium: TextStyle(
        fontSize: 14.0 * fontSizeScale,
        color: isDark ? colorWhite : colorBlack,
      ),
      labelLarge: TextStyle(
        fontSize: 14.0 * fontSizeScale,
        fontWeight: FontWeight.bold,
        color: colorWhite,
      ),
    );
  }

  // Method to set global font size scale
  static void setFontSizeScale(double scale) {
    fontSizeScale = scale;
  }

  // Method to get a text style with custom parameters
  static TextStyle getTextStyle({
    double baseSize = 14.0,
    Color? color,
    FontWeight fontWeight = FontWeight.normal,
  }) {
    return TextStyle(
      fontSize: baseSize * fontSizeScale,
      color: color ?? colorBlack,
      fontWeight: fontWeight,
    );
  }
}
