import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NoDataPage extends StatelessWidget {
  final ScrollPhysics? physics;
  final double? height;
  final String? img;
  final String? content;
  const NoDataPage(
      {super.key, this.physics, this.height, this.img, this.content});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      height: (height ?? ScreenUtil().screenHeight),
      child: SingleChildScrollView(
        physics: physics,
        child: Column(
          children: [
            SizedBox(height: 91.h),
            Image.asset(img ?? 'assets/png/mine/approval_no_data.png',
                width: 126.w, height: 133.h),
            SizedBox(height: 14.5.h),
            Text(content ?? '暂无数据，这里空空如也～',
                style: TextStyle(color: Color(0xFFB0B5BF), fontSize: 14.sp)),
          ],
        ),
      ),
    );
  }
}
