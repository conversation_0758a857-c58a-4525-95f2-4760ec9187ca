/// <AUTHOR>
/// @project FlutterKit
/// @date 8/6/23

// import 'package:flutter/foundation.dart';
// import 'package:flutter/gestures.dart';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:webview_flutter/webview_flutter.dart';

// Import for Android features.
import 'package:webview_flutter_android/webview_flutter_android.dart';

class ProxyWebviewScreenPage extends StatefulWidget {
  final String? title;
  final String url;
  final LinearGradient? gradient;
  const ProxyWebviewScreenPage(
      {super.key, required this.url, this.title, this.gradient});

  @override
  State<ProxyWebviewScreenPage> createState() => _WebviewScreenPageState();
}

class _WebviewScreenPageState extends State<ProxyWebviewScreenPage> {
  late final WebViewController controller;
  double progress = 0;


  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _initializeWebView() {
    controller = WebViewController()
      ..setBackgroundColor(
          widget.gradient != null ? Colors.transparent : Colors.white)
      ..setJavaScriptMode(JavaScriptMode.unrestricted) //允许javascript
      ..setOnConsoleMessage((mess) {
        debugPrint('---- web打印 ${mess.message}');
      })
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
          },
          onPageFinished: (url) {

          },
          onProgress: (p) {
            setState(() {
              progress = p / 100;
            });
          },
          onWebResourceError: (error) {
          },
          onUrlChange: (change) {},
          onHttpAuthRequest: (request) {},
          onHttpError: (error) {
          },
          onNavigationRequest: (request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return widget.title == null
        ? Material(
      color: Colors.white,
      child: Stack(
        children: [
          Container(
            height: 130.h,
            decoration: BoxDecoration(gradient: widget.gradient),
          ),
          _webViewBuilder()
        ],
      ),
    )
        : Scaffold(
      appBar: CommonNav(
        title: widget.title!,
        onBack: () {
          controller.canGoBack().then((v) {
            if (v) {
              controller.goBack();
            } else {
              NavigatorUtils.pop(context);
            }
          });
        },
      ),
      body: _webViewBuilder(),
    );
  }


  Widget _webViewBuilder() {
    return Column(
      children: [
        progress < 1.0
            ? LinearProgressIndicator(
          value: progress,
          color: AppTheme.colorBlue,
        )
            : Container(),
        Expanded(child:
        WebViewWidget(
          controller: controller,
        )
        )
      ],
    );
  }

}
