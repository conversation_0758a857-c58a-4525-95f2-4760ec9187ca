import 'dart:io';

import 'package:flutter/material.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class PdfPreviewPage extends StatelessWidget {
  final String title;
  final String filePath;
  const PdfPreviewPage(
      {super.key, required this.filePath, required this.title});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonNav(title: title),
      body: filePath.contains('https://')
          ? SfPdfViewer.network(filePath)
          : SfPdfViewer.file(File(filePath)),
    );
  }
}
