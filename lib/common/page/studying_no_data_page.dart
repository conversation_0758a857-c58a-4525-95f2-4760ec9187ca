import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/routers/campus_training_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';

class StudyingNoDataPage extends StatelessWidget {
  final ScrollPhysics? physics;
  final int index; //1-能力提升 2-校区培训
  const StudyingNoDataPage({super.key, required this.index, this.physics});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      child: SingleChildScrollView(
        physics: physics,
        child: Column(
          children: [
            SizedBox(height: 91.h),
            SizedBox(
              width: 133,
              height: 133,
              child: Image.asset('assets/png/study/studying_no_data.png'), //
            ),
            Sized<PERSON><PERSON>(height: 14.5.h),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '暂无学习数据\n快去',
                    style: TextStyle(
                            color: const Color(0xFFB0B5BF), fontSize: 14.sp)
                        .pfRegular,
                  ),
                  TextSpan(
                    text: index == 1 ? '能力提升' : '校区培训',
                    style: TextStyle(
                      color: const Color(0xFF0054FF),
                      fontSize: 14.sp,
                      decoration: TextDecoration.underline,
                    ).pfRegular,
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // 处理点击事件
                        if (index == 1) {
                          NavigatorUtils.push(
                              context, StudyRouter.capabilityUpgrading,
                              arguments: {'title': '能力提升'});
                        } else {
                          NavigatorUtils.push(
                              context, CampusTrainingRouter.campusTrainingPage,
                              arguments: {'title': '校区培训'});
                        }
                      },
                  ),
                  TextSpan(
                    text: '学习吧～',
                    style: TextStyle(
                            color: const Color(0xFFB0B5BF), fontSize: 14.sp)
                        .pfRegular,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
