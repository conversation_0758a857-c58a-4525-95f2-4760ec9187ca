import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/model/mine/team_manager_department_model.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class TeamNoDataPage extends StatelessWidget {
  final DepartmentModel? department;
  const TeamNoDataPage({super.key, this.department});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset('assets/png/mine/team_manager/no_data.png',
            width: 129.w, height: 114.h),
        SizedBox(height: 14.5.h),
        Text(
          '暂无数据，请添加团队介绍~',
          style: TextStyle(
            color: const Color(0xFFB0B5BF),
            fontSize: 14.sp,
          ).pfRegular,
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
        ElevatedButton(
          onPressed: () {
            NavigatorUtils.push(context, MineRouter.teamIntroductionEditPage,
                arguments: {'department': department});
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF0054FF),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(22.r),
            ),
            minimumSize: Size(120.w, 44.h),
          ),
          child: Text(
            '添加团队介绍',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
            ).pfMedium,
          ),
        ),
      ],
    );
  }
}
