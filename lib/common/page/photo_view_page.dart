import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:photo_view/photo_view.dart';

class PhotoViewPage extends StatelessWidget {
  final String url;
  const PhotoViewPage({super.key, required this.url});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '查看图片'),
      body: PhotoView(imageProvider: CachedNetworkImageProvider(url)),
    );
  }
}
