import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:permission_handler/permission_handler.dart';

import '../dialog/custom_dialog.dart';

class PosterChangeCodeWebView extends StatefulWidget {
  final String url;
  final String? title;
  const PosterChangeCodeWebView({super.key, required this.url, this.title});

  @override
  createState() => _PosterChangeCodeWebViewState();
}

class _PosterChangeCodeWebViewState extends State<PosterChangeCodeWebView> {
  final GlobalKey webViewKey = GlobalKey();

  String url = '';
  double progress = 0;
  InAppWebViewController? webViewController;
  @override
  void initState() {
    super.initState();
    url = widget.url;
    Permission.storage.status.then((storageStatus) {
      if (!storageStatus.isGranted) {
        if (Platform.isAndroid) {
          showDialog(
            context: context,
            builder: (context) => CustomDialog(
              title: "提示",
              content: "保存海报到本地需要存储权限",
              cancelButtonText: "取消",
              confirmButtonText: "确定",
              cancelButtonColor: AppTheme.colorButtonGrey,
              confirmButtonColor: AppTheme.colorBlue,
              onCancel: () {
                Navigator.of(context).pop();
              },
              onConfirm: () async {
                Navigator.of(context).pop();
                _requestStoragePermission();
              },
            ),
          );
        } else {
          _requestStoragePermission();
        }
      } else {
        _requestStoragePermission();
      }
    });
  }

  Future<void> saveGrowUpCode(String imageUrl) async {
    if (await _requestStoragePermission()) {
      try {
        final imageData =
            await NetworkAssetBundle(Uri.parse(imageUrl)).load("");
        final bytes = imageData.buffer.asUint8List();
        final result = await ImageGallerySaver.saveImage(bytes);
        if (result['isSuccess']) {
          ToastUtils.show('增长码保存成功');
          // Fluttertoast.showToast(msg: "增长码保存成功");
        } else {
          ToastUtils.show('增长码保存失败');
          // Fluttertoast.showToast(msg: "增长码保存失败");
        }
      } catch (e) {
        print(e);
        ToastUtils.show('增长码保存失败');
        // Fluttertoast.showToast(msg: "增长码保存失败");
      }
    }
  }

  Future<bool> _requestStoragePermission() async {
    PermissionStatus status = await Permission.storage.request();
    return status.isGranted;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop) {
            webViewController?.canGoBack().then((canGoBack) {
              if (canGoBack) {
                webViewController?.goBack();
              } else {
                Navigator.pop(context);
              }
            });
          }
        },
        child: Material(
          color: Colors.white,
          child: Column(children: <Widget>[
            if (widget.title != null)
              TeamManagerNav(
                title: widget.title!,
                leftWidget: IconButton(
                  onPressed: () {
                    webViewController?.canGoBack().then((canGoBack) {
                      if (canGoBack) {
                        webViewController?.goBack();
                      } else {
                        Navigator.pop(context);
                      }
                    });
                  },
                  icon: const Icon(Icons.arrow_back_ios,
                      color: Color(0xFF000000)),
                  iconSize: 18,
                ),
              ),
            Expanded(
                child: Stack(
              children: [
                InAppWebView(
                  key: webViewKey,
                  initialUrlRequest: URLRequest(url: WebUri(widget.url)),
                  initialSettings: InAppWebViewSettings(
                      javaScriptEnabled: true,
                      useOnDownloadStart: true,
                      allowsBackForwardNavigationGestures: true,
                      underPageBackgroundColor: Colors.white),
                  onWebViewCreated: (controller) async {
                    webViewController = controller;
                  },
                  onLongPressHitTestResult: (controller, hitTestResult) {
                    if (Platform.isAndroid) {
                      var growUpCodeImageUrl = hitTestResult.extra;
                      if (growUpCodeImageUrl != null &&
                          growUpCodeImageUrl.isNotEmpty) {
                        saveGrowUpCode(growUpCodeImageUrl);
                      }
                    }
                  },
                  onDownloadStartRequest:
                      (controller, DownloadStartRequest downloadStartRequest) {
                    // 注册消息处理器
                    controller.addJavaScriptHandler(
                      handlerName: 'onImageCaptured',
                      callback: (args) async {
                        if (args.isEmpty || !(args[0] is String)) {
                          return;
                        }
                        String base64Data = args[0];
                        Uint8List bytes = base64Decode(base64Data);

                        if (await _requestStoragePermission()) {
                          try {
                            final result =
                                await ImageGallerySaver.saveImage(bytes);
                            if (result['isSuccess']) {
                              // Fluttertoast.showToast(msg: "图片保存成功");
                            } else {
                              ToastUtils.show('图片保存失败');
                            }
                          } catch (e) {
                            print(e);
                            ToastUtils.show('图片保存失败');
                          }
                        }
                      },
                    );

                    // 注入 JavaScript 代码以获取 canvas 图像
                    String jsCode = """
          (async () => {
            const canvasElement = document.getElementById('canvas');
            if (!canvasElement) {
              window.flutter_inappwebview.callHandler('onCanvasCaptured', JSON.stringify({ success: false, message: 'Canvas element with id "canvas" not found.' }));
              return;
            }

            try {
              // 将 canvas 转换为图像数据 URL
              const dataUrl = canvasElement.toDataURL('image/png');
              window.flutter_inappwebview.callHandler('onImageCaptured', dataUrl.split(',')[1]);
            } catch (error) {
              console.error('Error capturing canvas:', error);
              window.flutter_inappwebview.callHandler('onImageCaptured', JSON.stringify({ success: false, message: 'Failed to capture canvas.' }));
            }
          })();
        """;

                    controller.evaluateJavascript(source: jsCode);
                  },
                  onLoadStart: (controller, url) async {},
                  onLoadStop: (controller, url) async {},
                  onUpdateVisitedHistory: (controller, url, isReload) {},
                  onTitleChanged: (controller, title) {},
                  onProgressChanged: (controller, progress) {
                    setState(() {
                      this.progress = progress / 100;
                    });
                  },
                ),
                progress < 1.0
                    ? LinearProgressIndicator(
                        value: progress,
                        color: AppTheme.colorBlue,
                      )
                    : Container(),
              ],
            )),
          ]),
        ));
  }
}
