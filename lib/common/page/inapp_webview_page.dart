import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:geolocator/geolocator.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/routers/navigator_utils.dart';

enum ProgressIndicatorType { circular, linear }

class InappWebviewPage extends StatefulWidget {
  final String url;
  final String? title;
  const InappWebviewPage({super.key, required this.url, this.title});

  @override
  State<InappWebviewPage> createState() => _InappWebviewPageState();
}

class _InappWebviewPageState extends State<InappWebviewPage> {
  final GlobalKey webViewKey = GlobalKey();

  String url = '';
  double progress = 0;
  InAppWebViewController? webViewController;

  @override
  void initState() {
    super.initState();
    url = widget.url;
    _tes();
  }

  void _tes() async {
    await InAppWebViewController.setWebContentsDebuggingEnabled(kDebugMode);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(children: <Widget>[
        if (widget.title != null) TeamManagerNav(title: widget.title!),
        Expanded(
            child: Stack(
          children: [
            GestureDetector(
              onHorizontalDragEnd: (details) {
                webViewController?.canGoBack().then((v) {
                  if (v) {
                    if (details.primaryVelocity! > 0) {
                      webViewController?.goBack();
                    } else {
                      webViewController?.goForward();
                    }
                  }
                });
              },
              child: InAppWebView(
                key: webViewKey,
                initialUrlRequest: URLRequest(url: WebUri(widget.url)),
                initialSettings: InAppWebViewSettings(
                    allowsBackForwardNavigationGestures: true,
                    underPageBackgroundColor: Colors.white),
                onWebViewCreated: (controller) async {
                  webViewController = controller;

                  addJsEvent(controller);
                  debugPrint('in app web view 抽奖接口初始化完毕');
                  if (!kIsWeb &&
                      defaultTargetPlatform == TargetPlatform.android) {
                    await controller.startSafeBrowsing();
                  }
                },
                onLoadStart: (controller, url) async {},
                onLoadStop: (controller, url) async {
                  if (url != null) {
                    if (url.path.contains('xtj-lottery-mobile')) {
                      controller
                          .evaluateJavascript(source: '''window.raffleForm = {
  name: '${_getUserInfo()?['name']}',
  mobile: '${_getUserInfo()?['mobile']}',
  id_no: '${_getUserInfo()?['id_no']}',
  department_type: '${_getUserInfo()?['department_type']}',
  department_name: '${_getUserInfo()?['department_name']}',
}''');
                    }
                  }

                  final sslCertificate = await controller.getCertificate();
                  debugPrint('issecret ${sslCertificate.toString()}');
                },
                onUpdateVisitedHistory: (controller, url, isReload) {},
                onTitleChanged: (controller, title) {},
                onProgressChanged: (controller, progress) {
                  setState(() {
                    this.progress = progress / 100;
                  });
                },
                /* shouldOverrideUrlLoading:
                      (controller, navigationAction) async {
                    final originalUrl = navigationAction.request.url.toString();
                    final Uri originalUri = Uri.parse(originalUrl);
                    Map<String, String> existingParams =
                        Map.from(originalUri.queryParameters);
                    if (existingParams.containsKey('name')) {
                      return NavigationActionPolicy.ALLOW;
                    }

                    debugPrint('Original URL: $originalUrl');

                    if (originalUrl.contains('xtj-lottery-mobile')) {
                      existingParams['name'] = '${_getUserInfo()?['name']}';
                      existingParams['mobile'] = '${_getUserInfo()?['mobile']}';
                      existingParams['id_no'] = '${_getUserInfo()?['id_no']}';
                      existingParams['department_type'] =
                          '${_getUserInfo()?['department_type']}';
                      existingParams['department_name'] =
                          '${_getUserInfo()?['department_name']}';

                      // 使用 replace 方法来更新 URL 的查询参数
                      Uri modifiedUri =
                          originalUri.replace(queryParameters: existingParams);

                      debugPrint('Modified URL: ${modifiedUri.toString()}');

                      controller.loadUrl(
                          urlRequest:
                              URLRequest(url: WebUri(modifiedUri.toString())));

                      return NavigationActionPolicy.CANCEL;
                    } else {
                      return NavigationActionPolicy.ALLOW;
                    }
                  } */
              ),
            ),
            progress < 1.0
                ? LinearProgressIndicator(
                    value: progress,
                    color: AppTheme.colorBlue,
                  )
                : Container(),
          ],
        )),
      ]),
    );
  }

  static bool urlIsSecure(Uri url) {
    return (url.scheme == "https") || isLocalizedContent(url);
  }

  static bool isLocalizedContent(Uri url) {
    return (url.scheme == "file" ||
        url.scheme == "chrome" ||
        url.scheme == "data" ||
        url.scheme == "javascript" ||
        url.scheme == "about");
  }

  void addJsEvent(InAppWebViewController controller) {
    controller.addJavaScriptHandler(
        handlerName: 'geoLocation',
        callback: (args) async {
          return await getGeoLocation();
        });
    controller.addJavaScriptHandler(
        handlerName: 'exit',
        callback: (args) async {
          NavigatorUtils.pop(context);
        });
  }

  Future<dynamic> getGeoLocation() async {
    if (!await Geolocator.isLocationServiceEnabled()) {
      return {'code': 1, 'msg': '手机定位开关未打开，请打开后重试'};
    }
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return {'code': 1, 'msg': '请前往手机设置打并开定位权限'};
      }
    }
    if (permission == LocationPermission.deniedForever) {
      return {'code': 1, 'msg': '请前往手机设置打并开定位权限'};
    }
    if (permission == LocationPermission.unableToDetermine) {
      return {'code': 1, 'msg': '获取定位权限发生未知错误，请联系管理员'};
    }
    Position position = await Geolocator.getCurrentPosition();
    return {
      'code': 0,
      'data': {'lat': position.latitude, "lng": position.longitude}
    };
  }

  Map? _getUserInfo() {
    if (GlobalPreferences().userInfo == null) {
      return null;
    }
    Map userInfo = {};
    userInfo['name'] = GlobalPreferences().userInfo?.user.name ?? '';
    userInfo['mobile'] = GlobalPreferences().userInfo?.user.mobile ?? '';
    userInfo['id_no'] = GlobalPreferences().userInfo?.user.id_no ?? '';
    userInfo['department_type'] =
        GlobalPreferences().userInfo?.departmentTypeMin;
    userInfo['department_name'] =
        GlobalPreferences().userInfo?.departmentNameMin;
    return userInfo;
  }
}
