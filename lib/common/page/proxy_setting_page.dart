import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/widget/common_nav.dart';

class ProxySettingPage extends StatefulWidget {
  const ProxySettingPage({Key? key}) : super(key: key);

  @override
  State<ProxySettingPage> createState() => _ProxySettingPageState();
}

class _ProxySettingPageState extends State<ProxySettingPage> {
  bool _isOn = false; // 是否开启代理
  final TextEditingController _controller =
      TextEditingController(); // TextEditingController 实例，用于控制和获取 TextField 的内容
  final TextEditingController _portController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _isOn = GlobalPreferences().isProxyOn;
    _controller.text = GlobalPreferences().proxyIp ?? '';
    _portController.text = GlobalPreferences().proxyPort ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: 'PROXY'),
      body: Column(
        children: [
          TextField(
            controller: _controller,
            decoration: const InputDecoration(
              hintText: '***********',
            ),
          ),
          TextField(
            controller: _portController,
            decoration: const InputDecoration(
              hintText: '8888',
            ),
          ),
          CupertinoSwitch(
              // switch开关组件，用户可以切换代理的开关状态。当状态改变时，调用 _action 方法更新设置。
              value: _isOn,
              onChanged: _action)
        ],
      ),
    );
  }

  _action(bool isOn) {
    _isOn = isOn;
    GlobalPreferences().isProxyOn = isOn;
    GlobalPreferences().proxyIp = _controller.text;
    GlobalPreferences().proxyPort = _portController.text;
    setState(() {});
  }
}
