import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
import 'package:url_launcher/url_launcher.dart';

// 冻结和注册后带待审批和驳回共用此dialog

class CustomTipsDialog extends StatefulWidget {
  final String imagePath;
  final String title;
  final String content;
  final String? buttonText;
  final String? status; //账号状态 审核不通过 禁用
  final VoidCallback onPressed;
  final List? departments;
  final bool? canPop;
  final bool? isSystem; //审批人是否是系统 例如：途小径审批中心
  const CustomTipsDialog({
    super.key,
    required this.imagePath,
    required this.title,
    required this.content,
    this.buttonText,
    this.status,
    required this.onPressed,
    this.departments,
    this.canPop,
    this.isSystem,
  });

  @override
  State<CustomTipsDialog> createState() => _CustomTipsDialogState();
}

class _CustomTipsDialogState extends State<CustomTipsDialog> {
  List departmentLeaders = [];

  int? departmentId;

  String? optionText;

  bool? canPing;

  bool get isNotPass => widget.status == 'review_not_pass';

  bool get isFrozen => widget.status == 'frozen';

  _getDepartmentLeader() {
    if (widget.departments != null && widget.departments!.isNotEmpty) {
      /* int? minRoleId;
      for (var dept in widget.departments!) {
        if (dept['roles'] != null && dept['roles'] is List) {
          for (var roleItem in dept['roles']) {
            int currentId = roleItem['id'];
            if (minRoleId == null || currentId < minRoleId) {
              minRoleId = currentId;
              departmentId = dept['department']['id'];
            }
          }
        }
      }
      if (minRoleId == null) {
        return;
      } */
      int minDepartId;
      List departs = widget.departments!;
      departs.insert(0, {
        'department': {'id': 59}
      });
      departs.insert(1, {
        'department': {'id': 60}
      });
      departs.sort((a, b) {
        return a['department']['id'].compareTo(b['department']['id']);
      });
      minDepartId = departs.first['department']['id'];

      UserServiceProvider().getReviewerList(minDepartId).then((value) {
        if (value?.code == 0) {
          departmentLeaders = value?.data ?? [];
          if (departmentLeaders.isNotEmpty) {
            //过滤负责人被冻结的
            departmentLeaders = departmentLeaders
                .where((e) => e['status'] != 'frozen')
                .toList();
            setState(() {});
          }
        } else {
          EasyLoading.showError('获取部门负责人失败');
        }
      });
    }
  }

  void _getApprovalPingStatus() {
    UserServiceProvider().getApprovalCanPing().then((res) {
      if (res?.code == 0) {
        canPing = res?.data;
        setState(() {});
      }
    });
  }

  @override
  void initState() {
    super.initState();
    if (isFrozen) {
      //获取部门负责人
      _getDepartmentLeader();
    }
    if (isNotPass && widget.isSystem != null && !widget.isSystem!) {
      _getApprovalPingStatus();
    }
    optionText = widget.buttonText;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: widget.canPop ?? false,
        child: Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28), // Dialog round corners
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min, // Adjust size to fit content
            children: [
              // Top image without padding
              ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(25)),
                child: Image.asset(
                  widget.imagePath,
                  width: double.infinity,
                  fit: BoxFit.cover, // Scale to match dialog width
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(
                    16.0), // Padding for content below the image
                child: Column(
                  children: [
                    const SizedBox(height: 25),
                    // Title
                    Text(
                      widget.title,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.colorPrimaryBlack, // Hex 333333 color
                      ),
                    ),
                    const SizedBox(height: 20),
                    // Content
                    if (widget.status == 'review_not_pass')
                      Text(
                        widget.content,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 15,
                          color: Color(0xFF606266), // Hex 606266 color
                        ),
                      ),
                    if (widget.status == 'frozen')
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: '请联系部门负责人\n',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: const Color(0xFF606266),
                              ),
                            ),
                            ...departmentLeaders.map((e) => TextSpan(
                                  text: '【${e['name']}】',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: const Color(0xFF0054FF),
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      if (e['guid'] == 'system_approval') {
                                        WeChatService().launchWeChatWork();
                                      } else {
                                        launchUrl(
                                            Uri.parse('tel:${e['mobile']}'));
                                      }
                                    },
                                )),
                            TextSpan(
                              text: '\n启用账号',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Color(0xFF606266),
                              ),
                            ),
                          ],
                        ),
                      ),
                    SizedBox(height: 20.h),
                    if (isNotPass) _notPassButton(),
                    if (isFrozen)
                      _optionButton(
                        '退出登录',
                        AppTheme.colorBlue,
                        null,
                        Colors.white,
                        () {
                          AppInfo().clearLoginStatu();
                          Navigator.of(context).pushNamedAndRemoveUntil(
                              CommonRouter.loginPage, (route) => false);
                        },
                      ),
                    if (optionText != null)
                      SizedBox(
                        width: 170,
                        child: ElevatedButton(
                          onPressed: widget.onPressed,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.colorBlue,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 0, // No shadow
                            padding: EdgeInsets.symmetric(
                                vertical: 14.5.h, horizontal: 23.w),
                          ),
                          child: Text(
                            optionText!,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget _notPassButton() {
    if (widget.isSystem != null && widget.isSystem!) {
      //系统审核联系微信客服
      return _optionButton('联系客服审批', AppTheme.colorBlue, null, Colors.white,
          () {
        WeChatService().launchWeChatWork();
      });
    } else {
      //非系统审批
      if (canPing != null) {
        if (canPing!) {
          return _optionButton('催一催', AppTheme.colorBlue, null, Colors.white,
              () {
            EasyLoading.show();
            UserServiceProvider().getApprovalPing().then((res) {
              EasyLoading.dismiss();
              if (res?.code == 0) {
                setState(() {
                  canPing = false;
                });
              }
            });
          });
        } else {
          return _optionButton(
              '已经催过了，尝试线下联系～',
              AppTheme.colorBlue,
              const Color(0xFF216AFE).withOpacity(0.1),
              AppTheme.colorBlue,
              null);
        }
      } else {
        return Container();
      }
    }
  }

  Widget _optionButton(String title, Color? bac, Color? disBac, Color textColor,
      void Function()? onPressed) {
    return SizedBox(
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: bac,
          disabledBackgroundColor: disBac,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          padding: EdgeInsets.symmetric(vertical: 14.5.h, horizontal: 23.w),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            color: textColor,
          ),
        ),
      ),
    );
  }
}
