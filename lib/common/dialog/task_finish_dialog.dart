import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/widget/submit_button.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class TaskFinishDialog extends StatelessWidget {
  final bool hasExam;
  final void Function()? onTap;
  const TaskFinishDialog({super.key, this.hasExam = false, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Container(
        padding: EdgeInsets.fromLTRB(20.w, 13.h, 20.w, 35.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFD9E6FF),
              Colors.white,
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 图标
            SizedBox(
              width: 163.5.w,
              height: 123.h,
              child: Image.asset('assets/png/mine/task/finish_task.png'),
            ),
            SizedBox(height: 8.h),
            Text(
              '提示',
              style: TextStyle(fontSize: 18.sp, color: '#333333'.toColor())
                  .pfSemiBold,
            ),
            SizedBox(height: 18.h),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                text:
                    hasExam ? '您已完成学习任务，请参与答题考试\n继续学习可前往' : '您已完成学习任务\n继续学习可前往',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: '#333333'.toColor(),
                ).pfRegular,
                children: [
                  TextSpan(
                    text: '【我的】',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.colorBlue,
                    ).pfMedium,
                    recognizer: TapGestureRecognizer()..onTap = onTap,
                  ),
                  TextSpan(
                    text: ' - ',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.colorBlue,
                    ).pfMedium,
                  ),
                  TextSpan(
                    text: '【我的学习】',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.colorBlue,
                    ).pfMedium,
                    recognizer: TapGestureRecognizer()..onTap = onTap,
                  ),
                  const TextSpan(
                    text: '中查看学习记录',
                  ),
                ],
              ),
            ),
            SizedBox(height: 25.5.h),
            // 按钮
            SizedBox(
              width: 158.w,
              height: 48.h,
              child: SubmitButton(
                  onTap: () {
                    NavigatorUtils.pop(context);
                  },
                  name: '我知道了',
                  enable: true),
            )
          ],
        ),
      ),
    );
  }
}
