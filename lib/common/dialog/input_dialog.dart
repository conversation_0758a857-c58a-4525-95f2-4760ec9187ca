import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/page/home/<USER>';

class InputDialog extends StatefulWidget {
  final String title;
  final String placeholder;
  final String cancelButtonText;
  final String confirmButtonText;
  final Color cancelButtonColor;
  final Color confirmButtonColor;
  final Function onCancel;
  final Function(String) onConfirm;
  final String backgroundImage;

  const InputDialog({
    Key? key,
    required this.title,
    this.placeholder = '请输入驳回理由',
    this.cancelButtonText = '取消',
    this.confirmButtonText = '确定',
    this.cancelButtonColor = const Color(0xFFECECEC),
    this.confirmButtonColor = const Color(0xFF0054FF),
    this.onCancel = _defaultCancelFunction,
    required this.onConfirm,
    this.backgroundImage = 'assets/png/input_dialog_bg.png',
  }) : super(key: key);

  static void _defaultCancelFunction() {}

  @override
  _InputDialogState createState() => _InputDialogState();
}

class _InputDialogState extends State<InputDialog> {
  final TextEditingController _controller = TextEditingController();
  String? _errorText;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          image: DecorationImage(
            image: AssetImage(widget.backgroundImage),
            fit: BoxFit.cover,
          ),
        ),
        padding: const EdgeInsets.symmetric(
            horizontal: 28.0), // Padding 28 pixels for left and right
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 22), // Space from the top to the title
            Text(
              widget.title,
              style: TextStyle(
                fontSize: 17.sp,
                color: AppTheme.colorPrimaryBlack,
              ).pfSemiBold,
            ),
            const SizedBox(height: 33), // Space between title and input field
            TextField(
              controller: _controller,
              decoration: InputDecoration(
                hintText: widget.placeholder,
                hintStyle: TextStyle(color: Color(0xFF777777), fontSize: 15.sp)
                    .pfRegular,
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: 10.0),
                border: OutlineInputBorder(
                  borderRadius:
                      BorderRadius.circular(16.0), // 16 pixel corner radius
                  borderSide: BorderSide(
                    color: Colors.grey.shade300,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.0),
                  borderSide: BorderSide(
                    color: Colors.grey.shade300,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16.0),
                  borderSide: BorderSide(
                    color: Colors.blue,
                  ),
                ),
                errorText: _errorText,
              ),
            ),
            const SizedBox(height: 30), // Space between input field and buttons
            Row(
              children: [
                // Cancel Button
                Expanded(
                  child: SizedBox(
                    height: 45, // Set button height to 45 pixels
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.cancelButtonColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                              16.0), // Rounded corner 16 pixels
                        ),
                        elevation: 0, // Remove shadow effect
                      ),
                      onPressed: () => widget.onCancel(),
                      child: Text(
                        widget.cancelButtonText,
                        style: TextStyle(
                                color: const Color(0xFFB3B3B3), fontSize: 15.sp)
                            .pfMedium,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 20), // 20 pixels gap between buttons
                // Confirm Button
                Expanded(
                  child: SizedBox(
                    height: 45, // Set button height to 45 pixels
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: widget.confirmButtonColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                              16.0), // Rounded corner 16 pixels
                        ),
                        elevation: 0, // Remove shadow effect
                      ),
                      onPressed: _handleConfirm,
                      child: Text(
                        widget.confirmButtonText,
                        style: TextStyle(color: Colors.white, fontSize: 15.sp)
                            .pfMedium,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16), // Optional bottom padding
          ],
        ),
      ),
    );
  }

  void _handleConfirm() {
    if (_controller.text.isEmpty) {
      setState(() {
        _errorText = '请输入内容'; // Set the error message
      });
    } else {
      widget.onConfirm(_controller.text);
    }
  }
}
