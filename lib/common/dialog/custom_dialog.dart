import 'package:flutter/material.dart';
import 'package:npemployee/common/app_theme.dart';

class CustomDialog extends StatelessWidget {
  final String title;
  final String content;
  final String cancelButtonText;
  final String confirmButtonText;
  final Color cancelButtonColor;
  final Color confirmButtonColor;
  final Function onCancel;
  final Function onConfirm;
  final String backgroundImage;

  const CustomDialog({
    Key? key,
    required this.title,
    required this.content,
    this.cancelButtonText = '取消',
    this.confirmButtonText = '确定',
    this.cancelButtonColor = const Color(0xFFECECEC),
    this.confirmButtonColor = const Color(0xFF0054FF),
    this.onCancel = _defaultCancelFunction,
    this.onConfirm = _defaultConfirmFunction,
    this.backgroundImage = 'assets/png/account_cancel_bg.png',
  }) : super(key: key);

  static void _defaultCancelFunction() {}
  static void _defaultConfirmFunction() {}

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          image: DecorationImage(
            image: AssetImage(backgroundImage),
            fit: BoxFit.cover,
          ),
        ),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.bold,
                color: AppTheme.colorPrimaryBlack,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              content,
              style: TextStyle(
                fontSize: 15,
                color: AppTheme.colorPrimaryBlack,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 25),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10.0), // Padding to ensure 28 pixels from both edges
              child: Row(
                children: [
                  // Cancel Button
                  Expanded(
                    child: SizedBox(
                      height: 45, // Set the button height to 45 pixels
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: cancelButtonColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16.0), // Rounded corner 16 pixels
                          ),
                          elevation: 0, // Remove shadow effect
                        ),
                        onPressed: () => onCancel(),
                        child: Text(
                          cancelButtonText,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 20), // 20 pixels gap between buttons
                  // Confirm Button
                  Expanded(
                    child: SizedBox(
                      height: 45, // Set the button height to 45 pixels
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: confirmButtonColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16.0), // Rounded corner 16 pixels
                          ),
                          elevation: 0, // Remove shadow effect
                        ),
                        onPressed: () => onConfirm(),
                        child: Text(
                          confirmButtonText,
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
