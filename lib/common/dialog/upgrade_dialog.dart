import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:install_plugin/install_plugin.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:url_launcher/url_launcher.dart';

class UpgradeDialog extends StatefulWidget {
  final Map? data;
  final Map? storeData;
  const UpgradeDialog({super.key, this.data, this.storeData});

  @override
  State<UpgradeDialog> createState() => _UpgradeDialogState();
}

class _UpgradeDialogState extends State<UpgradeDialog> {
  Map? upgradeData;
  List<String>? contents = [];
  bool isRequired = true;
  String? version;
  String? platform;
  String? apkUrl;
  String? fileName;

  bool isDownloading = false;
  double downloadProgress = 0;
  String? taskID;
  final ReceivePort _port = ReceivePort();

  Future<bool> _checkPermission() async {
    if (Platform.isIOS) {
      return true;
    }

    if (Platform.isAndroid) {
      final info = await DeviceInfoPlugin().androidInfo;
      if (info.version.sdkInt > 28) {
        return true;
      }

      final status = await Permission.storage.status;
      if (status == PermissionStatus.granted) {
        return true;
      }

      final result = await Permission.storage.request();
      return result == PermissionStatus.granted;
    }

    throw StateError('unknown platform');
  }

  void _bindBackgroundIsolate() {
    final isSuccess = IsolateNameServer.registerPortWithName(
      _port.sendPort,
      'downloader_upgrade_send_port',
    );
    if (!isSuccess) {
      _unbindBackgroundIsolate();
      _bindBackgroundIsolate();
      return;
    }
    _port.listen((dynamic data) {
      final taskId = (data as List<dynamic>)[0] as String;
      final status = DownloadTaskStatus.fromInt(data[1] as int);
      final progress = data[2] as int;

      debugPrint(
        'Callback on UI isolate: '
        'task ($taskId) is in status ($status) and process ($progress)',
      );

      if (taskID == taskId) {
        setState(() {
          downloadProgress = progress / 100;
        });
      }
    });
  }

  void _unbindBackgroundIsolate() {
    IsolateNameServer.removePortNameMapping('downloader_upgrade_send_port');
  }

  @pragma('vm:entry-point')
  static void downloadCallback(
    String id,
    int status,
    int progress,
  ) {
    debugPrint(
      'Callback on background isolate: '
      'task ($id) is in status ($status) and process ($progress)',
    );

    IsolateNameServer.lookupPortByName('downloader_upgrade_send_port')
        ?.send([id, status, progress]);
  }

  void _downloadApk() async {
    final taskId = await FlutterDownloader.enqueue(
      url: apkUrl!,
      fileName: fileName,
      savedDir: await AppInfo().upgradeDownloadDir,
      showNotification: false,
      saveInPublicStorage: false,
      openFileFromNotification: false,
    );
    if (taskId == null) {
      ToastUtils.show('下载失败, 请重试...');
      return;
    }
    setState(() {
      isDownloading = true;
      taskID = taskId;
    });
  }

  @override
  void initState() {
    super.initState();
    upgradeData = widget.data ?? {};
    contents = upgradeData?['note'].toString().split("-@-");
    isRequired = upgradeData?['required_update'];
    version = upgradeData?['version'];
    platform = upgradeData?['platform'];

    if (Platform.isAndroid && widget.storeData?['approve'] == "0") {
      apkUrl = upgradeData?['file_url'];
      // apkUrl =
      //     'https://res.xtjzx.cn/decompress_proddata/4134/最新时政“珍”题【2024年11月更新】@-1732605880246@.pdf';
      if (apkUrl?.contains(".apk") ?? false) {
        fileName = apkUrl?.split('/').last;
      } else {
        fileName = '${apkUrl?.split('/').last}.apk';
      }
      _bindBackgroundIsolate();
      FlutterDownloader.registerCallback(downloadCallback, step: 1);
    }
  }

  @override
  void dispose() {
    if (Platform.isAndroid && widget.storeData?['approve'] == "0") {
      _unbindBackgroundIsolate();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: !isRequired,
        child: Material(
          color: Colors.transparent,
          child: Center(
            child: Container(
              width: 300.w,
              height: 382.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(27.5.r),
              ),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Positioned(
                    top: -10.h,
                    child: Image.asset('assets/png/home/<USER>',
                        width: 300.w),
                  ),
                  Positioned(
                      top: -40.h,
                      right: 10.w,
                      child: Image.asset('assets/png/home/<USER>',
                          width: 116.5.w, height: 144.h)),
                  Positioned(
                    left: 48.w,
                    top: 47.h,
                    child: Text(
                      isDownloading ? '新版本下载中' : '新版本',
                      style: TextStyle(color: Colors.white, fontSize: 27.sp)
                          .pfSemiBold,
                    ),
                  ),
                  Positioned(
                    left: isDownloading ? 134.5.w + 85.w : 134.5.w,
                    top: 47.h,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 4.5.w, vertical: 1.5.h),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(7.r),
                      ),
                      child: Text(
                        'v$version',
                        style: TextStyle(
                                color: const Color(0xFF3376FE), fontSize: 8.sp)
                            .pfRegular,
                      ),
                    ),
                  ),
                  isDownloading
                      ? Center(
                          child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  LinearProgressIndicator(
                                    value: downloadProgress,
                                    backgroundColor: Color(0xFFF7F7F7),
                                    color: Color(0xFF0054FF),
                                  ),
                                  SizedBox(height: 10.h),
                                  Text(
                                    textAlign: TextAlign.center,
                                    downloadProgress == -1
                                        ? '下载失败，请重试'
                                        : (downloadProgress == 1
                                            ? '下载完成'
                                            : '下载过程中，请勿退出App \n (取消后会重新下载)'),
                                    style: TextStyle(
                                            fontSize: 13.sp,
                                            color: const Color(0xFF999999))
                                        .pfRegular,
                                  )
                                ],
                              )),
                        )
                      : Positioned(
                          top: 143.5.h,
                          bottom: 82.5.h,
                          child: Container(
                            width: 300.w,
                            padding: EdgeInsets.symmetric(horizontal: 30.w),
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  ...contents?.map((e) => Container(
                                            margin:
                                                EdgeInsets.only(bottom: 16.h),
                                            child: BulletText(e),
                                          )) ??
                                      [],
                                ],
                              ),
                            ),
                          )),
                  isDownloading
                      ? Positioned(
                          bottom: 15.h,
                          child: Container(
                              width: 300.w,
                              padding: EdgeInsets.symmetric(horizontal: 30.w),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  GestureDetector(
                                    onTap: () => _cancelDownload(),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFE7E7E7),
                                        borderRadius:
                                            BorderRadius.circular(10.r),
                                      ),
                                      width: 108.w,
                                      height: 50.h,
                                      alignment: Alignment.center,
                                      child: Text(
                                        '取消',
                                        style: TextStyle(
                                                fontSize: 16.sp,
                                                color: const Color(0xFF333333))
                                            .pfRegular,
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () => _installApk(),
                                    child: Container(
                                      width: 108.w,
                                      height: 50.h,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFE7E7E7),
                                        borderRadius:
                                            BorderRadius.circular(10.r),
                                      ),
                                      child: Text(
                                        '安装',
                                        style: TextStyle(
                                                fontSize: 16.sp,
                                                color: const Color(0xFF333333))
                                            .pfRegular,
                                      ),
                                    ),
                                  ),
                                ],
                              )))
                      : Positioned(
                          bottom: 15.h,
                          child: Container(
                            width: 300.w,
                            padding: EdgeInsets.symmetric(horizontal: 30.w),
                            child: !isRequired
                                ? Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      GestureDetector(
                                        onTap: () =>
                                            Navigator.of(context).pop(),
                                        child: Image.asset(
                                            'assets/png/home/<USER>',
                                            width: 108.w,
                                            height: 41.5.h),
                                      ),
                                      GestureDetector(
                                        onTap: () => _jumpToStore(),
                                        child: Image.asset(
                                            'assets/png/home/<USER>',
                                            width: 108.w,
                                            height: 50.h),
                                      )
                                    ],
                                  )
                                : GestureDetector(
                                    onTap: () => _jumpToStore(),
                                    child: Image.asset(
                                        'assets/png/home/<USER>',
                                        width: 221.5.w,
                                        height: 50.h),
                                  ),
                          ))
                ],
              ),
            ),
          ),
        ));
  }

  void _jumpToStore() async {
    if (!isRequired) {
      Navigator.of(context).pop();
    }
    if (Platform.isIOS) {
      Uri appStoreUrl = Uri.parse('https://apps.apple.com/app/id6503936061');
      if (await canLaunchUrl(appStoreUrl)) {
        launchUrl(appStoreUrl, mode: LaunchMode.externalApplication);
      }
    } else if (Platform.isAndroid) {
      if (widget.storeData?['approve'] == "0") {
        //审核未通过，应用内下载
        if (await _checkPermission()) {
          _downloadApk();
        } else {
          ToastUtils.show('请授予存储权限');
        }
      } else {
        launchAppMarket('com.xtj.person');
      }
    }
  }

  void _cancelDownload() async {
    await FlutterDownloader.cancel(taskId: taskID!);
    await FlutterDownloader.remove(taskId: taskID!, shouldDeleteContent: true);
    ToastUtils.show('下载已取消');
    setState(() {
      isDownloading = false;
    });
  }

  void _installApk() async {
    if (downloadProgress != 1) {
      ToastUtils.show('请下载完成后，再安装');
      return;
    }
    await InstallPlugin.installApk(
        '${await AppInfo().upgradeDownloadDir}/$fileName');
  }

  Future<void> launchAppMarket(String packageName) async {
    final List<String> marketUrls = [
      "market://details?id=$packageName", // 通用方式
      "https://a.app.qq.com/o/simple.jsp?pkgname=$packageName", // 应用宝
      "appmarket://details?id=$packageName", // 华为
      "mimarket://details?id=$packageName", // 小米
      "vivomarket://details?id=$packageName", // VIVO
      "market://details?id=$packageName", // OPPO
    ];

    for (String url in marketUrls) {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        return;
      }
    }

    String fallbackUrl = "https://m.xtjzx.cn/app/xtjr-app-download-page/";
    await launchUrl(Uri.parse(fallbackUrl),
        mode: LaunchMode.externalApplication);
  }
}

class BulletText extends StatelessWidget {
  final String text;

  const BulletText(this.text, {super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(top: 5),
          child: Icon(Icons.circle, size: 8, color: Color(0xFF0054FF)),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 14, color: Colors.black),
          ),
        ),
      ],
    );
  }
}
