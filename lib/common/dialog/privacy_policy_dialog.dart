import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../routers/common_router.dart';
import '../../routers/navigator_utils.dart';

class PrivacyPolicyDialog extends StatelessWidget {
  final VoidCallback onAgree;
  final VoidCallback onDisagree;

  const PrivacyPolicyDialog(
      {Key? key, required this.onAgree, required this.onDisagree})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        padding: const EdgeInsets.all(15.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('温馨提示',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            const Align(
              alignment: Alignment.centerLeft,
              child: Text(
                '欢迎来到新途径人!',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 10),
            const Text('1.为了给您提供服务，我们可能会申请手机存储权限、摄像头权限；',
                style: TextStyle(fontSize: 10)),
            const Text('2.为了基于您所在的位置向您推荐内容，我们可能会申请您的位置权限；',
                style: TextStyle(fontSize: 10)),
            const Text('3.我们会努力采取各种安全技术保护您的个人信息，未经您同意，我们不会从第三方获取、共享或对外提供您的信息；',
                style: TextStyle(fontSize: 10)),
            const Text('4.您还可以访问、更正、删除您的个人信息，我们也将提供注销方式；',
                style: TextStyle(fontSize: 10)),
            const Text(
                '5.我们的产品集成友盟+SDK，友盟+SDK需要收集您的设备Mac地址、唯一设备识别码（IMEI/android ID/IDFA/OPENUDID/GUID/IP地址/SIM 卡 IMSI 信息）以提供统计分析服务，并通过地理位置校准报表数据准确性，提供基础反作弊能力',
                style: TextStyle(fontSize: 10)),
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.center,
              child: RichText(
                text: TextSpan(
                  children: [
                    const TextSpan(
                      text: '您可以阅读完整版',
                      style: TextStyle(
                          fontSize: 12,
                          color: Colors.black,
                          fontWeight: FontWeight.w500),
                    ),
                    TextSpan(
                      text: '用户协议',
                      style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xff0054ff),
                          fontWeight: FontWeight.w500),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          NavigatorUtils.push(
                              context, CommonRouter.proxyWebview,
                              arguments: {
                                'title': '用户协议',
                                'url':
                                    'https://web.xtjstatic.cn/agreement/protocol.html',
                              });
                        },
                    ),
                    const TextSpan(
                      text: '和',
                      style: TextStyle(
                          fontSize: 12,
                          color: Colors.black,
                          fontWeight: FontWeight.w500),
                    ),
                    TextSpan(
                      text: '隐私政策',
                      style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xff0054ff),
                          fontWeight: FontWeight.w500),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          NavigatorUtils.push(
                              context, CommonRouter.proxyWebview,
                              arguments: {
                                'title': '隐私政策',
                                'url':
                                    'https://web.xtjstatic.cn/agreement/privacyAgreement.htm',
                              });
                        },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: onDisagree,
                  child: const Text('不同意并退出',
                      style: TextStyle(color: Colors.grey, fontSize: 10)),
                ),
                TextButton(
                  onPressed: onAgree,
                  style: TextButton.styleFrom(
                      backgroundColor: const Color(0xff0054ff)),
                  child: const Text('同意并继续',
                      style: TextStyle(color: Colors.white, fontSize: 10)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
