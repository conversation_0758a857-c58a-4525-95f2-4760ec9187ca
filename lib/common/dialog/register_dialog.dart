import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluwx/fluwx.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/service/wechat_service.dart';

class RegisterDialog extends StatefulWidget {
  final Function(String) onClick;
  const RegisterDialog({super.key, required this.onClick});

  @override
  State<RegisterDialog> createState() => _RegisterDialogState();
}

class _RegisterDialogState extends State<RegisterDialog> {
  bool tipDisplay = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
      },
      child: Material(
        color: Colors.transparent,
        child: Center(
            child: Stack(
          children: [
            Container(
                margin: EdgeInsets.symmetric(horizontal: 26.w),
                padding: EdgeInsets.symmetric(vertical: 27.h),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Color(0xFFDBE7FF), Color(0xFFFFFFFF)])),
                child: Stack(
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '注册提示',
                          style: TextStyle(
                                  color: const Color(0xFF333333),
                                  fontSize: 17.sp)
                              .phBold,
                        ),
                        SizedBox(height: 20.h),
                        GestureDetector(
                          onTap: () {},
                          child: Container(
                            color: Colors.white,
                            padding: EdgeInsets.symmetric(
                                vertical: 13.h, horizontal: 16.w),
                            width: ScreenUtil().screenWidth,
                            child: RichText(
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              text: TextSpan(
                                text: '注: 每位新途径员工(即一个身份证号)仅能注册一个新途径人账号!  ',
                                style: TextStyle(
                                        color: AppTheme.colorBlue,
                                        fontSize: 13.sp,
                                        height: 20.sp / 13.sp)
                                    .pfRegular,
                                children: [
                                  WidgetSpan(
                                    child: GestureDetector(
                                      behavior: HitTestBehavior.opaque,
                                      onTap: () {
                                        setState(() {
                                          tipDisplay = !tipDisplay;
                                        });
                                      },
                                      child: Image.asset(
                                          'assets/png/login/register_tip.png',
                                          width: 17,
                                          height: 17),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 15.h),
                        Row(
                          children: [
                            SizedBox(width: 11.w),
                            Image.asset('assets/png/login/register_user.png',
                                width: 22, height: 22),
                            Text('老用户',
                                style: TextStyle(
                                        color: const Color(0xFF333333),
                                        fontSize: 15.sp)
                                    .phBold),
                          ],
                        ),
                        SizedBox(height: 13.h),
                        GestureDetector(
                          onTap: () {},
                          child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              child: RichText(
                                text: TextSpan(
                                  text: '如您已注册过新途径人，可点击前往',
                                  style: TextStyle(
                                          color: const Color(0xFF777777),
                                          fontSize: 13.sp,
                                          height: 21.sp / 13.sp)
                                      .pfRegular,
                                  children: [
                                    TextSpan(
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () {
                                            WeChatService()
                                                .launchWeChatMiniProgram(
                                                    userName: 'gh_e4ce8f40af72',
                                                    path: 'pages/home/<USER>');
                                          },
                                        text: '新途径人小程序',
                                        style: TextStyle(
                                                color: AppTheme.colorBlue,
                                                fontSize: 13.sp,
                                                decoration:
                                                    TextDecoration.underline,
                                                height: 21.sp / 13.sp)
                                            .pfMedium),
                                    TextSpan(
                                        text:
                                            '在【我的-个人设置】内查看登录手机号。点击下方【切换账号】使用此手机号登录App即可。',
                                        style: TextStyle(
                                                color: const Color(0xFF777777),
                                                fontSize: 13.sp,
                                                height: 21.sp / 13.sp)
                                            .pfRegular),
                                  ],
                                ),
                              )),
                        ),
                        SizedBox(height: 15.h),
                        Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Image.asset(
                                'assets/png/login/register_step_img.png',
                                fit: BoxFit.cover)),
                        SizedBox(height: 22.h),
                        Row(
                          children: [
                            SizedBox(width: 11.w),
                            Image.asset('assets/png/login/register_user.png',
                                width: 22, height: 22),
                            Text('新用户',
                                style: TextStyle(
                                        color: const Color(0xFF333333),
                                        fontSize: 15.sp)
                                    .phBold),
                          ],
                        ),
                        SizedBox(height: 13.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Text(
                              '如您未注册过新途径人小程序，请点击【新注册】按钮，提交申请后记得联系审批人为您审核。',
                              style: TextStyle(
                                      color: const Color(0xFF777777),
                                      fontSize: 13.sp,
                                      height: 21.sp / 13.sp)
                                  .pfRegular),
                        ),
                        SizedBox(height: 32.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => _handleRegister(context),
                                  child: Container(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 12.h),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFECECEC),
                                      borderRadius: BorderRadius.circular(16.r),
                                    ),
                                    child: Text(
                                      '新注册',
                                      style: TextStyle(
                                        color: const Color(0xFFB3B3B3),
                                        fontSize: 15.sp,
                                      ).pfMedium,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () => _handleExit(context),
                                  child: Container(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 12.h),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16.r),
                                      color: const Color(0xFF0054FF),
                                    ),
                                    child: Text(
                                      '切换账号',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 15.sp,
                                      ).pfMedium,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ],
                )),
            Positioned(
              top: 13.h,
              right: 30.w,
              child: Image.asset('assets/png/login/register_top_img.png',
                  width: 61, height: 61),
            ),
            if (tipDisplay)
              Positioned(
                top: 130.h,
                left: 26.w + 16.w,
                child: GestureDetector(
                  onTap: () {
                    WeChatService().launchWeChatWork();
                  },
                  child: Container(
                    width: ScreenUtil().screenWidth - 52.w - 32.w,
                    decoration: BoxDecoration(
                        color: const Color(0xFF000000).withOpacity(0.8),
                        borderRadius: BorderRadius.circular(12.sp)),
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 13.h),
                    child: RichText(
                        text: TextSpan(
                            text: '如您之前注册过新途径人小程序账号，请',
                            style: TextStyle(
                                    color: const Color(0xFFFFFFFF),
                                    fontSize: 13.sp,
                                    height: 21.sp / 13.sp)
                                .pfRegular,
                            children: [
                          TextSpan(
                              text: '联系客服',
                              style: TextStyle(
                                      color: const Color(0xFFF7B500),
                                      fontSize: 13.sp,
                                      height: 21.sp / 13.sp,
                                      decoration: TextDecoration.underline)
                                  .pfMedium),
                          TextSpan(
                              text: '将其他账号设置为当前账号的子账号(增长、分销等数据会关联至主账号)',
                              style: TextStyle(
                                      color: const Color(0xFFFFFFFF),
                                      fontSize: 13.sp,
                                      height: 21.sp / 13.sp)
                                  .pfRegular),
                        ])),
                  ),
                ),
              ),
          ],
        )),
      ),
    );
  }

  _handleRegister(BuildContext context) {
    Navigator.pop(context);
    widget.onClick('register');
  }

  _handleExit(BuildContext context) {
    Navigator.pop(context);
    AppInfo().clearLoginStatu();
    widget.onClick('exit');
  }
}

class RegisterConfirmDialog extends StatelessWidget {
  final String name;
  final Map depart;
  final String mobile;
  final String id_no;
  final String join_at;
  final String? reviewer_name;
  final Function(String) onClick;
  const RegisterConfirmDialog({
    super.key,
    required this.name,
    required this.depart,
    required this.mobile,
    required this.id_no,
    required this.onClick,
    required this.join_at,
    this.reviewer_name,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
      },
      child: Material(
        color: Colors.transparent,
        child: Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 26.w),
            padding: EdgeInsets.symmetric(horizontal: 27.w, vertical: 22.h),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.r),
                gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFFFFEEE1), Color(0xFFFFFFFF)])),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '请确认您的信息',
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 17.sp)
                          .pfSemiBold,
                ),
                SizedBox(height: 19.h),
                Column(
                  children: [
                    _itemView('员工姓名:', name),
                    SizedBox(height: 18.h),
                    _itemView('部门:', depart['name']),
                    SizedBox(height: 18.h),
                    _itemView('手机号:', mobile),
                    SizedBox(height: 18.h),
                    _itemView('身份证:', id_no),
                    SizedBox(height: 18.h),
                    _itemView('入职时间:', join_at),
                    if (reviewer_name != null) SizedBox(height: 18.h),
                    if (reviewer_name != null)
                      _itemView('审批人:', reviewer_name!),
                  ],
                ),
                SizedBox(height: 25.h),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _handleExit(context),
                        child: Container(
                          height: 44.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: const Color(0xFFECECEC),
                            borderRadius: BorderRadius.circular(16.r),
                          ),
                          child: Text(
                            '取消',
                            style: TextStyle(
                              color: const Color(0xFFB3B3B3),
                              fontSize: 15.sp,
                            ).pfMedium,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _handleRegister(context),
                        child: Container(
                          height: 44.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.r),
                            color: const Color(0xFF0054FF),
                          ),
                          child: Text(
                            '确定',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 15.sp,
                            ).pfMedium,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _itemView(String title, String content) {
    return Row(
      children: [
        Container(
          width: 80.w,
          child: Text(
            title,
            style: TextStyle(color: const Color(0xFF808080), fontSize: 15.sp)
                .pfMedium,
          ),
        ),
        Expanded(
            child: Text(
          content,
          style: TextStyle(color: const Color(0xFF333333), fontSize: 15.sp)
              .pfMedium,
        ))
      ],
    );
  }

  _handleRegister(BuildContext context) {
    Navigator.pop(context);
    onClick('register');
  }

  _handleExit(BuildContext context) {
    Navigator.pop(context);
  }
}

//老用户完善信息弹框
class ImproveDialog extends StatelessWidget {
  final void Function() onClick;
  const ImproveDialog({
    super.key,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
      },
      child: Material(
        color: Colors.transparent,
        child: Center(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 26.w),
            padding: EdgeInsets.symmetric(horizontal: 34.w, vertical: 22.h),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(20.r)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '提示',
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 17.sp)
                          .pfSemiBold,
                ),
                SizedBox(height: 19.h),
                Text('发现您之前有注册过新途径人账号，请完善信息',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                            color: const Color(0xFF666666), fontSize: 15.sp)
                        .pfRegular),
                SizedBox(height: 25.h),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _handleExit(context),
                        child: Container(
                          height: 44.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: const Color(0xFFECECEC),
                            borderRadius: BorderRadius.circular(16.r),
                          ),
                          child: Text(
                            '取消',
                            style: TextStyle(
                              color: const Color(0xFFB3B3B3),
                              fontSize: 15.sp,
                            ).pfMedium,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _handleRegister(context),
                        child: Container(
                          height: 44.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.r),
                            color: const Color(0xFF0054FF),
                          ),
                          child: Text(
                            '去完善',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 15.sp,
                            ).pfMedium,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _handleRegister(BuildContext context) {
    Navigator.pop(context);
    onClick();
  }

  _handleExit(BuildContext context) {
    Navigator.pop(context);
  }
}
