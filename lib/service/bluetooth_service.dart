import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

/// 蓝牙服务单例类
/// 封装flutter_blue_plus插件，提供蓝牙功能API给WebView使用
class BluetoothService {
  final String _logTag = "BluetoothService"; // 特殊的日志标识符

  // 单例实例
  static final BluetoothService _instance = BluetoothService._internal();

  // 工厂构造函数返回单例实例
  factory BluetoothService() {
    return _instance;
  }

  // 私有构造函数
  BluetoothService._internal() {
    _init();
  }

  // 当前连接的设备
  BluetoothDevice? _connectedDevice;

  // 扫描结果缓存
  List<ScanResult> _scanResults = [];

  // 扫描状态
  bool _isScanning = false;

  // 扫描结果流控制器
  final StreamController<String> _scanResultsController =
      StreamController<String>.broadcast();

  // 扫描结果流
  Stream<String> get scanResultsStream => _scanResultsController.stream;

  // 初始化
  Future<void> _init() async {
    // 设置日志级别
    FlutterBluePlus.setLogLevel(LogLevel.info, color: false);

    // 监听蓝牙状态变化
    FlutterBluePlus.adapterState.listen((state) {
      debugPrint('$_logTag 蓝牙状态: $state');
    });

    // 监听扫描结果
    FlutterBluePlus.scanResults.listen((results) {
      _scanResults = results;
      _sendScanResultsUpdate();
    }, onError: (e) {
      debugPrint('$_logTag 扫描监听错误: $e');
    });
  }

  // 发送扫描结果更新
  void _sendScanResultsUpdate() {
    try {
      // 转换为JSON格式
      List<Map<String, dynamic>> devices = _scanResults.map((result) {
        return {
          'id': result.device.remoteId.str,
          'name': result.device.platformName.isNotEmpty
              ? result.device.platformName
              : result.advertisementData.advName.isNotEmpty
                  ? result.advertisementData.advName
                  : '未知设备',
          'rssi': result.rssi,
          'connectable': result.advertisementData.connectable,
          'isConnected': result.device.isConnected,
        };
      }).toList();

      final resultJson =
          jsonEncode({'code': 0, 'data': devices, 'msg': '扫描结果更新'});

      _scanResultsController.add(resultJson);
    } catch (e) {
      debugPrint('$_logTag 发送扫描结果更新失败: $e');
    }
  }

  /// 检查蓝牙权限
  Future<bool> _checkPermissions() async {
    if (Platform.isAndroid) {
      // Android 12及以上需要蓝牙扫描和连接权限
      if (await Permission.bluetoothScan.status != PermissionStatus.granted) {
        final status = await Permission.bluetoothScan.request();
        if (status != PermissionStatus.granted) {
          return false;
        }
      }

      if (await Permission.bluetoothConnect.status !=
          PermissionStatus.granted) {
        final status = await Permission.bluetoothConnect.request();
        if (status != PermissionStatus.granted) {
          return false;
        }
      }
    }

    return true;
  }

  /// 检查蓝牙是否支持
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> isBluetoothSupported() async {
    try {
      bool isSupported = await FlutterBluePlus.isSupported;
      return jsonEncode({
        'code': 0,
        'data': isSupported,
        'msg': isSupported ? '设备支持蓝牙' : '设备不支持蓝牙'
      });
    } catch (e) {
      debugPrint('$_logTag 检查蓝牙支持失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '检查蓝牙支持失败: $e'});
    }
  }

  /// 获取蓝牙状态
  /// 返回JSON格式: {"code": 0, "data": "on/off/unknown/unavailable/unauthorized", "msg": ""}
  Future<String> getBluetoothState() async {
    try {
      BluetoothAdapterState state = FlutterBluePlus.adapterStateNow;
      String stateStr = state.toString().split('.').last;
      return jsonEncode(
          {'code': 0, 'data': stateStr, 'msg': '蓝牙状态: $stateStr'});
    } catch (e) {
      debugPrint('$_logTag 获取蓝牙状态失败: $e');
      return jsonEncode({'code': -1, 'data': 'unknown', 'msg': '获取蓝牙状态失败: $e'});
    }
  }

  /// 打开蓝牙（仅Android）
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> turnOnBluetooth() async {
    try {
      if (!Platform.isAndroid) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '只有Android支持此功能'});
      }

      await FlutterBluePlus.turnOn();
      return jsonEncode({'code': 0, 'data': true, 'msg': '蓝牙已开启'});
    } catch (e) {
      debugPrint('$_logTag 开启蓝牙失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '开启蓝牙失败: $e'});
    }
  }

  /// 开始扫描蓝牙设备
  /// 参数:
  /// - timeout: 扫描超时时间（秒）
  /// - withServices: 按服务UUID过滤
  /// - withNames: 按设备名称过滤
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> startScan(
      {int? timeout,
      List<String>? withServices,
      List<String>? withNames}) async {
    try {
      // 检查权限
      if (!await _checkPermissions()) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '蓝牙权限被拒绝'});
      }

      // 检查蓝牙是否开启
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '蓝牙未开启'});
      }

      // 如果已经在扫描，先停止
      if (_isScanning) {
        await FlutterBluePlus.stopScan();
      }

      // 清空扫描结果
      _scanResults = [];

      // 转换服务UUID
      List<Guid> services = [];
      if (withServices != null && withServices.isNotEmpty) {
        services = withServices.map((s) => Guid(s)).toList();
      }

      // 转换设备名称
      List<String> names = [];
      if (withNames != null && withNames.isNotEmpty) {
        names = withNames;
      }

      // 开始扫描
      await FlutterBluePlus.startScan(
        timeout: timeout == null ? null : Duration(seconds: timeout),
        withServices: services,
        withNames: names,
        androidUsesFineLocation: true,
      );

      _isScanning = true;

      return jsonEncode({'code': 0, 'data': true, 'msg': '开始扫描蓝牙设备'});
    } catch (e) {
      debugPrint('$_logTag 开始扫描失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '开始扫描失败: $e'});
    }
  }

  /// 停止扫描蓝牙设备
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> stopScan() async {
    try {
      await FlutterBluePlus.stopScan();
      _isScanning = false;
      return jsonEncode({'code': 0, 'data': true, 'msg': '停止扫描蓝牙设备'});
    } catch (e) {
      debugPrint('$_logTag 停止扫描失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '停止扫描失败: $e'});
    }
  }

  /// 获取扫描结果
  /// 返回JSON格式: {"code": 0, "data": [设备列表], "msg": ""}
  Future<String> getScanResults() async {
    try {
      // 获取最新的扫描结果
      _scanResults = FlutterBluePlus.lastScanResults;

      // 转换为JSON格式
      List<Map<String, dynamic>> devices = _scanResults.map((result) {
        return {
          'id': result.device.remoteId.str,
          'name': result.device.platformName.isNotEmpty
              ? result.device.platformName
              : result.advertisementData.advName.isNotEmpty
                  ? result.advertisementData.advName
                  : '未知设备',
          'rssi': result.rssi,
          'connectable': result.advertisementData.connectable,
          'manufacturerData':
              result.advertisementData.manufacturerData.isNotEmpty
                  ? base64Encode(
                      result.advertisementData.manufacturerData.values.first)
                  : null,
          'serviceUuids': result.advertisementData.serviceUuids
              .map((e) => e.toString())
              .toList(),
          'serviceData': result.advertisementData.serviceData.map(
              (key, value) => MapEntry(key.toString(), base64Encode(value))),
        };
      }).toList();

      return jsonEncode({'code': 0, 'data': devices, 'msg': '获取扫描结果成功'});
    } catch (e) {
      debugPrint('$_logTag 获取扫描结果失败: $e');
      return jsonEncode({'code': -1, 'data': [], 'msg': '获取扫描结果失败: $e'});
    }
  }

  /// 连接到蓝牙设备
  /// 参数:
  /// - deviceId: 设备ID
  /// - autoConnect: 是否自动重连
  /// - timeout: 连接超时时间（秒）
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> connectToDevice(String deviceId,
      {bool autoConnect = false, int? timeout}) async {
    try {
      // 查找设备
      BluetoothDevice? device;
      for (var result in _scanResults) {
        if (result.device.remoteId.str == deviceId) {
          device = result.device;
          break;
        }
      }

      // 如果在扫描结果中找不到，尝试直接创建设备
      device ??= BluetoothDevice.fromId(deviceId);

      // 显示加载提示
      EasyLoading.show(status: '正在连接设备...');

      // 连接设备
      if (timeout != null) {
        await device.connect(
          autoConnect: autoConnect,
          timeout: Duration(seconds: timeout),
        );
      } else {
        await device.connect(
          autoConnect: autoConnect,
        );
      }

      // 等待连接成功
      await device.connectionState
          .where((state) => state == BluetoothConnectionState.connected)
          .first
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () => throw TimeoutException('连接超时'),
          );

      _connectedDevice = device;

      EasyLoading.dismiss();

      return jsonEncode({'code': 0, 'data': true, 'msg': '连接设备成功'});
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint('$_logTag 连接设备失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '连接设备失败: $e'});
    }
  }

  /// 断开蓝牙设备连接
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> disconnectDevice() async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '没有连接的设备'});
      }

      await _connectedDevice!.disconnect();
      _connectedDevice = null;

      return jsonEncode({'code': 0, 'data': true, 'msg': '断开设备连接成功'});
    } catch (e) {
      debugPrint('$_logTag 断开设备连接失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '断开设备连接失败: $e'});
    }
  }

  /// 获取当前连接的设备信息
  /// 返回JSON格式: {"code": 0, "data": {设备信息}, "msg": ""}
  Future<String> getConnectedDevice() async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': null, 'msg': '没有连接的设备'});
      }

      Map<String, dynamic> deviceInfo = {
        'id': _connectedDevice!.remoteId.str,
        'name': _connectedDevice!.platformName.isNotEmpty
            ? _connectedDevice!.platformName
            : '未知设备',
        'mtu': _connectedDevice!.mtuNow,
        'isConnected': _connectedDevice!.isConnected,
      };

      return jsonEncode({'code': 0, 'data': deviceInfo, 'msg': '获取连接设备信息成功'});
    } catch (e) {
      debugPrint('$_logTag 获取连接设备信息失败: $e');
      return jsonEncode({'code': -1, 'data': null, 'msg': '获取连接设备信息失败: $e'});
    }
  }

  /// 发现设备服务
  /// 返回JSON格式: {"code": 0, "data": [服务列表], "msg": ""}
  Future<String> discoverServices() async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': null, 'msg': '没有连接的设备'});
      }

      // 显示加载提示
      EasyLoading.show(status: '正在发现服务...');

      // 发现服务
      await _connectedDevice!.discoverServices();

      // 转换为JSON格式
      List<Map<String, dynamic>> servicesList = [];

      // 手动遍历服务和特征，避免类型问题
      try {
        for (var service in _connectedDevice!.servicesList) {
          Map<String, dynamic> serviceMap = {
            'uuid': service.uuid.toString(),
            'isPrimary': true, // 假设所有服务都是主服务
            'characteristics': <Map<String, dynamic>>[],
          };

          for (var characteristic in service.characteristics) {
            Map<String, dynamic> characteristicMap = {
              'uuid': characteristic.uuid.toString(),
              'properties': {
                'broadcast': characteristic.properties.broadcast,
                'read': characteristic.properties.read,
                'writeWithoutResponse':
                    characteristic.properties.writeWithoutResponse,
                'write': characteristic.properties.write,
                'notify': characteristic.properties.notify,
                'indicate': characteristic.properties.indicate,
                'authenticatedSignedWrites':
                    characteristic.properties.authenticatedSignedWrites,
                'extendedProperties':
                    characteristic.properties.extendedProperties,
                'notifyEncryptionRequired':
                    characteristic.properties.notifyEncryptionRequired,
                'indicateEncryptionRequired':
                    characteristic.properties.indicateEncryptionRequired,
              },
              'descriptors': <Map<String, dynamic>>[],
              'lastValue': characteristic.lastValue.isNotEmpty
                  ? base64Encode(characteristic.lastValue)
                  : null,
            };

            for (var descriptor in characteristic.descriptors) {
              characteristicMap['descriptors'].add({
                'uuid': descriptor.uuid.toString(),
                'lastValue': descriptor.lastValue.isNotEmpty
                    ? base64Encode(descriptor.lastValue)
                    : null,
              });
            }

            serviceMap['characteristics'].add(characteristicMap);
          }

          servicesList.add(serviceMap);
        }
      } catch (e) {
        debugPrint('$_logTag 处理服务数据失败: $e');
      }

      EasyLoading.dismiss();

      return jsonEncode({'code': 0, 'data': servicesList, 'msg': '发现服务成功'});
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint('$_logTag 发现服务失败: $e');
      return jsonEncode({'code': -1, 'data': null, 'msg': '发现服务失败: $e'});
    }
  }

  /// 读取特征值
  /// 参数:
  /// - serviceUuid: 服务UUID
  /// - characteristicUuid: 特征UUID
  /// 返回JSON格式: {"code": 0, "data": "base64编码的特征值", "msg": ""}
  Future<String> readCharacteristic(
      String serviceUuid, String characteristicUuid) async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': null, 'msg': '没有连接的设备'});
      }

      // 获取服务
      BluetoothService? service = await _findService(serviceUuid);
      if (service == null) {
        return jsonEncode(
            {'code': -1, 'data': null, 'msg': '未找到服务: $serviceUuid'});
      }

      // 获取特征
      BluetoothCharacteristic? characteristic =
          _findCharacteristic(service, characteristicUuid);
      if (characteristic == null) {
        return jsonEncode(
            {'code': -1, 'data': null, 'msg': '未找到特征: $characteristicUuid'});
      }

      // 检查是否可读
      if (!characteristic.properties.read) {
        return jsonEncode({'code': -1, 'data': null, 'msg': '特征不支持读取操作'});
      }

      // 读取特征值
      List<int> value = await characteristic.read();

      return jsonEncode(
          {'code': 0, 'data': base64Encode(value), 'msg': '读取特征值成功'});
    } catch (e) {
      debugPrint('$_logTag 读取特征值失败: $e');
      return jsonEncode({'code': -1, 'data': null, 'msg': '读取特征值失败: $e'});
    }
  }

  /// 写入特征值
  /// 参数:
  /// - serviceUuid: 服务UUID
  /// - characteristicUuid: 特征UUID
  /// - value: base64编码的值
  /// - withoutResponse: 是否不需要响应
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> writeCharacteristic(
      String serviceUuid, String characteristicUuid, String value,
      {bool withoutResponse = false}) async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '没有连接的设备'});
      }

      // 获取服务
      BluetoothService? service = await _findService(serviceUuid);
      if (service == null) {
        return jsonEncode(
            {'code': -1, 'data': false, 'msg': '未找到服务: $serviceUuid'});
      }

      // 获取特征
      BluetoothCharacteristic? characteristic =
          _findCharacteristic(service, characteristicUuid);
      if (characteristic == null) {
        return jsonEncode(
            {'code': -1, 'data': false, 'msg': '未找到特征: $characteristicUuid'});
      }

      // 检查是否可写
      if (withoutResponse && !characteristic.properties.writeWithoutResponse) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '特征不支持无响应写入操作'});
      } else if (!withoutResponse && !characteristic.properties.write) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '特征不支持写入操作'});
      }

      // 解码base64值
      List<int> bytes;
      try {
        bytes = base64Decode(value);
      } catch (e) {
        return jsonEncode({'code': -1, 'data': false, 'msg': 'base64解码失败: $e'});
      }

      // 写入特征值
      await characteristic.write(bytes, withoutResponse: withoutResponse);

      return jsonEncode({'code': 0, 'data': true, 'msg': '写入特征值成功'});
    } catch (e) {
      debugPrint('$_logTag 写入特征值失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '写入特征值失败: $e'});
    }
  }

  /// 设置特征通知
  /// 参数:
  /// - serviceUuid: 服务UUID
  /// - characteristicUuid: 特征UUID
  /// - enable: 是否启用通知
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> setNotification(
      String serviceUuid, String characteristicUuid, bool enable) async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '没有连接的设备'});
      }

      // 获取服务
      BluetoothService? service = await _findService(serviceUuid);
      if (service == null) {
        return jsonEncode(
            {'code': -1, 'data': false, 'msg': '未找到服务: $serviceUuid'});
      }

      // 获取特征
      BluetoothCharacteristic? characteristic =
          _findCharacteristic(service, characteristicUuid);
      if (characteristic == null) {
        return jsonEncode(
            {'code': -1, 'data': false, 'msg': '未找到特征: $characteristicUuid'});
      }

      // 检查是否支持通知
      if (!characteristic.properties.notify &&
          !characteristic.properties.indicate) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '特征不支持通知或指示操作'});
      }

      // 设置通知
      await characteristic.setNotifyValue(enable);

      return jsonEncode(
          {'code': 0, 'data': true, 'msg': enable ? '启用通知成功' : '禁用通知成功'});
    } catch (e) {
      debugPrint('$_logTag 设置通知失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '设置通知失败: $e'});
    }
  }

  /// 读取描述符值
  /// 参数:
  /// - serviceUuid: 服务UUID
  /// - characteristicUuid: 特征UUID
  /// - descriptorUuid: 描述符UUID
  /// 返回JSON格式: {"code": 0, "data": "base64编码的描述符值", "msg": ""}
  Future<String> readDescriptor(String serviceUuid, String characteristicUuid,
      String descriptorUuid) async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': null, 'msg': '没有连接的设备'});
      }

      // 获取服务
      BluetoothService? service = await _findService(serviceUuid);
      if (service == null) {
        return jsonEncode(
            {'code': -1, 'data': null, 'msg': '未找到服务: $serviceUuid'});
      }

      // 获取特征
      BluetoothCharacteristic? characteristic =
          _findCharacteristic(service, characteristicUuid);
      if (characteristic == null) {
        return jsonEncode(
            {'code': -1, 'data': null, 'msg': '未找到特征: $characteristicUuid'});
      }

      // 获取描述符
      BluetoothDescriptor? descriptor =
          _findDescriptor(characteristic, descriptorUuid);
      if (descriptor == null) {
        return jsonEncode(
            {'code': -1, 'data': null, 'msg': '未找到描述符: $descriptorUuid'});
      }

      // 读取描述符值
      List<int> value = await descriptor.read();

      return jsonEncode(
          {'code': 0, 'data': base64Encode(value), 'msg': '读取描述符值成功'});
    } catch (e) {
      debugPrint('$_logTag 读取描述符值失败: $e');
      return jsonEncode({'code': -1, 'data': null, 'msg': '读取描述符值失败: $e'});
    }
  }

  /// 写入描述符值
  /// 参数:
  /// - serviceUuid: 服务UUID
  /// - characteristicUuid: 特征UUID
  /// - descriptorUuid: 描述符UUID
  /// - value: base64编码的值
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> writeDescriptor(String serviceUuid, String characteristicUuid,
      String descriptorUuid, String value) async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': false, 'msg': '没有连接的设备'});
      }

      // 获取服务
      BluetoothService? service = await _findService(serviceUuid);
      if (service == null) {
        return jsonEncode(
            {'code': -1, 'data': false, 'msg': '未找到服务: $serviceUuid'});
      }

      // 获取特征
      BluetoothCharacteristic? characteristic =
          _findCharacteristic(service, characteristicUuid);
      if (characteristic == null) {
        return jsonEncode(
            {'code': -1, 'data': false, 'msg': '未找到特征: $characteristicUuid'});
      }

      // 获取描述符
      BluetoothDescriptor? descriptor =
          _findDescriptor(characteristic, descriptorUuid);
      if (descriptor == null) {
        return jsonEncode(
            {'code': -1, 'data': false, 'msg': '未找到描述符: $descriptorUuid'});
      }

      // 解码base64值
      List<int> bytes;
      try {
        bytes = base64Decode(value);
      } catch (e) {
        return jsonEncode({'code': -1, 'data': false, 'msg': 'base64解码失败: $e'});
      }

      // 写入描述符值
      await descriptor.write(bytes);

      return jsonEncode({'code': 0, 'data': true, 'msg': '写入描述符值成功'});
    } catch (e) {
      debugPrint('$_logTag 写入描述符值失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '写入描述符值失败: $e'});
    }
  }

  /// 请求MTU值（仅Android）
  /// 参数:
  /// - mtu: 请求的MTU值
  /// 返回JSON格式: {"code": 0, "data": 实际协商的MTU值, "msg": ""}
  Future<String> requestMtu(int mtu) async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': null, 'msg': '没有连接的设备'});
      }

      if (!Platform.isAndroid) {
        return jsonEncode({'code': -1, 'data': null, 'msg': '只有Android支持此功能'});
      }

      // 请求MTU
      int negotiatedMtu = await _connectedDevice!.requestMtu(mtu);

      return jsonEncode({'code': 0, 'data': negotiatedMtu, 'msg': '请求MTU成功'});
    } catch (e) {
      debugPrint('$_logTag 请求MTU失败: $e');
      return jsonEncode({'code': -1, 'data': null, 'msg': '请求MTU失败: $e'});
    }
  }

  /// 读取RSSI值
  /// 返回JSON格式: {"code": 0, "data": RSSI值, "msg": ""}
  Future<String> readRssi() async {
    try {
      if (_connectedDevice == null) {
        return jsonEncode({'code': -1, 'data': null, 'msg': '没有连接的设备'});
      }

      // 读取RSSI
      int rssi = await _connectedDevice!.readRssi();

      return jsonEncode({'code': 0, 'data': rssi, 'msg': '读取RSSI成功'});
    } catch (e) {
      debugPrint('$_logTag 读取RSSI失败: $e');
      return jsonEncode({'code': -1, 'data': null, 'msg': '读取RSSI失败: $e'});
    }
  }

  /// 获取已连接的系统设备
  /// 返回JSON格式: {"code": 0, "data": [设备列表], "msg": ""}
  Future<String> getSystemDevices() async {
    try {
      // 获取系统设备
      List<BluetoothDevice> devices = [];
      try {
        // 尝试获取已连接的设备
        devices = await FlutterBluePlus.systemDevices([]);
      } catch (e) {
        debugPrint('$_logTag 获取系统设备失败，使用空列表: $e');
      }

      // 转换为JSON格式
      List<Map<String, dynamic>> devicesList = devices.map((device) {
        return {
          'id': device.remoteId.str,
          'name': device.platformName.isNotEmpty ? device.platformName : '未知设备',
        };
      }).toList();

      return jsonEncode({'code': 0, 'data': devicesList, 'msg': '获取系统设备成功'});
    } catch (e) {
      debugPrint('$_logTag 获取系统设备失败: $e');
      return jsonEncode({'code': -1, 'data': [], 'msg': '获取系统设备失败: $e'});
    }
  }

  /// 计算两个坐标之间的距离
  /// 参数:
  /// - startLatitude: 起始纬度
  /// - startLongitude: 起始经度
  /// - endLatitude: 结束纬度
  /// - endLongitude: 结束经度
  /// 返回JSON格式: {"code": 0, "data": 距离（米）, "msg": ""}
  Future<String> calculateDistance(double startLatitude, double startLongitude,
      double endLatitude, double endLongitude) async {
    try {
      // 计算距离
      double distance = Geolocator.distanceBetween(
        startLatitude,
        startLongitude,
        endLatitude,
        endLongitude,
      );

      return jsonEncode({'code': 0, 'data': distance, 'msg': '计算距离成功'});
    } catch (e) {
      debugPrint('$_logTag 计算距离失败: $e');
      return jsonEncode({'code': -1, 'data': null, 'msg': '计算距离失败: $e'});
    }
  }

  /// 辅助方法：查找服务
  Future<dynamic> _findService(String serviceUuid) async {
    if (_connectedDevice == null) return null;

    // 如果没有发现服务，先发现服务
    if (_connectedDevice!.servicesList.isEmpty) {
      await _connectedDevice!.discoverServices();
    }

    // 查找服务
    for (var service in _connectedDevice!.servicesList) {
      if (service.uuid.toString() == serviceUuid) {
        return service;
      }
    }

    return null;
  }

  /// 辅助方法：查找特征
  dynamic _findCharacteristic(dynamic service, String characteristicUuid) {
    try {
      for (var characteristic in service.characteristics) {
        if (characteristic.uuid.toString() == characteristicUuid) {
          return characteristic;
        }
      }
    } catch (e) {
      debugPrint('$_logTag 查找特征失败: $e');
    }

    return null;
  }

  /// 辅助方法：查找描述符
  dynamic _findDescriptor(dynamic characteristic, String descriptorUuid) {
    try {
      for (var descriptor in characteristic.descriptors) {
        if (descriptor.uuid.toString() == descriptorUuid) {
          return descriptor;
        }
      }
    } catch (e) {
      debugPrint('$_logTag 查找描述符失败: $e');
    }

    return null;
  }
}
