import 'package:flutter/material.dart';

class KRouterObserver extends NavigatorObserver {
  @override
  void didPush(Route route, Route? previousRoute) {
    print("--didPush--------${route.settings.name}");
    super.didPush(route, previousRoute);
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    print("--didReplace--------${newRoute?.settings.name}");
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    print("--didPop--------${previousRoute?.settings.name}");
    super.didPop(route, previousRoute);
  }
}
