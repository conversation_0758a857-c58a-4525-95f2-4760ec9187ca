import 'package:fluro/fluro.dart';
import 'package:npemployee/page/campus_training/campus_training_page.dart';
import 'package:npemployee/routers/irouter_provider.dart';

class CampusTrainingRouter extends IRouterProvider {
  static const campusTrainingPage =
      "/page/campus_training/campus_training_page";

  @override
  void initRouter(FluroRouter router) {
    router.define(campusTrainingPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return CampusTrainingPage(title: argument?['title']);
    }));
  }
}
