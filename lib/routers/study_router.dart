import 'dart:ffi';

import 'package:fluro/fluro.dart';
import 'package:npemployee/page/capability_upgrading/capability_audio_page.dart';
import 'package:npemployee/page/capability_upgrading/capability_detail_page.dart';
import 'package:npemployee/page/capability_upgrading/capability_study_rank_page.dart';
import 'package:npemployee/page/capability_upgrading/capability_upgrading_page.dart';
import 'package:npemployee/page/capability_upgrading/my_start_page.dart';
import 'package:npemployee/page/capability_upgrading/play_detail_page.dart';
import 'package:npemployee/page/capability_upgrading/studying_page.dart';
import 'package:npemployee/page/download/cache_audio_player.dart';
import 'package:npemployee/page/download/cache_chewie_player.dart';
import 'package:npemployee/page/download/download_detail_page.dart';
import 'package:npemployee/page/download/download_list_page.dart';
import 'package:npemployee/page/download/my_download_page.dart';
import 'package:npemployee/page/knowledge_contest/answer_analysis_page.dart';
import 'package:npemployee/page/knowledge_contest/answer_question_page.dart';
import 'package:npemployee/page/knowledge_contest/knowledge_contest_page.dart';
import 'package:npemployee/page/knowledge_contest/transcript_page.dart';
import 'package:npemployee/page/study/annual_meeting_ppt_page.dart';
import 'package:npemployee/page/study/annual_meeting_video_page.dart';
import 'package:npemployee/page/study/company_culture_page.dart';
import 'package:npemployee/page/study/operational_tool_page.dart';
import 'package:npemployee/page/study/video_play_page.dart';
import 'package:npemployee/routers/irouter_provider.dart';

class StudyRouter extends IRouterProvider {
  static const companyCulture = "/page/home/<USER>/CompanyCulture";
  static const annualMeetingVideo = "/page/home/<USER>/AnnualMeetingVideo";
  static const annualMeetingPPT = "/page/home/<USER>/AnnualMeetingPPT";
  static const videoPlay = "/page/home/<USER>/videoPlay";
  static const knowledgeContest = '/page/home/<USER>/knowledgeContest';
  static const answerQuestion = '/page/home/<USER>/answerQuestion';
  static const transcript = '/page/home/<USER>/transcript';
  static const capabilityUpgrading = '/page/home/<USER>/capabilityUpgrading';
  static const capabilityDetail = '/page/home/<USER>/capabilityDetail';
  // static const capabilityAudioPage =
  //     '/page/capability_upgrading/capability_audio_page';
  static const capabilityStudyRank = '/page/home/<USER>/capabilityStudyRank';
  static const playDetail = '/page/home/<USER>/playDetail';
  static const downloadList = '/page/home/<USER>/downloadList';
  static const myDownload = '/page/home/<USER>/myDownload';
  static const downloadDetail = '/page/home/<USER>/downloadDetail';
  static const cacheChewiePlayer = '/page/download/study/cache_chewie_player';
  // static const cacheAudioPlayer = '/page/download/study/cache_audio_player';

  static const myStarList = '/page/home/<USER>/myStarList';
  static const studyingPage = '/page/home/<USER>/studyingPage';
  static const operationalTool = '/page/home/<USER>/operationalTool';
  static const answerAnalysis = '/page/home/<USER>/answerAnalysis';

  @override
  void initRouter(FluroRouter router) {
    router.define(companyCulture, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return CompanyCulturePage(title: argument?['title']);
    }));
    router.define(annualMeetingVideo, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return AnnualMeetingVideoPage(title: argument?['title']);
    }));
    router.define(annualMeetingPPT, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return AnnualMeetingPPTPage(title: argument?['title']);
    }));
    router.define(videoPlay, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return VideoPlayPage(model: argument?['model']);
    }));
    router.define(knowledgeContest, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return KnowledgeContestPage(title: argument?['title']);
    }));
    router.define(answerQuestion, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return AnswerQuestionPage(
        paperId: argument?['paperId'],
        paperStatus: argument?['paperStatus'],
        limitTime: argument?['limitTime'],
      );
    }));
    router.define(transcript, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return TranscriptPage(
        paperId: argument?['paperId'],
      );
    }));
    router.define(capabilityUpgrading, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return CapabilityUpgradingPage(title: argument?['title']);
    }));
    router.define(capabilityDetail, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return CapabilityDetailPage(
        courseId: argument?['courseId'],
        lessonModel: argument?['lesson'],
        lessonId: argument?['lessonId'],
        chapterId: argument?['chapterId'],
      );
    }));
    // router.define(capabilityAudioPage, handler: Handler(handlerFunc: (c, p) {
    //   Map? argument = c!.settings!.arguments as Map?;
    //   return CapabilityAudioPage(
    //       courseId: argument?['courseId'], playLesson: argument?['lesson']);
    // }));
    router.define(playDetail, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return PlayDetailPage(
        course: argument?['course'],
        lessonModel: argument?['lesson'],
        lessonId: argument?['lessonId'],
        chapterId: argument?['chapterId'],
      );
    }));
    router.define(downloadList, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return DownloadListPage(
        chapters: argument?['chapters'],
        course: argument?['course'],
        type: argument?['type'],
      );
    }));
    router.define(myDownload, handler: Handler(handlerFunc: (c, p) {
      return const MyDownloadPage();
    }));
    router.define(downloadDetail, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return DownloadDetailPage(
          course: argument?['course'],
          checkedChapters: argument?['checkedChapters']);
    }));
    router.define(cacheChewiePlayer, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return CacheChewiePlayer(fileName: argument?['fileName']);
    }));
    /*  router.define(cacheAudioPlayer, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return CacheAudioPlayer(fileName: argument?['fileName']);
    })); */

    router.define(myStarList, handler: Handler(handlerFunc: (c, p) {
      return const MyStartPage();
    }));
    router.define(capabilityStudyRank, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return CapabilityStudyRankPage(courseId: argument?['course_id']);
    }));
    router.define(studyingPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return StudyingPage(courseId: argument?['courseId']);
    }));
    router.define(operationalTool, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return OperationalToolPage(title: argument?['title']);
    }));
    router.define(answerAnalysis, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return AnswerAnalysisPage(
          questions: argument?['questions'], index: argument?['index']);
    }));
  }
}
