import 'package:fluro/fluro.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/page/image_edit_page.dart';
import 'package:npemployee/common/page/inapp_webview_page.dart';
import 'package:npemployee/common/page/photo_view_page.dart';
import 'package:npemployee/common/page/poster_change_code_webview_page.dart';
import 'package:npemployee/common/page/proxy_setting_page.dart';
import 'package:npemployee/common/page/proxy_webview_page.dart';
import 'package:npemployee/common/page/webview_screen_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/page/home/<USER>';
import 'package:npemployee/page/home/<USER>';
import 'package:npemployee/page/login/login_page.dart';
import 'package:npemployee/page/login/old_personal_info_page.dart';
import 'package:npemployee/routers/irouter_provider.dart';
import 'package:npemployee/common/page/pdf_preview_page.dart';
import 'package:npemployee/tabs.dart';

class CommonRouter extends IRouterProvider {
  static String loginPage = '/login';
  static String tabs = '/tabs';
  static String oldPersonalInfoPage = "/old_person_info";
  static String userSubPage = '/user_sub_page';
  static String guestPage = '/guest_page';

  static const photoView = '/page/home/<USER>/photoView';
  static const pdfPreviewPage = "/page/common/pdf_preview_page";
  static const imageEditPage = "/page/common/image_edit_page";
  static const proxySettingPage =
      "/page/common/proxy_setting_page"; //debug抓包使用，上线前注意关闭
  static const inAppWebViewPage = "/page/common/page/inapp_webview";
  static const webview = '/page/common/webview_flutter';
  static const proxyWebview = '/page/common/proxy_webview';
  static const posterChangeCodeWebView =
      "/page/common/poster_change_code_webview";

  @override
  void initRouter(FluroRouter router) {
    //根据不同身份设置根路由
    if (AppInfo().hasLogined) {
      if (GlobalPreferences().userInfo != null) {
        tabs = '/';
      } else if (GlobalPreferences().oldUserInfo != null) {
        loginPage = '/'; //老用户完善页面注册可返回登录页面
      } else if (GlobalPreferences().userSubInfo != null) {
        userSubPage = '/';
      } else if (GlobalPreferences().guestInfo != null) {
        guestPage = '/';
      } else {
        tabs = '/';
      }
    } else {
      loginPage = '/';
    }

    router.define(tabs, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return Tabs(index: argument?['index']);
    }));
    router.define(loginPage, handler: Handler(handlerFunc: (c, p) {
      return LoginPage();
    }));
    router.define(oldPersonalInfoPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return OldPersonalInfoPage(oldPersonModel: argument?['oldPersonModel']);
    }));
    router.define(userSubPage, handler: Handler(handlerFunc: (c, p) {
      return const UserSubPage();
    }));
    router.define(guestPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return GuestPage(
        guestModel: argument?['guestModel'],
      );
    }));

    router.define(photoView, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return PhotoViewPage(url: argument?['url']);
    }));
    router.define(pdfPreviewPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return PdfPreviewPage(
          filePath: argument?['filePath'], title: argument?['title']);
    }));
    router.define(imageEditPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return ImageEditPage(imagePath: argument?['imagePath']);
    }));
    router.define(proxySettingPage, handler: Handler(handlerFunc: (c, p) {
      return const ProxySettingPage();
    }));
    router.define(inAppWebViewPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return InappWebviewPage(url: argument?['url'], title: argument?['title']);
    }));
    router.define(webview, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      // return WebViewScreen(
      //     title: argument?['title'], selectedUrl: argument?['url']);
      return WebviewScreenPage(
        title: argument?['title'],
        url: argument?['url'],
        needLandscape: argument?['need_landscape'],
        needNav: argument?['need_nav'],
        needBackBtn: argument?['need_back_btn'],
        enableLongPress: argument?['enable_long_press'],
        keepAlive: argument?['keep_alive'],
      );
    }));

    router.define(proxyWebview, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      // return WebViewScreen(
      //     title: argument?['title'], selectedUrl: argument?['url']);
      return ProxyWebviewScreenPage(
          title: argument?['title'], url: argument?['url']);
    }));

    router.define(posterChangeCodeWebView,
        handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      // return WebViewScreen(
      //     title: argument?['title'], selectedUrl: argument?['url']);
      return PosterChangeCodeWebView(
          title: argument?['title'], url: argument?['url']);
    }));
  }
}
