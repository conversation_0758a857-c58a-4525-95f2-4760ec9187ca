import 'package:fluro/fluro.dart';
import 'package:npemployee/page/im/complaint_page.dart';
import 'package:npemployee/page/im/contacts_detail_page.dart';
import 'package:npemployee/page/im/group/add_group.dart';
import 'package:npemployee/page/im/message_detail_page.dart';
import 'package:npemployee/page/im/message_list_page.dart';
import 'package:npemployee/page/im/message_search_page.dart';
import 'package:npemployee/page/im/user_profile_page.dart';
import 'package:npemployee/page/im/user_profile_page2.dart';
import 'package:npemployee/page/im/user_profile_page3.dart';
import 'package:npemployee/routers/irouter_provider.dart';

class MessageRouter extends IRouterProvider {
  static const messageListPage = '/page/im/messageListPage';
  static const messageDetailPage = '/page/im/messageDetailPage';
  static const messageSearchPage = '/page/im/messageSearchPage';
  static const contactDetailPage = '/page/im/contactDetailPage';
  static const userProfilePage = '/page/im/userProfilePage';
  static const complaintPage = '/page/im/complaintPage';
  static const userProfilePage2 = '/page/im/userProfilePage2';
  static const userProfilePage3 = '/page/im/userProfilePage3';
  static const addGroupPage = '/page/im/group/add_group';

  @override
  void initRouter(FluroRouter router) {
    router.define(messageListPage, handler: Handler(handlerFunc: (c, p) {
      return const MessageListPage();
    }));

    router.define(messageDetailPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return MessageDetailPage(
          con: argument?['con'], findingMsg: argument?['msg']);
    }));

    router.define(messageSearchPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return MessageSearchPage(
        contacts: argument?['contacts'],
        conversation: argument?['con'],
      );
    }));

    router.define(contactDetailPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return ContactsDetailPage(contacts: argument?['contacts']);
    }));

    router.define(userProfilePage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return UserProfilePage(
          contact: argument?['contact'], user: argument?['user']);
    }));

    router.define(complaintPage, handler: Handler(handlerFunc: (c, p) {
      return const ComplaintPage();
    }));

    router.define(userProfilePage2, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return UserProfilePage2(con: argument?['con']);
    }));

    router.define(userProfilePage3, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return UserProfilePage3(userId: argument?['userId']);
    }));

    router.define(addGroupPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return ImAddGroup(groupId: argument?['groupId']);
    }));
  }
}
