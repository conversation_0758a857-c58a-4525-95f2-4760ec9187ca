import 'package:fluro/fluro.dart';
import 'package:npemployee/page/function/query_course_watch/query_course_watch_page.dart';
import 'package:npemployee/routers/irouter_provider.dart';

class FunctionRouter extends IRouterProvider {
  static const queryCourseWatch =
      "/page/function/query_course_watch/query_course_watch_page";

  @override
  void initRouter(FluroRouter router) {
    router.define(queryCourseWatch, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return QueryCourseWatchPage(title: argument?['title']);
    }));
  }
}
