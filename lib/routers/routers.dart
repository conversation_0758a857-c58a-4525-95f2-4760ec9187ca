import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:npemployee/routers/campus_training_router.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/function_router.dart';
import 'package:npemployee/routers/irouter_provider.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/not_found_page.dart';
import 'package:npemployee/routers/study_router.dart';

class Routers {
  static final List<IRouterProvider> _listRouter = [];
  static final FluroRouter router = FluroRouter();

  static void initRouters() {
    router.notFoundHandler = Handler(
        handlerFunc: (BuildContext? c, Map<String, List<String>> params) {
      return const NotFoundPage();
    });

    _listRouter.clear();

    ///各个模块路由单独管理
    _listRouter.add(CommonRouter());
    _listRouter.add(MineRouter());
    _listRouter.add(StudyRouter());
    _listRouter.add(CampusTrainingRouter());
    _listRouter.add(MessageRouter());
    _listRouter.add(FunctionRouter());

    void initRouter(IRouterProvider routerProvider) {
      routerProvider.initRouter(router);
    }

    _listRouter.forEach(initRouter);
  }
}
