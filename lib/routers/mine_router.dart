import 'package:fluro/fluro.dart';
import 'package:npemployee/page/home/<USER>';
import 'package:npemployee/page/login/personal_info_page.dart';
import 'package:npemployee/page/mine/about_us_page.dart';
import 'package:npemployee/page/mine/approval_page.dart';
import 'package:npemployee/page/mine/my_task/my_task_answer_page.dart';
import 'package:npemployee/page/mine/my_task/my_task_des_page.dart';
import 'package:npemployee/page/mine/my_task/my_task_page.dart';
import 'package:npemployee/page/mine/my_task/ready_exam_page.dart';
import 'package:npemployee/page/mine/my_task/task_answer_analysis_page.dart';
import 'package:npemployee/page/mine/my_task/task_rank_page.dart';
import 'package:npemployee/page/mine/my_task/task_score_rank_page.dart';
import 'package:npemployee/page/mine/my_task/task_transcript_page.dart';
import 'package:npemployee/page/mine/personal_settings_page.dart';
import 'package:npemployee/page/mine/studying/studying_list_page.dart';
import 'package:npemployee/page/mine/team_manager/team_introduction_edig.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_page.dart';
import 'package:npemployee/routers/irouter_provider.dart';

class MineRouter extends IRouterProvider {
  static const loginPage = "/page/home/<USER>/LoginPage";
  static const personalSettingsPage = "/page/home/<USER>/PersonalSettingsPage";
  static const approvalPage = "/page/home/<USER>/ApprovalPage";
  static const aboutUsPage = "/page/home/<USER>/AboutUsPage";
  static const personalInfoPage = "/page/home/<USER>/personalInfoPage";
  static const studyingListPage = "/page/home/<USER>/studying/studyingListPage";
  static const teamManagerPage = "/page/home/<USER>/teamManager/teamManagerPage";
  static const teamIntroductionEditPage =
      "/page/home/<USER>/teamManager/teamIntroductionEditPage";
  static const qrcodeScanPage = "/page/home/<USER>";

  static const myTaskPage = "/page/mine/my_task/my_task_page";
  static const taskDesPage = "/page/mine/my_task/my_task_des_page";
  static const readyExamPage = '/page/mine/my_task/ready_exam_page';
  static const myTaskAnswerPage = '/page/mine/my_task/my_task_answer_page';
  static const taskTranscriptPage = '/page/mine/my_task/task_transcript_page';
  static const taskRankPage = '/page/mine/my_task/task_rank_page';
  static const taskAnswerAnalysisPage =
      '/page/mine/my_task/task_answer_analysis_page';
  static const taskScoreRankPage = '/page/mine/my_task/task_scope_rank_page';

  @override
  void initRouter(FluroRouter router) {
    router.define(personalSettingsPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return PersonalSettingsPage(avatar: argument!['avatar']);
    }));

    router.define(approvalPage, handler: Handler(handlerFunc: (c, p) {
      return ApprovalPage();
    }));

    router.define(aboutUsPage, handler: Handler(handlerFunc: (c, p) {
      return AboutUsPage();
    }));

    router.define(personalInfoPage, handler: Handler(handlerFunc: (c, p) {
      // Map? argument = c!.settings!.arguments as Map?;
      // UserLoginModel model = argument?['model'];
      // return PersonalInfoPage(loginModel: model);
      return PersonalInfoPage();
    }));

    router.define(studyingListPage, handler: Handler(handlerFunc: (c, p) {
      return const StudyingListPage();
    }));
    router.define(teamManagerPage, handler: Handler(handlerFunc: (c, p) {
      return const TeamManagerPage();
    }));
    router.define(teamIntroductionEditPage,
        handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return TeamIntroductionEdig(
        department: argument?['department'],
      );
    }));

    router.define(qrcodeScanPage, handler: Handler(handlerFunc: (c, p) {
      return const QrcodeScanPage();
    }));

    router.define(myTaskPage, handler: Handler(handlerFunc: (c, p) {
      return const MyTaskPage();
    }));
    router.define(taskDesPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return MyTaskDesPage(
          mission_user_id: argument?['id'], model: argument?['model']);
    }));
    router.define(readyExamPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return ReadyExamPage(model: argument?['model']);
    }));
    router.define(myTaskAnswerPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return MyTaskAnswerPage(
          model: argument?['model'], missionModel: argument?['missionModel']);
    }));
    router.define(taskTranscriptPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return TaskTranscriptPage(
        paperDataModel: argument?['paperData'],
        paperContentModel: argument?['paperContent'],
        mission: argument?['mission'],
      );
    }));
    router.define(taskRankPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return TaskRankPage(missionId: argument?['missionId']);
    }));
    router.define(taskAnswerAnalysisPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return TaskAnswerAnalysisPage(
          questions: argument?['questions'], index: argument?['index']);
    }));
    router.define(taskScoreRankPage, handler: Handler(handlerFunc: (c, p) {
      Map? argument = c!.settings!.arguments as Map?;
      return TaskScoreRankPage(
          model: argument?['model'], mission: argument?['mission']);
    }));
  }
}
