class Code {
  /// failed
  static const dynamic FAILED = 500; // 动态类型，允许与 int 和 String 比较
  /// success
  static const dynamic SUCCESS = 200;

  /// not login
  static const dynamic FAILED_NOT_LOGIN = 401;

  /// token 失效
  static const dynamic TOKEN_EXPIRATION = 403;

  /// 请求太过频繁
  static dynamic TOO_MANY_REQUEST = 429;

  // 工具方法：比较 code 值，允许 int 和 String 的比较
  static bool equals(dynamic code, dynamic expected) {
    if (code is int && expected is int) {
      return code == expected;
    } else if (code is String && expected is String) {
      return code == expected;
    } else if (code is String && expected is int) {
      return int.tryParse(code) == expected;
    } else if (code is int && expected is String) {
      return code.toString() == expected;
    }
    return false;
  }
}
