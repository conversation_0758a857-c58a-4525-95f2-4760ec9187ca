import 'dart:developer';
import 'dart:convert'; // for utf8.encode
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/network/token_interceptors.dart';
import '../common/app_info.dart';
import 'code.dart';
import 'config.dart';

final HttpManager httpManager = HttpManager();

class HttpManager {
  static const CONTENT_TYPE_JSON = "application/json";
  static final _baseUrlAlpha = "${Config.baseUrlBeta}";
  static final _baseUrl = "${Config.baseUrlRelease}";

  static get baseUrl =>
      AppInfo.isProduction ? Config.baseUrlRelease : Config.baseUrlBeta;

  Dio dio = Dio();

  final List<Interceptor> _commonInterceptors = [
    LogInterceptor(
        requestBody: AppInfo.getInstance()!.isDebug, responseBody: false),
    TokenInterceptors(),
  ];

  HttpManager() {
    _commonInterceptors.forEach((Interceptor interceptor) {
      dio.interceptors.add(interceptor);
    });
    (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      HttpClient client = HttpClient();
      client.findProxy = (url) {
        if (!GlobalPreferences().isProxyOn) {
          // 如果 isOn 为 false，返回 'DIRECT'，表示直接连接，不使用代理
          return 'DIRECT';
        }
        String ip = GlobalPreferences().proxyIp ?? '';
        String port = GlobalPreferences().proxyPort ?? '';
        return 'PROXY $ip:$port'; // 如果 isOn 为 true，从 Cookies 中获取代理 IP 地址，并返回 `PROXY $ip:8888`，表示使用指定的代理服务器和端口
      };
      client.userAgent = null; // 清除 userAgent 设置。如果之前有自定义的用户代理字符串，这里将其设置为 null
      client.badCertificateCallback = (X509Certificate cert, String host,
              int port) =>
          true; // 设置一个回调函数来处理无效证书。在这里，所有的证书都被接受，无论其是否有效，这通常用于开发环境中测试，但不推荐在生产环境中使用
      return client;
    };
  }

  Future<ResultData> requestUrl(url,
      {params, String? method, Options? options, String? baseUrl}) async {
    _setBaseUrl(baseUrl, url);
    options ??= Options();
    options.method = method ?? HttpMethod.GET;

    log("[HttpManager fetch] ${dio.options.baseUrl}" +
        url +
        " ${params.toString()}");

    Response<Map>? response;
    try {
      if (options.method == HttpMethod.GET) {
        response = await dio.get<Map>(
          url,
          queryParameters: params,
          options: options,
        );
      } else if (options.method == HttpMethod.POST) {
        response = await dio.post<Map>(
          url,
          data: params,
          options: options,
        );
      }

      final logStr = '''
url: ${dio.options.baseUrl}$url
params: ${params == null ? "null" : params.toString()}
statusCode: ${response?.statusCode.toString() ?? 'null'}
message: ${response?.data?['msg'] ?? 'null'}
connectivity: ${await ValidatorUtils.getNetworkStatusLog()}
''';
      await ValidatorUtils.writeErrorLog(logStr);
      if (response == null || response.statusCode != 200) {
        log("HTTP error: ${response?.statusMessage}");
      } else {
        log(response.toString());
      }

      if (url == '/xtjUser/myMobile') {
        response?.data?['msg'] = 'success';
      }
      return ResultData.fromJson(response?.data! as Map<String, dynamic>);
    } on DioException catch (e) {
      log("DioError: ${e.message}");
      final logStr = '''
type: ${e.type}
url: ${dio.options.baseUrl}$url
params: ${params == null ? "null" : params.toString()}
statusCode: ${e.response?.statusCode.toString() ?? 'null'}
message: ${e.response?.statusMessage ?? 'null'}
error: ${e.error.toString()}
connectivity: ${await ValidatorUtils.getNetworkStatusLog()}
''';
      await ValidatorUtils.writeErrorLog(logStr);
      if (e.response?.statusCode == 403) {
        return ResultData(
            data: null, code: Code.TOKEN_EXPIRATION, msg: '登录过期，请重新登录');
      } else if (e.response?.statusCode == 429) {
        ToastUtils.show('请求太过频繁，请稍后重试', duration: const Duration(seconds: 1));
        return ResultData(
            data: null, code: Code.TOO_MANY_REQUEST, msg: '请求太过频繁，请稍后重试');
      } else {
        return ResultData(data: null, code: Code.FAILED, msg: '未知错误，请联系管理员');
      }
    } catch (e) {
      log("Error: ${e.toString()}");
      final logStr = '''
url: ${dio.options.baseUrl}$url
params: ${params == null ? "null" : params.toString()}
statusCode: ${response?.statusCode ?? "null"}
message: ${e.toString()}
connectivity: ${await ValidatorUtils.getNetworkStatusLog()}
''';
      await ValidatorUtils.writeErrorLog(logStr);
      return ResultData(data: null, code: Code.FAILED, msg: '请检查网络后重试');
    }
  }

  _setBaseUrl(String? baseUrl, String url) {
    if (url != null && RegExp(r"^http[s]?:\/\/|\/\/").hasMatch(url)) {
      dio.options.baseUrl = '';
    } else if (baseUrl == null || baseUrl.isEmpty) {
      dio.options.baseUrl = AppInfo.isProduction ? _baseUrl : _baseUrlAlpha;
    } else {
      dio.options.baseUrl = baseUrl;
    }
  }

  String generateMd5(String input) {
    return md5.convert(utf8.encode(input)).toString();
  }
}

class HttpMethod {
  static const String GET = 'GET';
  static const String POST = 'POST';
}
