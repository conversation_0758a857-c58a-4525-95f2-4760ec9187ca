/// <AUTHOR>
/// @project FlutterKit
/// @date 12/7/23

import 'dart:convert';
import 'dart:math';
import 'package:convert/convert.dart';
import 'package:crypto/crypto.dart';


class SecurityUtils {
  static String generateNonce() {
    var random = Random();
    var values = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Url.encode(values);
  }

  static String generateTimestamp() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  static String generateSignature(Map<String, dynamic> params, String secretKey) {
    var data = params['nonce'] + params['timestamp'];
    var hmacSha256 = Hmac(sha256, utf8.encode(secretKey));
    var digest = hmacSha256.convert(utf8.encode(data));
    return hex.encode(digest.bytes); // Use Hex encoding
  }
}
