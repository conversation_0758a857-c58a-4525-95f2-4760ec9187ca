class ResultData {
  dynamic data; // 可以是 Map、List、String 或 null
  dynamic code; // 这里使用 dynamic 以兼容 String 和 int
  dynamic count;
  String msg;
  dynamic user_type;
  dynamic found; //查找手机号
  dynamic mobile; //查找手机号

  ResultData({
    this.data,
    required this.code,
    this.count,
    required this.msg,
    this.user_type,
    this.found,
    this.mobile,
  });

  factory ResultData.fromJson(Map<String, dynamic> json) {
    return ResultData(
      data: json['data'],
      code: json['code'], // 无论是 int 还是 String 都可以接收
      msg: json['msg'],
      count: json['count'],
      user_type: json['user_type'],
      found: json['found'],
      mobile: json['mobile'],
    );
  }

  // 获取标准化的 code，确保返回 int 类型
  int getCode() {
    if (code is int) {
      return code as int;
    } else if (code is String) {
      return int.tryParse(code as String) ?? -1; // 转换为 int，失败则返回 -1
    } else {
      return -1; // 非预期类型，返回 -1
    }
  }
}
