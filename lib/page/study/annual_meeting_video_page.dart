import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/study/annual_meeting_video_tag_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/annual_meeting_video_item.dart';

class AnnualMeetingVideoPage extends StatefulWidget {
  final String title;
  const AnnualMeetingVideoPage({super.key, required this.title});

  @override
  State<AnnualMeetingVideoPage> createState() => _AnnualMeetingVideoPageState();
}

class _AnnualMeetingVideoPageState extends State<AnnualMeetingVideoPage> {
  List years = [];
  String? dropValue;

  AnnualMeetingVideoTagModel? tagModel;
  List<CourseListModel> videos = [];

  final EasyRefreshController _refreshController = EasyRefreshController();

  void _refreshUI(bool isCache) {
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _getTags() {
    UserServiceProvider().getAnnualMeetingVideoTags(
      cacheCallBack: (data) {
        _formatTagsData(data, true);
      },
      successCallBack: (data) {
        _formatTagsData(data, false);
      },
      errorCallBack: (data) {},
    );
  }

  void _getAnnualMeetingVideoList({String? keyword, int? tag_id}) {
    UserServiceProvider().getAnnualMeetingVideoList(
      tag_id: tag_id,
      cacheCallBack: (value) {
        _formatAnnualMeetingVideoData(value, true);
      },
      successCallBack: (value) {
        _formatAnnualMeetingVideoData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  _formatTagsData(ResultData data, bool isCache) {
    List datas = data.data;
    if (datas.isNotEmpty) {
      tagModel = AnnualMeetingVideoTagModel.fromJson(data.data.first);
      years = tagModel!.edges.map((e) => e.name).toList();
      dropValue ??= years.first;
      if (!isCache) {
        int index = tagModel!.edges.indexWhere((e) => e.name == dropValue);
        _getAnnualMeetingVideoList(tag_id: tagModel?.edges[index].id);
      }
    }
    _refreshUI(isCache);
  }

  _formatAnnualMeetingVideoData(ResultData? value, bool isCache) {
    videos.clear();
    for (var e in value?.data) {
      videos.add(CourseListModel.formJson(e));
    }
    _refreshUI(isCache);
  }

  @override
  void initState() {
    super.initState();
    _getTags();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FD),
      appBar: CommonNav(
        title: widget.title,
        rightWidget: [
          Container(
            margin: EdgeInsets.only(right: 13.w),
            padding: EdgeInsets.fromLTRB(13.w, 6.h, 1.w, 6.h),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.r),
                color: const Color(0xFFF5F5F5)),
            child: DropdownButton(
              alignment: AlignmentDirectional.bottomStart,
              isDense: true,
              value: dropValue,
              iconSize: 18,
              borderRadius: BorderRadius.circular(16.r),
              dropdownColor: const Color.fromRGBO(0, 0, 0, 0.6),
              underline: const SizedBox.shrink(),
              selectedItemBuilder: (context) {
                return years.map((e) {
                  return Text(e,
                      style: TextStyle(
                              color: const Color(0xFF333333), fontSize: 14.sp)
                          .pfMedium);
                }).toList();
              },
              items: years.map((e) {
                return DropdownMenuItem(
                    value: e,
                    child: Text(
                      '$e',
                      style: TextStyle(
                              color: e == dropValue
                                  ? const Color(0xFF85ADFF)
                                  : Colors.white,
                              fontSize: 14.sp)
                          .pfMedium,
                    ));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  dropValue = value as String;
                });
                int index =
                    tagModel!.edges.indexWhere((e) => e.name == dropValue);
                _getAnnualMeetingVideoList(tag_id: tagModel?.edges[index].id);
              },
            ),
          ),
        ],
      ),
      body: EasyRefresh.builder(
        controller: _refreshController,
        onRefresh: () => _getTags(),
        childBuilder: (_, physic) => videos.isEmpty
            ? NoDataPage(physics: physic)
            : ListView.builder(
                physics: physic,
                itemCount: videos.length,
                itemBuilder: (_, index) {
                  return AnnualMeetingVideoItem(item: videos[index]);
                }),
      ),
    );
  }
}
