import 'dart:async';

import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/course_rank_model.dart';
import 'package:npemployee/model/study/depart_rank_model.dart';
import 'package:npemployee/model/study/department_tree_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/login/depart_tree_dialog.dart';
import 'package:npemployee/widget/study/all_moth_week_day_item.dart';
import 'package:npemployee/widget/study/department_list_item.dart';
import 'package:npemployee/widget/study/person_list_item.dart';
import 'package:npemployee/widget/study/top_tab_item.dart';

class StudyRankPage extends StatefulWidget {
  const StudyRankPage({super.key});

  @override
  State<StudyRankPage> createState() => _StudyRankPageState();
}

class _StudyRankPageState extends State<StudyRankPage> {
  int topTabIndex = 1; //1-个人榜 2-部门榜
  int allMonthWeekDayIndex = 1; //总榜/月榜/周榜/日榜下标
  List departments = []; //部门列表
  DepartmentModel? currentDepartment;
  // bool isDepartment = false;
  bool departIsOpen = false;
  List<DepartRankModel> departRankList = [];
  List<CourseRankModel> courseRankList = [];
  int page = 1;
  int size = 20;

  StreamSubscription? _tabSubscription;

  final EasyRefreshController _refreshController = EasyRefreshController(
      controlFinishRefresh: true, controlFinishLoad: true);

  get isDepartment => currentDepartment?.name != '各部门';

  @override
  void initState() {
    super.initState();
    currentDepartment = DepartmentModel.formJson({'name': '各部门', 'id': null});
    _subscriptionTabChangeBloc();
    _getAllData();
  }

  _getAllData() {
    _getData();
    page = 1;
    if (topTabIndex == 1) {
      _getCourseRankList();
    } else {
      _getDepartRankList();
    }
  }

  _getMoreData() {
    page++;
    if (topTabIndex == 1) {
      _getCourseRankList();
    } else {
      _getDepartRankList();
    }
  }

  void _subscriptionTabChangeBloc() {
    _tabSubscription?.cancel();
    _tabSubscription = BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change && state.page == 98) {
        print("-- 进入学习榜页面 ${state.page}");
        _getAllData();
      }
    });
  }

//获取部门列表
  void _getData() async {
    UserServiceProvider().getDepartmentList().then((value) {
      if (value?.code == 1) {
        EasyLoading.showError(value?.msg ?? '获取失败');
        return;
      }
      departments.clear();
      List datas = value?.data;
      datas.insert(0, {
        'children': null,
        'department': {'name': '各部门', 'id': null}
      });
      departments = datas;
      if (mounted) {
        setState(() {});
      }
    });
  }

//获取课程排名列表
  void _getCourseRankList() async {
    Map? params;
    if (allMonthWeekDayIndex == 2) {
      params = DateTimeUtils.getCurrentMonth();
    } else if (allMonthWeekDayIndex == 3) {
      params = DateTimeUtils.getCurrentWeek();
    } else if (allMonthWeekDayIndex == 4) {
      params = DateTimeUtils.getToday();
    } else {
      params = null;
    }
    UserServiceProvider().getCourseRankList(
        start: params?['start'],
        page: page,
        size: size,
        cacheCallBack: (value) {
          // _formatCourseRankData(value, true);
        },
        successCallBack: (value) {
          _formatCourseRankData(value, false);
        },
        errorCallBack: (value) {});
  }

  void _formatCourseRankData(ResultData? value, bool isCache) {
    if (page == 1) {
      _refreshController.finishRefresh();
      _refreshController.resetFooter();
      courseRankList.clear();
    } else {
      if ((value?.data ?? []).length < size) {
        _refreshController.finishLoad(IndicatorResult.noMore);
      } else {
        _refreshController.finishLoad(IndicatorResult.success);
      }
    }
    for (Map element in value?.data) {
      courseRankList.add(CourseRankModel(
        total_duration: element['total_duration'],
        today_duration: element['today_duration'],
        ranking: element['ranking'],
        user: element['user'],
      ));
    }

    if (mounted) {
      setState(() {});
    }
  }

//获取部门排名列表
  void _getDepartRankList() async {
    Map params = {};
    if (allMonthWeekDayIndex == 2) {
      params = DateTimeUtils.getCurrentMonth();
    } else if (allMonthWeekDayIndex == 3) {
      params = DateTimeUtils.getCurrentWeek();
    } else if (allMonthWeekDayIndex == 4) {
      params = DateTimeUtils.getToday();
    }
    if (isDepartment) {
      params['department_id'] = currentDepartment?.id;
    }
    UserServiceProvider().getDepartRankList(
        departId: params['department_id'],
        start: params['start'],
        page: page,
        size: size,
        cacheCallBack: (value) {
          // _formatDepartRankData(value, true);
        },
        successCallBack: (value) {
          _formatDepartRankData(value, false);
        },
        errorCallBack: (value) {});
  }

  _formatDepartRankData(ResultData? value, bool isCache) {
    if (page == 1) {
      _refreshController.finishRefresh();
      _refreshController.resetFooter();
      departRankList.clear();
    } else {
      if ((value?.data ?? []).length < size) {
        _refreshController.finishLoad(IndicatorResult.noMore);
      } else {
        _refreshController.finishLoad(IndicatorResult.success);
      }
    }
    for (Map element in value?.data) {
      if (isDepartment) {
        departRankList.add(DepartRankModel(
          department_id: element['department_id'],
          name: element['name'],
          num: element['num'],
          total_duration: element['total_duration'],
          avg_duration: element['avg_duration'],
          user_id: element['user_id'],
        ));
      } else {
        departRankList.add(DepartRankModel(
          department_id: element['department_id'],
          name: element['name'],
          num: element['num'],
          total_duration: element['total_duration'],
          avg_duration: element['avg_duration'],
        ));
      }
    }

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

// 总榜，月榜，周榜，日榜切换
  void _allMonthWeekDayChange(int i) {
    setState(() {
      allMonthWeekDayIndex = i;
    });
    _getAllData();
  }

  void _chooseDepartment() {
    setState(() {
      departIsOpen = !departIsOpen;
    });
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return FractionallySizedBox(
          heightFactor: 0.7,
          child: DepartTreeDialog(
            datas: departments,
            selected: currentDepartment,
            onDismiss: () {
              setState(() {
                departIsOpen = !departIsOpen;
              });
            },
            onDepartChange: (DepartmentModel? model) {
              setState(() {
                currentDepartment = model;
              });
              _getAllData();
            },
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _tabSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 19.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 22.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TopTabItem(
                  onTap: () {
                    setState(() {
                      topTabIndex = 1;
                    });
                    _getAllData();
                  },
                  isSelect: topTabIndex == 1,
                  svgPath: topTabIndex == 1
                      ? 'assets/png/study/person_check.png'
                      : 'assets/png/study/person_uncheck.png'),
              TopTabItem(
                onTap: () {
                  setState(() {
                    topTabIndex = 2;
                  });
                  _getAllData();
                },
                isSelect: topTabIndex == 2,
                svgPath: topTabIndex == 2
                    ? 'assets/png/study/depart_check.png'
                    : 'assets/png/study/depart_uncheck.png',
              ),
            ],
          ),
        ),
        SizedBox(height: 9.h),
        Row(
          mainAxisAlignment: topTabIndex == 2
              ? MainAxisAlignment.spaceBetween
              : MainAxisAlignment.center,
          children: [
            if (topTabIndex == 2)
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  _chooseDepartment();
                },
                child: Container(
                  padding: const EdgeInsets.all(6),
                  margin: EdgeInsets.only(left: 16.w),
                  child: Row(
                    children: [
                      Container(
                        constraints: BoxConstraints(
                            maxWidth: ScreenUtil().screenWidth - 12 - 297.w),
                        child: Text(
                          currentDepartment?.name ?? '',
                          style: TextStyle(
                                  color: const Color(0xFF242424),
                                  fontSize: 13.sp,
                                  overflow: TextOverflow.ellipsis)
                              .pfMedium,
                        ),
                      ),
                      Image.asset(
                        departIsOpen
                            ? 'assets/png/xiaoxin/drop_up.png'
                            : 'assets/png/xiaoxin/drop_down.png',
                        width: 15.w,
                        height: 10.h,
                      ),
                    ],
                  ),
                ),
              ),
            Container(
              margin: EdgeInsets.only(right: 16.w),
              width: 250.w,
              padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFF7F8FD),
                  borderRadius: BorderRadius.circular(17.5.r)),
              child: Row(
                children: [
                  AllMonthWeekDayItem(
                      onTap: () => _allMonthWeekDayChange(1),
                      title: '总榜',
                      isSelect: allMonthWeekDayIndex == 1),
                  AllMonthWeekDayItem(
                      onTap: () => _allMonthWeekDayChange(2),
                      title: '月榜',
                      isSelect: allMonthWeekDayIndex == 2),
                  AllMonthWeekDayItem(
                      onTap: () => _allMonthWeekDayChange(3),
                      title: '周榜',
                      isSelect: allMonthWeekDayIndex == 3),
                  AllMonthWeekDayItem(
                      onTap: () => _allMonthWeekDayChange(4),
                      title: '日榜',
                      isSelect: allMonthWeekDayIndex == 4),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 17.h),
        Expanded(
            child: MediaQuery.removePadding(
                context: context,
                removeTop: true,
                child: EasyRefresh.builder(
                  controller: _refreshController,
                  onRefresh: () => _getAllData(),
                  onLoad: () => _getMoreData(),
                  childBuilder: (_, pyhic) => (topTabIndex == 1 &&
                              courseRankList.isEmpty) ||
                          (topTabIndex == 2 && departRankList.isEmpty)
                      ? NoDataPage(physics: pyhic)
                      : ListView.builder(
                          physics: pyhic,
                          itemCount: topTabIndex == 1
                              ? courseRankList.length + 1
                              : departRankList.length + 1,
                          itemBuilder: (_, index) {
                            if (topTabIndex == 1) {
                              if (index == 0) {
                                final findIndex = courseRankList.indexWhere(
                                    (e) =>
                                        e.userId ==
                                        GlobalPreferences().userInfo?.user.id);
                                return PersonListMeItem(
                                    item: findIndex == -1
                                        ? null
                                        : courseRankList[findIndex],
                                    sort: findIndex == -1 ? null : index);
                              } else {
                                CourseRankModel model =
                                    courseRankList[index - 1];
                                return PersonListItem(
                                    item: model, index: index);
                              }
                            } else {
                              if (index == 0) {
                                return DepartmentListFirstItem(
                                    isDepartment: isDepartment,
                                    departmentName: currentDepartment!.name!);
                              } else {
                                DepartRankModel departmentItem =
                                    departRankList[index - 1];
                                return DepartmentListItem(
                                    index: index,
                                    item: departmentItem,
                                    isDepartment: isDepartment);
                              }
                            }
                          }),
                ))),
      ],
    );
  }
}
