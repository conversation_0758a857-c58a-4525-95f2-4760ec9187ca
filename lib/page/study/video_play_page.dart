import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marqueer/marqueer.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_handout_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/model/study/course_rank_model.dart';
import 'package:npemployee/model/study/course_watch_history_model.dart';
import 'package:npemployee/model/study/course_watching_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/audio_bloc/audio_event.dart';
import 'package:npemployee/provider/audio_bloc/audio_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/study/chewie/chewie_player.dart';
import 'package:npemployee/widget/study/person_list_item.dart';
import 'package:npemployee/widget/study/video_chapter_menu.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';

class VideoPlayPage extends StatefulWidget {
  final CourseListModel model;
  const VideoPlayPage({super.key, required this.model});

  @override
  State<VideoPlayPage> createState() => _VideoPlayPageState();
}

class _VideoPlayPageState extends State<VideoPlayPage> {
  final List<String> _tabs = ['视频目录', '正在学习', '学习排行'];
  PageController _pageController = PageController();
  int _tabIndex = 1;
  List<ChapterModel> chapters = [];
  List<LessonModel> lessons = [];
  List<CourseWatchHistoryModel> watchs = [];
  LessonModel? playingLesson; //正在播放lesson
  StreamSubscription? subscription;
  List<CourseHandoutModel?> handouts = [];

  final GlobalKey<VideoChapterMenuState> _videoChapterMenuKey =
      GlobalKey<VideoChapterMenuState>();

  // 添加下载任务相关变量
  final ReceivePort _port = ReceivePort();
  bool _isHandoutDownloaded = false;

  void _getCourseChapter() {
    UserServiceProvider().getCourseChapter(
      widget.model.id,
      cacheCallBack: (value) {
        _formatCourseChapterData(value, true);
      },
      successCallBack: (value) {
        _formatCourseChapterData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  void _getCourseWatchHistory() {
    UserServiceProvider().getCourseWatchList(
      course_id: widget.model.id,
      cacheCallBack: (value) {
        // _formatCourseWatchHistoryData(value, false);
      },
      successCallBack: (value) {
        _formatCourseWatchHistoryData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  void _getCourseHandout() {
    UserServiceProvider().getCourseHandout(
      cacheCallBack: (value) {
        _formatCourseHandoutData(value, true);
      },
      successCallBack: (value) {
        _formatCourseHandoutData(value, false);
      },
    );
  }

  void _formatCourseHandoutData(ResultData value, bool isCache) async {
    handouts.clear();
    for (var e in value.data) {
      handouts.add(CourseHandoutModel.fromJson(e));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatCourseChapterData(ResultData value, bool isCache) async {
    chapters.clear();
    lessons.clear();
    for (Map e in value.data) {
      ChapterModel chapterModel = ChapterModel.formJson(e);
      chapters.add(chapterModel);
      for (var element in chapterModel.lesson) {
        lessons.add(element);
      }
    }
    if (!isCache) {
      _getCourseWatchHistory();
    }
  }

  _formatCourseWatchHistoryData(ResultData? value, bool isCache) {
    watchs.clear();
    for (var e in value?.data ?? []) {
      watchs.add(CourseWatchHistoryModel.formJson(e));
    }
    //首次进入，默认播放上次观看
    if (watchs.isNotEmpty) {
      LessonModel l = lessons.firstWhere((e) => e.id == watchs.first.lesson_id);
      BlocManager().audioBloc.add(AudioPlayingEvent(l));
    } else {
      BlocManager().audioBloc.add(AudioPlayingEvent(lessons.first));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _audioPlayingListen() {
    subscription = BlocManager().audioBloc.stream.listen((state) async {
      if (state.type == AudioEventType.playingLesson &&
          state.playingLesson != null) {
        debugPrint('video_play切换小节 -- ${state.playingLesson?.id}');
        debugPrint('video_play之前小节 -- ${playingLesson?.id}');
        if (playingLesson?.id == state.playingLesson?.id) {
          return;
        }
        playingLesson = state.playingLesson;
        // 检查本节课资料是否已下载
        if (_getCurrentHandout() != null) {
          final handout = _getCurrentHandout()!;
          final downloadPath =
              '${await AppInfo().handoutDownloadDir}/${handout.name}.${handout.url.split('.').last}';
          _isHandoutDownloaded = File(downloadPath).existsSync();
        } else {
          _isHandoutDownloaded = false;
        }
        setState(() {});
        _videoChapterMenuKey.currentState?.updateSelectedLesson(playingLesson);
      }
    });
  }

  Future<bool> _checkPermission() async {
    if (Platform.isIOS) {
      return true;
    }

    if (Platform.isAndroid) {
      final info = await DeviceInfoPlugin().androidInfo;
      if (info.version.sdkInt > 28) {
        return true;
      }

      final status = await Permission.storage.status;
      if (status == PermissionStatus.granted) {
        return true;
      }

      final result = await Permission.storage.request();
      return result == PermissionStatus.granted;
    }

    throw StateError('unknown platform');
  }

  CourseHandoutModel? _getCurrentHandout() {
    if (playingLesson == null) return null;
    return handouts.firstWhere(
      (handout) => handout?.lesson_id == playingLesson!.id,
      orElse: () => null,
    );
  }

  // 初始化下载监听
  void _bindBackgroundIsolate() {
    final isSuccess = IsolateNameServer.registerPortWithName(
      _port.sendPort,
      'handout_downloader_send_port',
    );
    if (!isSuccess) {
      _unbindBackgroundIsolate();
      _bindBackgroundIsolate();
      return;
    }
    _port.listen((dynamic data) {
      final taskId = (data as List<dynamic>)[0] as String;
      final status = DownloadTaskStatus.fromInt(data[1] as int);
      final progress = data[2] as int;

      if (progress == 100 && status == DownloadTaskStatus.complete) {
        setState(() {
          _isHandoutDownloaded = true;
        });
      }
    });
  }

  void _unbindBackgroundIsolate() {
    IsolateNameServer.removePortNameMapping('handout_downloader_send_port');
  }

  @pragma('vm:entry-point')
  static void downloadCallback(String id, int status, int progress) {
    IsolateNameServer.lookupPortByName('handout_downloader_send_port')
        ?.send([id, status, progress]);
  }

  // 下载本节资料
  Future<void> _downloadHandout(CourseHandoutModel handout) async {
    await _checkPermission();
    final taskId = await FlutterDownloader.enqueue(
      url: handout.url,
      savedDir: await AppInfo().handoutDownloadDir,
      fileName: '${handout.name}.${handout.url.split('.').last}',
      saveInPublicStorage: false,
      openFileFromNotification: false,
    );
  }

  @override
  void initState() {
    super.initState();

    _audioPlayingListen();
    _getCourseChapter();
    _getCourseHandout();

    _bindBackgroundIsolate();

    FlutterDownloader.registerCallback(downloadCallback);
  }

  @override
  void dispose() {
    _unbindBackgroundIsolate();
    subscription?.cancel();
    subscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          SizedBox(height: ScreenUtil().statusBarHeight),
          Stack(
            children: [
              ChewiePlayer(
                  lesson: playingLesson,
                  width: ScreenUtil().screenWidth,
                  height: 208.h),
              Positioned(
                  child: IconButton(
                onPressed: () {
                  NavigatorUtils.pop(context);
                },
                icon: const Icon(Icons.arrow_back_ios, size: 18),
                color: Colors.white,
              ))
            ],
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  playingLesson?.title ?? '',
                  style: TextStyle(
                      color: const Color(0xFF323640),
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 12.h),
                Text(
                  widget.model.introduction ?? '',
                  style: TextStyle(
                      color: const Color(0xFF777777),
                      fontSize: 14.sp,
                      overflow: TextOverflow.ellipsis),
                ),
                SizedBox(height: 17.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text('共${chapters.length}章${lessons.length}节 | ',
                            style: TextStyle(
                                color: const Color(0xFF999999),
                                fontSize: 12.sp)),
                        Image.asset('assets/png/xiaoxin/studyed_icon.png',
                            width: 12, height: 12),
                        Text('${widget.model.watch_count ?? 0}人已学习',
                            style: TextStyle(
                                color: const Color(0xFF999999),
                                fontSize: 12.sp)),
                      ],
                    ),
                    if (_getCurrentHandout() != null)
                      GestureDetector(
                        onTap: () async {
                          if (_isHandoutDownloaded) {
                            // 分享逻辑
                            final downloadPath =
                                '${await AppInfo().handoutDownloadDir}/${_getCurrentHandout()?.name}.${_getCurrentHandout()?.url.split('.').last}';
                            ShareResult res =
                                await Share.shareXFiles([XFile(downloadPath)]);
                            if (res.status == ShareResultStatus.unavailable) {
                              ToastUtils.show('分享失败');
                            }
                          } else {
                            _downloadHandout(_getCurrentHandout()!);
                          }
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 7.w, vertical: 2.h),
                          decoration: BoxDecoration(
                              color: const Color(0xFFEBF2FF),
                              borderRadius: BorderRadius.circular(10.r)),
                          child: Row(
                            children: [
                              Text(
                                '本节资料',
                                style: TextStyle(
                                    color: const Color(0xFF0054FF),
                                    fontSize: 12.sp),
                              ),
                              SizedBox(width: 2.w),
                              !_isHandoutDownloaded
                                  ? Image.asset(
                                      'assets/png/xiaoxin/download.png',
                                      width: 12,
                                      height: 12,
                                    )
                                  : const Icon(Icons.share,
                                      size: 14, color: Color(0xFFFF7000))
                            ],
                          ),
                        ),
                      ),
                  ],
                )
              ],
            ),
          ),
          Container(
            width: ScreenUtil().screenWidth,
            height: 6.h,
            color: const Color(0xFFF7F8FD),
          ),
          SizedBox(height: 12.w),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              VideoTabItem(
                title: '视频目录',
                isSelect: _tabIndex == 1,
                onTap: () {
                  setState(() {
                    _tabIndex = 1;
                  });
                  _pageController.jumpToPage(_tabIndex - 1);
                },
              ),
              VideoTabItem(
                title: '正在学习',
                isSelect: _tabIndex == 2,
                onTap: () {
                  setState(() {
                    _tabIndex = 2;
                  });
                  _pageController.jumpToPage(_tabIndex - 1);
                },
              ),
              VideoTabItem(
                title: '学习排行',
                isSelect: _tabIndex == 3,
                onTap: () {
                  setState(() {
                    _tabIndex = 3;
                  });
                  _pageController.jumpToPage(_tabIndex - 1);
                },
              )
            ],
          ),
          Expanded(
              child: PageView(
            controller: _pageController,
            onPageChanged: (value) {
              setState(() {
                _tabIndex = value + 1;
              });
            },
            children: [
              VideoChapterMenu(
                key: _videoChapterMenuKey,
                datas: chapters,
                courseId: widget.model.id,
                lessons: lessons,
                selected: playingLesson,
              ),
              VideoStudyingPage(courseId: widget.model.id),
              StudyRankPage(courseId: widget.model.id),
            ],
          ))
        ],
      ),
    );
  }
}

class VideoStudyingPage extends StatefulWidget {
  final int courseId;
  const VideoStudyingPage({super.key, required this.courseId});

  @override
  State<VideoStudyingPage> createState() => _VideoStudyingPageState();
}

class _VideoStudyingPageState extends State<VideoStudyingPage> {
  List<CourseWatchingModel> courseWatchings = [];

  void _getWatchingList() {
    UserServiceProvider().getCourseWatchingList(widget.courseId,
        cacheCallBack: (value) {
      _formatCourseWatchingData(value, true);
    }, successCallBack: (value) {
      _formatCourseWatchingData(value, false);
    }, errorCallBack: (value) {});
  }

  _formatCourseWatchingData(ResultData? value, bool isCache) {
    courseWatchings.clear();
    for (var element in value?.data) {
      courseWatchings.add(CourseWatchingModel.formJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();

    _getWatchingList();
  }

  @override
  Widget build(BuildContext context) {
    return courseWatchings.isEmpty
        ? const NoDataPage()
        : MediaQuery.removePadding(
            removeTop: true,
            context: context,
            child: ListView.builder(
                itemCount: courseWatchings.length,
                itemBuilder: (c, index) {
                  CourseWatchingModel watch = courseWatchings[index];
                  return Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom: BorderSide(
                                color: Color.fromRGBO(230, 230, 230, 0.5),
                                width: 0.5))),
                    child: Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: SizedBox(
                              width: 46,
                              height: 46,
                              child: CachedNetworkImage(
                                  imageUrl: watch.user.avatar)),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(watch.user.name,
                                style: TextStyle(
                                        color: const Color(0xFF222222),
                                        fontSize: 16.sp)
                                    .pfMedium),
                            SizedBox(height: 3.h),
                            Row(
                              children: [
                                Expanded(
                                    child: Text(watch.departmentName,
                                        style: TextStyle(
                                                color: const Color(0xFF464B59),
                                                fontSize: 12.sp)
                                            .pfMedium)),
                                SizedBox(width: 34.w),
                                Text('已学${watch.stutyTime}分钟',
                                    style: TextStyle(
                                            color: const Color(0xFF464B59),
                                            fontSize: 12.sp)
                                        .pfMedium)
                              ],
                            )
                          ],
                        )),
                      ],
                    ),
                  );
                }));
  }
}

class StudyRankPage extends StatefulWidget {
  final int courseId;
  const StudyRankPage({super.key, required this.courseId});

  @override
  State<StudyRankPage> createState() => _StudyRankPageState();
}

class _StudyRankPageState extends State<StudyRankPage> {
  List<CourseRankModel> courseRank = [];

  void _getStudyRankData() {
    UserServiceProvider().getCourseRankList(
      courseId: widget.courseId,
      cacheCallBack: (value) {
        _formatStudyRankData(value, true);
      },
      successCallBack: (value) {
        _formatStudyRankData(value, false);
      },
    );
  }

  void _formatStudyRankData(ResultData value, bool isCache) {
    courseRank.clear();
    for (var element in value.data) {
      courseRank.add(CourseRankModel(
          total_duration: element['total_duration'],
          today_duration: element['today_duration'],
          ranking: element['ranking'],
          user: element['user']));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getStudyRankData();
  }

  @override
  Widget build(BuildContext context) {
    return courseRank.isEmpty
        ? const NoDataPage()
        : Column(
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: 7.h),
                width: ScreenUtil().screenWidth,
                decoration: const BoxDecoration(color: Color(0xFFFFFAF2)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset('assets/png/xiaoxin/video_sort_ling.png',
                        width: 18, height: 18),
                    SizedBox(width: 7.w),
                    SizedBox(
                      width: ScreenUtil().screenWidth - 64.w,
                      height: 20.h,
                      child: Marqueer(
                          interaction: false,
                          separatorBuilder: (context, index) {
                            return SizedBox(width: 16.w);
                          },
                          child: Text('此排行榜仅为本视频的学习排行，观看时长大于一分钟才会被记录哦~',
                              style: TextStyle(
                                  color: const Color(0xFFFFAC38),
                                  fontSize: 13.sp))),
                    ),
                  ],
                ),
              ),
              Expanded(
                  child: MediaQuery.removePadding(
                      removeTop: true,
                      context: context,
                      child: ListView.builder(
                          itemCount: courseRank.length,
                          itemBuilder: (c, index) {
                            if (index == 0) {
                              int meIndex = courseRank.indexWhere((e) =>
                                  e.userId ==
                                  GlobalPreferences().userInfo?.user.id);

                              return PersonListMeItem(
                                  item: meIndex == -1
                                      ? null
                                      : courseRank[meIndex]);
                            }
                            CourseRankModel item = courseRank[index - 1];
                            return PersonListItem(item: item, index: index);
                          })))
            ],
          );
  }
}
