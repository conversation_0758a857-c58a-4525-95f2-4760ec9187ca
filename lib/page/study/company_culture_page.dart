import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/widget/common_nav.dart';

class CompanyCulturePage extends StatefulWidget {
  final String title;
  const CompanyCulturePage({super.key, required this.title});

  @override
  State<CompanyCulturePage> createState() => _CompanyCulturePageState();
}

class _CompanyCulturePageState extends State<CompanyCulturePage> {
  final ScrollController _scrollController = ScrollController();
  final List<String> _imagePaths = [
    'assets/png/xiaoxin/company_culture_1.png',
    'assets/png/xiaoxin/company_culture_2.png',
    'assets/png/xiaoxin/company_culture_3.png',
    'assets/png/xiaoxin/company_culture_4.png',
    'assets/png/xiaoxin/company_culture_5.png',
    'assets/png/xiaoxin/company_culture_6.png',
    'assets/png/xiaoxin/company_culture_7.png',
  ];
  final List<String> _outline = [
    '新途径企业文化',
    '新途径职教·教学体系',
    '新途径教学理念',
    '新途径课堂',
    '做不到就别来新途径',
    '新途径对学员说',
    '学员在新途径',
    '新途径在线运营理念',
    '新途径slogan'
  ];
  bool _showOutline = false;

  void _scrollToImage(int index) {
    double itemHeight = ScreenUtil().screenHeight; // 假设每张图片的高度等于屏幕高度
    _scrollController.animateTo(
      index * itemHeight,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonNav(
        title: widget.title,
      ),
      body: Stack(
        children: [
          ListView.builder(
            controller: _scrollController,
            itemCount: _imagePaths.length,
            itemBuilder: (context, index) {
              return Image.asset(_imagePaths[index], fit: BoxFit.fitWidth);
            },
          ),
          Positioned(
            bottom: 20.w,
            right: 20.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _showOutline = !_showOutline;
                    });
                  },
                  child: const Icon(Icons.list),
                ),
                if (_showOutline)
                  ...List.generate(_outline.length, (index) {
                    int toIndex = index;
                    if (_outline[index] == '新途径对学员说') {
                      toIndex = 5;
                    } else if (_outline[index] == '做不到就别来新途径') {
                      toIndex = 4;
                    }
                    return Padding(
                      padding: EdgeInsets.only(top: 10.w),
                      child: ElevatedButton(
                        onPressed: () {
                          _scrollToImage(toIndex);

                          setState(() {
                            _showOutline = false;
                          });
                        },
                        child: Text(_outline[index],
                            style: TextStyle().pfSemiBold),
                      ),
                    );
                  }),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
