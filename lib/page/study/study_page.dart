import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/page/study/study_rank_page.dart';
import 'package:npemployee/page/study/xiaoxin_academy_page.dart';
import 'package:npemployee/provider/tab_bloc/tab_event.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'health_rank_page.dart';

class StudyPage extends StatefulWidget {
  @override
  _StudyPageState createState() => _StudyPageState();
}

class _StudyPageState extends State<StudyPage> {
  int _selectedIndex = 0;
  final PageController _pageController = PageController();
  final List<String> _tabs = ['小新学院', '学习榜', '健康榜'];

  StreamSubscription? _tabSubscription;

  final List<Widget> _pageList = [
    XiaoxinAcademyPage(),
    StudyRankPage(),
    HealthRankPage(),
  ];

  void _onTabSelected(int index) {
    _selectedIndex = index;
    _pageController.animateToPage(_selectedIndex,
        duration: const Duration(milliseconds: 300), curve: Curves.ease);
  }

  void _subscriptionTabChangeBloc() {
    _tabSubscription?.cancel();
    _tabSubscription = BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change && state.page == 1) {
        print("-- 进入学习页面 ${state.page}");
        if (_selectedIndex == 0) {
          BlocManager().tabBloc.add(TabChangeEvent(99));
        } else if (_selectedIndex == 1) {
          BlocManager().tabBloc.add(TabChangeEvent(98));
        } else {
          BlocManager().tabBloc.add(TabChangeEvent(97));
        }
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _subscriptionTabChangeBloc();
  }

  @override
  void dispose() {
    _tabSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: double.infinity,
            height: 117,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFD6E8FA), Color(0xFFF2F8FE).withAlpha(0)],
              ),
            ),
          ),
          Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 16.w),
                height: 88.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: List.generate(_tabs.length, (index) {
                    bool isSelected = _selectedIndex == index;
                    return GestureDetector(
                      onTap: () => _onTabSelected(index),
                      child: Padding(
                        padding: const EdgeInsets.only(right: 24.0),
                        child: Text(
                          _tabs[index],
                          style: isSelected
                              ? TextStyle(
                                      fontSize: 22.sp,
                                      color: AppTheme.colorBlackTitle)
                                  .pfSemiBold
                              : TextStyle(
                                  fontSize: 17.sp,
                                  color: const Color(0xFF808080),
                                ).pfMedium,
                        ),
                      ),
                    );
                  }),
                ),
              ),
              Expanded(
                  child: MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      child: PageView.builder(
                          controller: _pageController,
                          itemCount: 3,
                          onPageChanged: (value) {
                            setState(() {
                              _selectedIndex = value;
                            });
                          },
                          itemBuilder: (pc, pi) {
                            return _pageList[pi];
                          })))
            ],
          )
        ],
      ),
    );
  }
}
