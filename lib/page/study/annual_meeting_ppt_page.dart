import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/download/task_info.dart';
import 'package:npemployee/model/study/annual_meeting_PPT_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/annual_meeting_ppt_item.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class AnnualMeetingPPTPage extends StatefulWidget with WidgetsBindingObserver {
  final String title;
  const AnnualMeetingPPTPage({super.key, required this.title});

  @override
  State<AnnualMeetingPPTPage> createState() => _AnnualMeetingPPTPageState();
}

class _AnnualMeetingPPTPageState extends State<AnnualMeetingPPTPage> {
  bool _dropIsOpen = false; //分类按钮是否打开
  String _dropValue = '';
  List<AnnualMeetingPptModel> ppts = [];
  List<AnnualMeetingPptTagModel> pptTags = [];
  List<String> _dropItems = [];

  List<TaskInfo> _tasks = [];
  final ReceivePort _port = ReceivePort();

  final EasyRefreshController _refreshController = EasyRefreshController();
  AutoScrollController scrollController = AutoScrollController();

  void _getTags() {
    UserServiceProvider().getPptMeetingTagList(
      cacheCallBack: (data) {
        _formatTagsData(data, true);
      },
      successCallBack: (data) {
        _formatTagsData(data, false);
      },
      errorCallBack: (data) {},
    );
  }

  void _getPpts({int? tag_id}) {
    UserServiceProvider().getPptMeetingList(
      tag_id: tag_id,
      cacheCallBack: (data) {
        _formatPptsData(data, true);
      },
      successCallBack: (data) {
        _formatPptsData(data, false);
      },
      errorCallBack: (data) {},
    );
  }

  void _formatTagsData(ResultData? data, bool isCache) {
    pptTags.clear();
    for (var element in data?.data) {
      pptTags.add(AnnualMeetingPptTagModel.formJson(element));
    }
    _dropValue = pptTags.first.name;
    _dropItems = pptTags.map((e) => e.name).toList();

    if (!isCache) {
      _getPpts(tag_id: pptTags.first.id);
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _formatPptsData(ResultData? data, bool isCache) {
    ppts.clear();
    for (var element in data?.data ?? []) {
      ppts.add(AnnualMeetingPptModel.formJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
        _prepare();
      }
    }
    scrollController.scrollToIndex(0);
  }

  @override
  void initState() {
    super.initState();
    _getTags();

    _bindBackgroundIsolate();

    FlutterDownloader.registerCallback(downloadCallback, step: 1);
  }

  @override
  void dispose() {
    _unbindBackgroundIsolate();
    super.dispose();
  }

  void _bindBackgroundIsolate() {
    final isSuccess = IsolateNameServer.registerPortWithName(
      _port.sendPort,
      'downloader_ppt_send_port',
    );
    if (!isSuccess) {
      _unbindBackgroundIsolate();
      _bindBackgroundIsolate();
      return;
    }
    _port.listen((dynamic data) {
      final taskId = (data as List<dynamic>)[0] as String;
      final status = DownloadTaskStatus.fromInt(data[1] as int);
      final progress = data[2] as int;

      debugPrint(
        'Callback on UI isolate: '
        'task ($taskId) is in status ($status) and process ($progress)',
      );

      if (_tasks.isNotEmpty) {
        final task = _tasks.firstWhere((task) => task.taskId == taskId);
        setState(() {
          task
            ..status = status
            ..progress = progress;
        });
      }
    });
  }

  void _unbindBackgroundIsolate() {
    IsolateNameServer.removePortNameMapping('downloader_ppt_send_port');
  }

  @pragma('vm:entry-point')
  static void downloadCallback(
    String id,
    int status,
    int progress,
  ) {
    debugPrint(
      'Callback on background isolate: '
      'task ($id) is in status ($status) and process ($progress)',
    );

    IsolateNameServer.lookupPortByName('downloader_ppt_send_port')
        ?.send([id, status, progress]);
  }

  Future<bool> _checkPermission() async {
    if (Platform.isIOS) {
      return true;
    }

    if (Platform.isAndroid) {
      final info = await DeviceInfoPlugin().androidInfo;
      if (info.version.sdkInt > 28) {
        return true;
      }

      final status = await Permission.storage.status;
      if (status == PermissionStatus.granted) {
        return true;
      }

      final result = await Permission.storage.request();
      return result == PermissionStatus.granted;
    }

    throw StateError('unknown platform');
  }

  Future<void> _prepare() async {
    final tasks = await FlutterDownloader.loadTasks();

    if (tasks == null) {
      debugPrint('No tasks were retrieved from the database.');
      return;
    }

    _tasks = [];

    for (var ppt in ppts) {
      _tasks.add(TaskInfo(
          name: ppt.name,
          link: ppt.url,
          pptTag: pptTags.firstWhere((tag) => tag.id == ppt.tag_id).name));
    }

    for (final task in tasks) {
      for (final info in _tasks) {
        String taskUrl =
            Platform.isAndroid ? Uri.decodeFull(task.url) : task.url;
        if (info.link == taskUrl) {
          info
            ..taskId = task.taskId
            ..status = task.status
            ..progress = task.progress;
        }
      }
    }

    await _checkPermission();

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CommonNav(title: widget.title, rightWidget: [
          GestureDetector(
            onTap: () {
              setState(() {
                _dropIsOpen = !_dropIsOpen;
              });
            },
            child: Container(
                margin: EdgeInsets.only(right: 16.w),
                child: Row(
                  children: [
                    Text(_dropValue,
                        style: TextStyle(
                                color: const Color(0xFF1D1C1F), fontSize: 13.sp)
                            .pfRegular),
                    SizedBox(width: 4.w),
                    if (pptTags.isNotEmpty)
                      Image.asset(
                          _dropIsOpen
                              ? 'assets/png/xiaoxin/drop_up.png'
                              : 'assets/png/xiaoxin/drop_down.png',
                          width: 8,
                          height: 8),
                  ],
                )),
          ),
        ]),
        body: Stack(
          children: [
            EasyRefresh.builder(
              controller: _refreshController,
              onRefresh: () =>
                  _getPpts(tag_id: pptTags[_dropItems.indexOf(_dropValue)].id),
              childBuilder: (_, physic) => ppts.isEmpty
                  ? NoDataPage(physics: physic)
                  : ListView.builder(
                      controller: scrollController,
                      physics: physic,
                      itemCount: ppts.length,
                      itemBuilder: (c, index) {
                        if (index >= _tasks.length) {
                          return const SizedBox();
                        }
                        TaskInfo task = _tasks[index];
                        return AutoScrollTag(
                          key: ValueKey(index),
                          controller: scrollController,
                          index: index,
                          child: AnnualMeetingPPTItem(
                              ppt: ppts[index], task: task),
                        );
                      }),
            ),
            if (_dropIsOpen)
              Stack(children: [
                Container(
                  color: const Color.fromRGBO(0, 0, 0, 0.6),
                ),
                Container(
                    height: 260.h,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(20.r),
                            bottomRight: Radius.circular(20.r))),
                    child: Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                              itemCount: _dropItems.length,
                              itemBuilder: (c, index) {
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _dropValue = _dropItems[index];
                                    });
                                  },
                                  child: Container(
                                    decoration: const BoxDecoration(
                                        color: Colors.white,
                                        border: Border(
                                            top: BorderSide(
                                                color: Color(0xFFEDEDED),
                                                width: 0.5))),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 20.w, vertical: 17.h),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(_dropItems[index],
                                            style:
                                                _dropItems[index] == _dropValue
                                                    ? TextStyle(
                                                            color: const Color(
                                                                0xFF0054FF),
                                                            fontSize: 15.sp)
                                                        .pfMedium
                                                    : TextStyle(
                                                            color: const Color(
                                                                0xFF666666),
                                                            fontSize: 15.sp)
                                                        .pfRegular),
                                        if (_dropItems[index] == _dropValue)
                                          const Icon(Icons.check,
                                              size: 20,
                                              color: Color(0xFF0054FF))
                                      ],
                                    ),
                                  ),
                                );
                              }),
                        ),
                        SizedBox(height: 16.h),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _dropIsOpen = !_dropIsOpen;
                            });
                            _getPpts(
                                tag_id:
                                    pptTags[_dropItems.indexOf(_dropValue)].id);
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 64.w, vertical: 10.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFF1C79FF),
                              borderRadius: BorderRadius.circular(16.r),
                            ),
                            child: Text('确定',
                                style: TextStyle(
                                        color: Colors.white, fontSize: 14.sp)
                                    .pfMedium),
                          ),
                        ),
                        SizedBox(height: 16.h),
                      ],
                    )),
              ]),
          ],
        ));
  }
}
