import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marqueer/marqueer.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/model/study/course_rank_model.dart';
import 'package:npemployee/model/study/course_watch_history_model.dart';
import 'package:npemployee/model/study/course_watching_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/audio_bloc/audio_event.dart';
import 'package:npemployee/provider/audio_bloc/audio_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/study/chewie/chewie_player.dart';
import 'package:npemployee/widget/study/person_list_item.dart';
import 'package:npemployee/widget/study/video_chapter_menu.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';

class CapabilityVideoPage extends StatefulWidget {
  final int courseId; //当前课程
  final LessonModel? playLesson;
  final int? playLessonId;
  final int? playChapterId;
  final bool? playStatus;
  const CapabilityVideoPage(
      {super.key,
      required this.courseId,
      required this.playLesson,
      this.playLessonId,
      this.playChapterId,
      this.playStatus});

  @override
  State<CapabilityVideoPage> createState() => _CapabilityVideoPageState();
}

class _CapabilityVideoPageState extends State<CapabilityVideoPage> {
  PageController _pageController = PageController();
  int _tabIndex = 1;

  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  int currentPosition = -1; //当前播放进度

  StreamSubscription? subscription;

  LessonModel? playLesson;
  CourseListModel? course;
  List<CourseListModel> myStarList = [];
  List<CourseWatchingModel> courseWatchings = [];
  List<ChapterModel> chapters = [];
  List<LessonModel> lessons = [];
  List<CourseWatchHistoryModel> courseWatchHistorys = [];

  final GlobalKey<VideoChapterMenuState> _videoChapterMenuKey =
      GlobalKey<VideoChapterMenuState>();
  bool _isExpanded = false; //简介展开收起

  bool? _wasPlaying; //记录视频是否正在播放

  void _getCourseDetail() {
    UserServiceProvider().getCourseDetail(widget.courseId,
        cacheCallBack: (value) {
      _formatCourseDetailData(value, true);
    }, successCallBack: (value) {
      _formatCourseDetailData(value, false);
    }, errorCallBack: (value) {});
  }

  void _getCourseChapter() {
    UserServiceProvider().getCourseChapter(widget.courseId,
        cacheCallBack: (value) {
      _formatCourseChapterData(value, true);
    }, successCallBack: (value) {
      _formatCourseChapterData(value, false);
    }, errorCallBack: (value) {});
  }

  _formatCourseChapterData(ResultData value, bool isCache) {
    chapters.clear();
    lessons.clear();
    for (var e in value.data) {
      ChapterModel chapterModel = ChapterModel.formJson(e);
      chapters.add(chapterModel);
      for (var lesson in chapterModel.lesson) {
        lessons.add(lesson);
      }
    }

    if (!isCache) {
      _videoChapterMenuKey.currentState?.updateSelectedLesson(playLesson);
      _getCourseWatchHistory();
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _getMyStarList({String? keyword}) {
    UserServiceProvider().getMyStarList(
      cacheCallBack: (value) {
        _formatMyStarListData(value, true);
      },
      successCallBack: (value) {
        _formatMyStarListData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  void _getCourseWatchHistory() {
    UserServiceProvider().getCourseWatchList(
      course_id: widget.courseId,
      cacheCallBack: (value) {
        // _formatCourseWatchHistoryData(value, true);
      },
      successCallBack: (value) {
        _formatCourseWatchHistoryData(value, false);
      },
      errorCallBack: (value) {
        // _initializePlayer();
      },
    );
  }

  _formatCourseDetailData(ResultData value, bool isCache) {
    course = CourseListModel.formJson(value.data);
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatMyStarListData(ResultData? value, bool isCache) {
    myStarList.clear();
    for (var e in value?.data) {
      for (var element in e['course']) {
        myStarList.add(CourseListModel.formJson(element));
      }
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatCourseWatchHistoryData(ResultData? value, bool isCache) {
    courseWatchHistorys.clear();
    for (var e in value?.data ?? []) {
      courseWatchHistorys.add(CourseWatchHistoryModel.formJson(e));
    }
    if (courseWatchHistorys.isNotEmpty) {
      LessonModel l = lessons
          .firstWhere((e) => e.id == courseWatchHistorys.first.lesson_id);
      BlocManager().audioBloc.add(AudioPlayingEvent(l));
    } else {
      BlocManager().audioBloc.add(AudioPlayingEvent(lessons.first));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _audioPlayingListen() {
    subscription = BlocManager().audioBloc.stream.listen((state) async {
      if (state.type == AudioEventType.playingLesson &&
          state.playingLesson != null) {
        debugPrint('capability_video页面切换小节 -- ${state.playingLesson?.id}');
        debugPrint(
            'capability_video页面之前小节 -- ${playLesson?.id} -- $currentPosition');
        if (playLesson?.id == state.playingLesson?.id) {
          return;
        }
        setState(() {
          playLesson = state.playingLesson!;
        });
        _videoChapterMenuKey.currentState?.updateSelectedLesson(playLesson);
      }
    });
  }

  @override
  void initState() {
    super.initState();

    _audioPlayingListen();

    _getCourseDetail();
    _getCourseChapter();
    _getMyStarList();
    if (widget.playLesson != null) {
      BlocManager().audioBloc.add(AudioPlayingEvent(widget.playLesson!));
    }
  }

  @override
  void didUpdateWidget(covariant CapabilityVideoPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.playStatus != null &&
        widget.playStatus != oldWidget.playStatus) {
      setState(() {
        _wasPlaying = widget.playStatus;
      });
    }
  }

  @override
  void dispose() {
    subscription?.cancel();
    subscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Stack(
            children: [
              ChewiePlayer(
                lesson: playLesson,
                playStatus: _wasPlaying,
                width: ScreenUtil().screenWidth,
                height: 208.h,
              ),
              if (course != null && course?.course_type == 2)
                Positioned(
                    child: IconButton(
                  onPressed: () {
                    NavigatorUtils.pop(context);
                  },
                  icon: const Icon(Icons.arrow_back_ios, size: 18),
                  color: Colors.white,
                )),
            ],
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        playLesson?.title ?? '',
                        style: TextStyle(
                                color: const Color(0xFF323640), fontSize: 16.sp)
                            .pfSemiBold,
                      ),
                    ),
                    if (course != null && course?.course_type == 2)
                      GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          setState(() {
                            _wasPlaying = false;
                          });
                          NavigatorUtils.push(context, StudyRouter.downloadList,
                              arguments: {
                                'chapters': chapters,
                                'course': course,
                                'type': 2
                              }).then((v) {
                            setState(() {
                              _wasPlaying = true;
                            });
                          });
                        },
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: Image.asset(
                              'assets/png/xiaoxin/audio_download.png'),
                        ),
                      ),
                    if (course != null && course?.course_type == 2)
                      SizedBox(width: 34.w),
                    if (course != null && course?.course_type == 2)
                      GestureDetector(
                        onTap: () {
                          EasyLoading.show();
                          UserServiceProvider()
                              .starOrCancelCourse(widget.courseId)
                              .then((value) {
                            EasyLoading.dismiss();
                            if (value?.code != 0) {
                              EasyLoading.showError(value!.msg);
                              return;
                            }
                            _getMyStarList();
                          });
                        },
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: myStarList.indexWhere(
                                      (star) => star.id == widget.courseId) !=
                                  -1
                              ? Image.asset('assets/png/xiaoxin/collect.png')
                              : Image.asset('assets/png/xiaoxin/uncollect.png'),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: 12.h),
                if (course != null)
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: ValidatorUtils.delHL(course?.introduction)
                                      .length >
                                  22
                              ? (_isExpanded
                                  ? ValidatorUtils.delHL(course?.introduction)
                                  : '${ValidatorUtils.delHL(course?.introduction).substring(0, 22)}'
                                      '...')
                              : ValidatorUtils.delHL(course?.introduction),
                          style: TextStyle(
                            color: const Color(0xFF777777),
                            fontSize: 12.sp,
                          ).pfRegular,
                        ),
                        if (ValidatorUtils.delHL(course?.introduction).length >
                            22)
                          WidgetSpan(
                            alignment: PlaceholderAlignment.baseline,
                            baseline: TextBaseline.alphabetic,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _isExpanded = !_isExpanded;
                                });
                              },
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(width: 5.w),
                                  Text(
                                    _isExpanded ? ' 收起' : ' 展开',
                                    style: TextStyle(
                                      color: const Color(0xFF4A7FE9),
                                      fontSize: 12.sp,
                                    ).pfMedium,
                                  ),
                                  SizedBox(width: 3.w),
                                  Transform.rotate(
                                    angle: _isExpanded ? 3.14159 : 0,
                                    child: Image.asset(
                                        'assets/png/xiaoxin/audio_drop.png',
                                        width: 8,
                                        height: 8),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                SizedBox(height: 17.h),
                if (course != null)
                  Row(
                    children: [
                      Text('共${course!.lesson_count}节 | ',
                          style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 12.sp)
                              .pfRegular),
                      Image.asset('assets/png/xiaoxin/studyed_icon.png',
                          width: 12, height: 12),
                      Text('${course!.watch_count}人已学习',
                          style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 12.sp)
                              .pfRegular),
                    ],
                  ),
              ],
            ),
          ),
          Container(
            width: ScreenUtil().screenWidth,
            height: 6.h,
            color: const Color(0xFFF7F8FD),
          ),
          SizedBox(height: 12.w),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              VideoTabItem(
                title: '视频目录',
                isSelect: _tabIndex == 1,
                onTap: () {
                  setState(() {
                    _tabIndex = 1;
                  });
                  _pageController.jumpToPage(_tabIndex - 1);
                },
              ),
              VideoTabItem(
                title: '正在学习',
                isSelect: _tabIndex == 2,
                onTap: () {
                  setState(() {
                    _tabIndex = 2;
                  });
                  _pageController.jumpToPage(_tabIndex - 1);
                },
              ),
              VideoTabItem(
                title: '学习排行',
                isSelect: _tabIndex == 3,
                onTap: () {
                  setState(() {
                    _tabIndex = 3;
                  });
                  _pageController.jumpToPage(_tabIndex - 1);
                },
              )
            ],
          ),
          Expanded(
              child: PageView(
            controller: _pageController,
            onPageChanged: (value) {
              setState(() {
                _tabIndex = value + 1;
              });
            },
            children: [
              VideoChapterMenu(
                key: _videoChapterMenuKey,
                datas: chapters,
                courseId: widget.courseId,
                lessons: lessons,
                selected: playLesson,
              ),
              VideoStudyingPage(courseId: widget.courseId),
              StudyRankPage(courseId: widget.courseId),
            ],
          ))
        ],
      ),
    );
  }
}

class VideoStudyingPage extends StatefulWidget {
  final int courseId;
  const VideoStudyingPage({super.key, required this.courseId});

  @override
  State<VideoStudyingPage> createState() => _VideoStudyingPageState();
}

class _VideoStudyingPageState extends State<VideoStudyingPage> {
  List<CourseWatchingModel> watchings = [];

  void _getWatchingList() {
    UserServiceProvider().getCourseWatchingList(widget.courseId,
        cacheCallBack: (value) {
      _formatCourseWatchingData(value, true);
    }, successCallBack: (value) {
      _formatCourseWatchingData(value, false);
    }, errorCallBack: (value) {});
  }

  _formatCourseWatchingData(ResultData? value, bool isCache) {
    watchings.clear();
    for (var element in value?.data) {
      watchings.add(CourseWatchingModel.formJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getWatchingList();
  }

  @override
  Widget build(BuildContext context) {
    return watchings.isEmpty
        ? const NoDataPage()
        : MediaQuery.removePadding(
            removeTop: true,
            context: context,
            child: ListView.builder(
                itemCount: watchings.length,
                itemBuilder: (c, index) {
                  CourseWatchingModel watch = watchings[index];
                  return Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom: BorderSide(
                                color: Color.fromRGBO(230, 230, 230, 0.5),
                                width: 0.5))),
                    child: Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: Container(
                              width: 46,
                              height: 46,
                              child: CachedNetworkImage(
                                  imageUrl: watch.user.avatar)),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(watch.user.name,
                                  style: TextStyle(
                                      color: const Color(0xFF222222),
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w500)),
                              SizedBox(height: 3.h),
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(watch.departmentName,
                                        style: TextStyle(
                                                color: const Color(0xFF464B59),
                                                fontSize: 12.sp)
                                            .pfRegular),
                                  ),
                                  SizedBox(width: 34.w),
                                  Text('已学${watch.stutyTime}分钟',
                                      style: TextStyle(
                                              color: const Color(0xFF464B59),
                                              fontSize: 12.sp)
                                          .pfRegular)
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  );
                }));
  }
}

class StudyRankPage extends StatefulWidget {
  final int courseId;
  const StudyRankPage({super.key, required this.courseId});

  @override
  State<StudyRankPage> createState() => _StudyRankPageState();
}

class _StudyRankPageState extends State<StudyRankPage> {
  List<CourseRankModel> courseRank = [];

  void _getStudyRankData() {
    UserServiceProvider().getCourseRankList(
      courseId: widget.courseId,
      cacheCallBack: (value) {
        _formatStudyRankData(value, true);
      },
      successCallBack: (value) {
        _formatStudyRankData(value, false);
      },
    );
  }

  void _formatStudyRankData(ResultData value, bool isCache) {
    courseRank.clear();
    for (var element in value.data) {
      courseRank.add(CourseRankModel(
          total_duration: element['total_duration'],
          today_duration: element['today_duration'],
          ranking: element['ranking'],
          user: element['user']));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getStudyRankData();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 7.h),
          width: ScreenUtil().screenWidth,
          decoration: const BoxDecoration(color: Color(0xFFFFFAF2)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset('assets/png/xiaoxin/video_sort_ling.png',
                  width: 18, height: 18),
              SizedBox(width: 7.w),
              SizedBox(
                width: ScreenUtil().screenWidth - 64.w,
                height: 20.h,
                child: Marqueer(
                    interaction: false,
                    separatorBuilder: (context, index) {
                      return SizedBox(width: 16.w);
                    },
                    child: Text('此排行榜仅为本视频的学习排行，观看时长大于一分钟才会被记录哦~',
                        style: TextStyle(
                            color: const Color(0xFFFFAC38), fontSize: 13.sp))),
              ),
            ],
          ),
        ),
        Expanded(
            child: MediaQuery.removePadding(
                removeTop: true,
                context: context,
                child: ListView.builder(
                    itemCount: courseRank.length,
                    itemBuilder: (c, index) {
                      if (index == 0) {
                        int meIndex = courseRank.indexWhere((e) =>
                            e.userId == GlobalPreferences().userInfo?.user.id);

                        return PersonListMeItem(
                            item: meIndex == -1 ? null : courseRank[meIndex]);
                      }
                      CourseRankModel item = courseRank[index - 1];
                      return PersonListItem(item: item, index: index);
                    })))
      ],
    );
  }
}
