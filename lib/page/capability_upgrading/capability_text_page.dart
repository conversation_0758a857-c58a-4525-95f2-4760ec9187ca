import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class CapabilityTextPage extends StatefulWidget {
  const CapabilityTextPage({super.key});

  @override
  State<CapabilityTextPage> createState() => _CapabilityTextPageState();
}

class _CapabilityTextPageState extends State<CapabilityTextPage> {
  final FlutterTts _flutterTts = FlutterTts();
  bool _isReading = false;

  @override
  void initState() {
    super.initState();
    _flutterTts.setLanguage('language');
    _flutterTts.setSpeechRate(0.5);
    _flutterTts.setPitch(1.0);
    _speakHandlersSet();
  }

  void _speakHandlersSet() {
    _flutterTts.setStartHandler(() {
      _isReading = true;
    });
    _flutterTts.setCompletionHandler(() {
      _isReading = false;
    });
  }

  Future<void> _speak(String text) async {
    await _flutterTts.speak(
        '你好，今天星期五, 阴天。就发了几分离开打算减肥i哦啊多少积分凯莉撒的肌肤拉屎的风景阿里斯顿看风景阿克琉斯的肌肤卢卡斯的肌肤卡拉屎的肌肤');
  }

  Future<void> _stopSpeak() async {
    await _flutterTts.stop();
    _isReading = false;
  }

  @override
  void dispose() {
    if (_isReading) _flutterTts.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部章节和阅读情况
              Row(
                children: [
                  CachedNetworkImage(
                    imageUrl:
                        'https://img2.baidu.com/it/u=2571723274,2884981383&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=1599',
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '当前章节:第1节 王立群读《史记》',
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              '简介文案简介文案简介文案',
                              style: TextStyle(color: Colors.grey),
                            ),
                            SizedBox(width: 4),
                            InkWell(
                              onTap: () {
                                // 展开内容的点击事件
                                _showBottomSheet();
                              },
                              child: Text(
                                '展开',
                                style: TextStyle(color: Colors.blue),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              // 文字内容部分
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    '《史记》详细记载了上至上古传说中的黄帝时代，下至汉武帝太初四年间共3000多年的历史。\n\n'
                    '全书包括十二本纪、三十世家、七十列传、十表、八书，共一百三十篇，约五十二万六千五百字。\n\n'
                    '本纪记载历代帝王的政绩，世家叙述诸侯和勋贵的兴亡，列传记录重要人物的言行事迹，表为大事年表，书则包括各种典章制度。\n\n'
                    '全书包括十二本纪、三十世家、七十列传、十表、八书，共一百三十篇，约五十二万六千五百字。\n\n'
                    '本纪记载历代帝王的政绩，世家叙述诸侯和勋贵的兴亡，列传记录重要人物的言行事迹，表为大事年表，书则包括各种典章制度。\n\n'
                    '列传记录重要人物的言行事迹，表为大事年表，书则包括各种典章制度。',
                    style: TextStyle(fontSize: 16, height: 1.5),
                  ),
                ),
              ),
            ],
          ),
          Positioned(
            bottom: 90.h,
            right: 10.w,
            child: GestureDetector(
              onTap: () async {
                // 播放文字的点击事件
                if (_isReading) {
                  _stopSpeak();
                } else {
                  _speak('');
                }
              },
              child: Container(
                alignment: Alignment.center,
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    color: Colors.white,
                    boxShadow: const [
                      BoxShadow(
                          color: Color.fromRGBO(0, 84, 255, 0.12),
                          offset: Offset(0, 5.5),
                          blurRadius: 18,
                          spreadRadius: 0)
                    ]),
                child: Text(
                  '听',
                  style: TextStyle(
                      color: const Color(0xFF606266),
                      fontSize: 23.sp,
                      fontWeight: FontWeight.w900),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  void _showBottomSheet() {
    showModalBottomSheet(
        isDismissible: false,
        context: context,
        builder: (_) {
          return ClipRRect(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r)),
            child: Container(
              color: Colors.white,
              width: ScreenUtil().screenWidth,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 35.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Stack(
                        children: [
                          Positioned(
                              top: 14.h,
                              child: Image.asset(
                                  'assets/png/xiaoxin/sort_bac.png',
                                  width: 125.w)),
                          Text(
                            '书籍简介',
                            style: TextStyle(
                                color: const Color(0xFF333333),
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w500),
                          )
                        ],
                      ),
                      IconButton(
                          onPressed: () => NavigatorUtils.pop(_),
                          icon: Icon(Icons.close))
                    ],
                  ),
                  SizedBox(height: 25.h),
                  Text(
                    '啊交房贷是开发哈都开始发感慨拉屎的都死了结果来看到家啊个路口阿里国际哦机构俄文i哦啊交佛IE我减肥哦i微积分哦IE我房间哦微积分饿哦我i风景饿哦我i减肥啊疯狂了多少积分',
                    style: TextStyle(
                        color: const Color(0xFF636363), fontSize: 14.sp),
                  )
                ],
              ),
            ),
          );
        });
  }
}
