import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/model/study/course_tag_model.dart';
import 'package:npemployee/model/study/course_watch_history_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';

class CapabilityUpgradingPage extends StatefulWidget {
  final String title;
  const CapabilityUpgradingPage({super.key, required this.title});

  @override
  State<CapabilityUpgradingPage> createState() =>
      _CapabilityUpgradingPageState();
}

class _CapabilityUpgradingPageState extends State<CapabilityUpgradingPage> {
  String searchQuery = '';
  int _tabIndex = 0;

  List<CourseTagModel> tagTabs = [];

  final PageController _pageController = PageController();
  final ScrollController _scrollController = ScrollController();

  final List<GlobalKey> _tabKeyList = [];

  FocusNode searchFocusNode = FocusNode();

  void _autoScrollTabToVisible() {
    final RenderBox? tabRenderBox =
        _tabKeyList[_tabIndex].currentContext?.findRenderObject() as RenderBox?;
    if (tabRenderBox != null) {
      final tabPosition = tabRenderBox.localToGlobal(Offset.zero).dx;
      final tabWidth = tabRenderBox.size.width;
      final screenWidth = MediaQuery.of(context).size.width;
      final scrollOffset = _scrollController.offset;

      if (tabPosition < 0) {
        _scrollController.animateTo(scrollOffset + tabPosition - 16.w,
            duration: const Duration(milliseconds: 300), curve: Curves.ease);
      } else if (tabPosition + tabWidth > screenWidth) {
        _scrollController.animateTo(
            scrollOffset + (tabPosition + tabWidth - screenWidth) + 16.w,
            duration: const Duration(milliseconds: 300),
            curve: Curves.ease);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getCourseTagList();
  }

  void _getCourseTagList() {
    UserServiceProvider().getCourseTagList(
      1,
      cacheCallBack: (value) {
        _formatTagsData(value, true);
      },
      successCallBack: (value) {
        _formatTagsData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  _formatTagsData(ResultData? value, bool isCache) {
    tagTabs.clear();
    for (var e in value?.data[0]['tag']['edges']['children'] ?? []) {
      tagTabs.add(CourseTagModel(
          id: e['id'],
          name: e['name'],
          sort: e['sort'],
          status: e['status'],
          parent_id: e['parent_id']));
    }

    _tabKeyList.clear();
    _tabKeyList.addAll(tagTabs.map((e) => GlobalKey()));

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (searchFocusNode.hasFocus) {
          searchFocusNode.unfocus();
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        appBar: CommonNav(
          title: widget.title,
          rightWidget: [
            TextButton(
                onPressed: () {
                  if (searchFocusNode.hasFocus) {
                    searchFocusNode.unfocus();
                  }
                  NavigatorUtils.push(context, StudyRouter.myStarList);
                },
                child: Text('我的藏阅',
                    style: TextStyle(
                            color: const Color(0xFF333333), fontSize: 15.sp)
                        .pfRegular))
          ],
        ),
        body: Column(
          children: [
            Container(
              color: Colors.white,
              child: Column(
                children: [
                  SizedBox(height: 12.h),
                  _searchView(),
                  SizedBox(height: 20.h),
                  _tabView(),
                  SizedBox(height: 12.h),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            Expanded(
              child: tagTabs.isEmpty
                  ? const NoDataPage()
                  : PageView(
                      controller: _pageController,
                      onPageChanged: (value) {
                        setState(() {
                          _tabIndex = value;
                        });
                        _autoScrollTabToVisible();
                      },
                      children: [
                        ...tagTabs.map((e) {
                          return ContentPage(
                            id: e.id,
                            search: searchQuery,
                            searchFocusNode: searchFocusNode,
                          );
                        }),
                      ],
                    ),
            )
          ],
        ),
      ),
    );
  }

  Widget _searchView() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: TextField(
        focusNode: searchFocusNode,
        onChanged: (value) {
          if (value.isNotEmpty) return;
          setState(() {
            searchQuery = value;
          });
        },
        onSubmitted: (value) {
          setState(() {
            searchQuery = value;
          });
        },
        textInputAction: TextInputAction.search,
        decoration: InputDecoration(
          hintText: '请输入搜索内容',
          hintStyle: TextStyle(
            color: const Color(0xFFCCCCCC),
            fontSize: 15.sp,
          ).pfMedium,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF333333),
              width: 1.5,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF333333),
              width: 1.5,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF333333),
              width: 1.5,
            ),
          ),
          suffixIcon: const Icon(Icons.search),
        ),
        style: TextStyle(fontSize: 15.sp).pfMedium,
      ),
    );
  }

  Widget _tabView() {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: SingleChildScrollView(
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              ...tagTabs.map((e) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: VideoTabItem(
                      key: _tabKeyList[tagTabs.indexOf(e)],
                      title: e.name,
                      isSelect: _tabIndex == tagTabs.indexOf(e),
                      onTap: () {
                        setState(() {
                          _tabIndex = tagTabs.indexOf(e);
                        });
                        _pageController.animateToPage(_tabIndex,
                            duration: const Duration(milliseconds: 100),
                            curve: Curves.ease);
                      }),
                );
              }),
            ],
          ),
        ));
  }
}

class ContentPage extends StatefulWidget {
  final int id;
  final String search;
  final FocusNode searchFocusNode;
  const ContentPage({
    super.key,
    required this.id,
    required this.search,
    required this.searchFocusNode,
  });

  @override
  State<ContentPage> createState() => _ContentPageState();
}

class _ContentPageState extends State<ContentPage>
    with AutomaticKeepAliveClientMixin {
  late int id;
  late String searchQuery;
  late FocusNode searchFocusNode;

  List<CourseListModel> courseList = [];
  List<CourseWatchHistoryModel> courseWatchHistorys = [];

  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  final EasyRefreshController _refreshController = EasyRefreshController(
      controlFinishRefresh: true, controlFinishLoad: true);
  int page = 1;
  int size = 10;

  void _getCourseList(int tag_id, {String? keyword, bool isRefresh = true}) {
    page = isRefresh ? 1 : page + 1;
    UserServiceProvider().getCourseList(
      tag_id,
      keyword: keyword,
      page: page,
      size: size,
      cacheCallBack: (value) {
        // _formatCourseListData(value, true, isRefresh: isRefresh);
      },
      successCallBack: (value) {
        _formatCourseListData(value, false, isRefresh: isRefresh);
      },
      errorCallBack: (value) {},
    );
  }

  _formatCourseListData(ResultData? value, bool isCache,
      {bool isRefresh = true}) {
    if (isRefresh) {
      _refreshController.finishRefresh(IndicatorResult.success);
      _refreshController.resetFooter();
      courseList.clear();
    } else {
      bool hasMore = ValidatorUtils.listHasMore(value?.count, page, size);
      _refreshController.finishLoad(
          hasMore ? IndicatorResult.success : IndicatorResult.noMore);
    }

    if (!isCache) {
      for (var e in value?.data) {
        courseList.add(CourseListModel.formJson(e));
      }
      _getCourseWatchHistory();
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _getCourseWatchHistory() {
    UserServiceProvider().getCourseWatchList(
      cacheCallBack: (value) {
        _formatCourseWatchHistoryData(value, true);
      },
      successCallBack: (value) {
        _formatCourseWatchHistoryData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  _formatCourseWatchHistoryData(ResultData? value, bool isCache) {
    courseWatchHistorys.clear();
    for (var e in value?.data ?? []) {
      courseWatchHistorys.add(CourseWatchHistoryModel.formJson(e));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }

  @override
  void initState() {
    super.initState();
    id = widget.id;
    searchQuery = widget.search;
    searchFocusNode = widget.searchFocusNode;
    _getCourseList(id, keyword: searchQuery, isRefresh: true);
  }

  @override
  void didUpdateWidget(covariant ContentPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.search != oldWidget.search) {
      searchQuery = widget.search;
      _getCourseList(id, keyword: searchQuery, isRefresh: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return EasyRefresh.builder(
      controller: _refreshController,
      onRefresh: () =>
          _getCourseList(id, keyword: searchQuery, isRefresh: true),
      onLoad: () => _getCourseList(id, keyword: searchQuery, isRefresh: false),
      childBuilder: (_, physic) => courseList.isEmpty
          ? NoDataPage(physics: physic)
          : ListView.builder(
              physics: physic,
              itemCount: courseList.length,
              itemBuilder: _itemBuilder),
    );
  }

  Widget _itemBuilder(c, index) {
    CourseListModel model = courseList[index];
    String formatIntro = ValidatorUtils.delHL(model.introduction);

    Color typeColor;
    String typeString;
    if (model.media_type == 'audio') {
      typeString = '音频';
      typeColor = const Color(0xFFFFAD3B);
    } else if (model.media_type == 'video') {
      typeString = '视频';
      typeColor = const Color(0xFF0054FF);
    } else {
      typeString = '音视频';
      typeColor = const Color(0xFF18D3B3);
    }

    return GestureDetector(
      onTap: () {
        if (searchFocusNode.hasFocus) {
          searchFocusNode.unfocus();
        }
        if (model.course_type == 1) {
          //书籍
          NavigatorUtils.push(context, StudyRouter.capabilityDetail,
              arguments: {'courseId': model.id}).then((_) {
            Timer.periodic(const Duration(seconds: 1), (t) {
              t.cancel();
              _getCourseWatchHistory();
            });
          });
        } else {
          NavigatorUtils.push(context, StudyRouter.playDetail, arguments: {
            'course': model,
          }).then((_) {
            Timer.periodic(const Duration(seconds: 1), (t) {
              t.cancel();
              _getCourseWatchHistory();
            });
          });
        }
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h, left: 16.w, right: 16.w),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(16.r)),
        child: Row(
          children: [
            isLandscapeCache.containsKey(model.image)
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: CachedNetworkImage(
                      imageUrl: model.image,
                      fit: BoxFit.cover,
                      height: isLandscapeCache[model.image]! ? 64.h : 102.h,
                      width: isLandscapeCache[model.image]! ? 119.w : 73.w,
                    ),
                  )
                : FutureBuilder(
                    future: _isLandscape(model.image),
                    builder: (_, snapData) {
                      if (snapData.connectionState == ConnectionState.done) {
                        final isLandscape = snapData.data ?? true;
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: model.image,
                            fit: BoxFit.cover,
                            height: isLandscape ? 64.h : 102.h,
                            width: isLandscape ? 119.w : 73.w,
                          ),
                        );
                      }
                      return Container();
                    }),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Text(
                          model.name,
                          style: TextStyle(
                                  color: const Color(0xFF323640),
                                  fontSize: 15.sp)
                              .pfMedium,
                        ),
                      ),
                      if (courseWatchHistorys.isNotEmpty &&
                          courseWatchHistorys.first.course_id == model.id)
                        Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 3.w, vertical: 2.h),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6.r),
                                border:
                                    Border.all(color: const Color(0xFF0054FF))),
                            child: Text('上次学习',
                                style: TextStyle(
                                        color: const Color(0xFF0054FF),
                                        fontSize: 11.sp)
                                    .pfRegular)),
                    ],
                  ),
                  SizedBox(height: 6.h),
                  Text(
                    formatIntro,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                            color: const Color(0xFF999999), fontSize: 12.sp)
                        .pfRegular,
                  ),
                  SizedBox(height: 6.h),
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 1.h, horizontal: 5.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.r),
                            border: Border.all(color: typeColor, width: 0.5)),
                        child: Text(
                          typeString,
                          style: TextStyle(color: typeColor, fontSize: 10.sp)
                              .pfMedium,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text('共${model.lesson_count}节 | ',
                          style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 12.sp)
                              .pfRegular),
                      Image.asset('assets/png/xiaoxin/studyed_icon.png',
                          width: 12, height: 12),
                      Text('${model.watch_count}人已学习',
                          style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 12.sp)
                              .pfRegular),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => false;
}
