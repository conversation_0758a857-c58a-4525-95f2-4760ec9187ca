import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marqueer/marqueer.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/study/course_rank_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/person_list_item.dart';

class CapabilityStudyRankPage extends StatefulWidget {
  final int courseId;
  const CapabilityStudyRankPage({super.key, required this.courseId});

  @override
  State<CapabilityStudyRankPage> createState() =>
      _CapabilityStudyRankPageState();
}

class _CapabilityStudyRankPageState extends State<CapabilityStudyRankPage> {
  List<CourseRankModel> courseRank = [];

  void _getStudyRankData() {
    UserServiceProvider().getCourseRankList(
      courseId: widget.courseId,
      cacheCallBack: (value) {
        _formatStudyRankData(value, true);
      },
      successCallBack: (value) {
        _formatStudyRankData(value, false);
      },
    );
  }

  void _formatStudyRankData(ResultData value, bool isCache) {
    courseRank.clear();
    for (var element in value.data) {
      courseRank.add(CourseRankModel(
          total_duration: element['total_duration'],
          today_duration: element['today_duration'],
          ranking: element['ranking'],
          user: element['user']));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getStudyRankData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '学习排名'),
      body: courseRank.isEmpty
          ? const NoDataPage()
          : Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(vertical: 7.h),
                  width: ScreenUtil().screenWidth,
                  decoration: const BoxDecoration(color: Color(0xFFFFFAF2)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset('assets/png/xiaoxin/video_sort_ling.png',
                          width: 18, height: 18),
                      SizedBox(width: 7.w),
                      SizedBox(
                        width: ScreenUtil().screenWidth - 64.w,
                        height: 20.h,
                        child: Marqueer(
                            interaction: false,
                            separatorBuilder: (context, index) {
                              return SizedBox(width: 16.w);
                            },
                            child: Text('此排行榜仅为本视频的学习排行，观看时长大于一分钟才会被记录哦~',
                                style: TextStyle(
                                    color: const Color(0xFFFFAC38),
                                    fontSize: 13.sp))),
                      ),
                    ],
                  ),
                ),
                Expanded(
                    child: MediaQuery.removePadding(
                        removeTop: true,
                        context: context,
                        child: ListView.builder(
                            itemCount: courseRank.length + 1,
                            itemBuilder: (c, index) {
                              if (index == 0) {
                                int meIndex = courseRank.indexWhere((e) =>
                                    e.userId ==
                                    GlobalPreferences().userInfo?.user.id);

                                return PersonListMeItem(
                                    item: meIndex == -1
                                        ? null
                                        : courseRank[meIndex]);
                              }
                              CourseRankModel item = courseRank[index - 1];
                              return PersonListItem(
                                item: item,
                                index: index,
                              );
                            })))
              ],
            ),
    );
  }
}
