import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/capability_upgrading/capability_audio_page.dart';
import 'package:npemployee/page/capability_upgrading/capability_text_page.dart';
import 'package:npemployee/page/capability_upgrading/capability_video_page.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';

import '../../routers/study_router.dart';

class PlayDetailPage extends StatefulWidget {
  final CourseListModel course; //当前课程
  final LessonModel? lessonModel; //null非下载页面跳转
  final int? lessonId; //我的任务-播放具体章节
  final int? chapterId; //我的任务-播放具体章节

  const PlayDetailPage(
      {super.key,
      required this.course,
      this.lessonModel,
      this.lessonId,
      this.chapterId});

  @override
  State<PlayDetailPage> createState() => _PlayDetailPageState();
}

class _PlayDetailPageState extends State<PlayDetailPage> {
  int _tabIndex = 1;

  List<ChapterModel> chapters = [];
  List<LessonModel> lessons = [];
  LessonModel? playingLesson;
  bool _wasPlaying = true;

  List<String> _getMediaTypeList(List<MediaModel> medias) {
    List<String> mediaTypes = [];
    for (var element in medias) {
      String mediaType = element.media_type;
      if (!mediaTypes.contains(mediaType)) {
        mediaTypes.add(mediaType);
      }
    }
    return mediaTypes;
  }

  void _getCourseChapter() {
    EasyLoading.show();
    UserServiceProvider().getCourseChapter(widget.course.id,
        cacheCallBack: (value) {
      EasyLoading.dismiss();
      _formatCourseChapterData(value, true);
    }, successCallBack: (value) {
      EasyLoading.dismiss();
      _formatCourseChapterData(value, false);
    }, errorCallBack: (value) {
      EasyLoading.dismiss();
      EasyLoading.showError(value.msg);
    });
  }

  _formatCourseChapterData(ResultData value, bool isCache) {
    chapters.clear();
    lessons.clear();
    for (var e in value.data) {
      ChapterModel chapterModel = ChapterModel.formJson(e);
      chapters.add(chapterModel);
      for (var lesson in chapterModel.lesson) {
        lessons.add(lesson);
      }
    }
    if (lessons.isNotEmpty && widget.lessonModel == null) {
      playingLesson = lessons.first;
    }

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getCourseChapter();

    if (widget.lessonModel != null) {
      playingLesson = widget.lessonModel;
    }

    //书籍默认显示音频页面
    if (widget.course.course_type == 1 &&
        _getMediaTypeList(playingLesson!.media).contains('mp3')) {
      _tabIndex = 2;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SafeArea(
      child: widget.course.course_type == 2
          ? Column(
              children: [
                Expanded(
                    child: CapabilityVideoPage(
                  courseId: widget.course.id,
                  playLesson: playingLesson,
                  playLessonId: widget.lessonId,
                  playChapterId: widget.chapterId,
                ))
              ],
            )
          : Column(
              children: [
                if (widget.course.course_type == 1) _tabView(),
                if (_getMediaTypeList(playingLesson?.media ?? [])
                    .contains('mp4'))
                  Expanded(
                      child: CapabilityVideoPage(
                          playStatus: _wasPlaying,
                          courseId: widget.course.id,
                          playLesson: playingLesson)),
                if (_getMediaTypeList(playingLesson?.media ?? [])
                    .contains('mp3'))
                  Expanded(
                      child: CapabilityAudioPage(
                          courseId: widget.course.id,
                          course: widget.course,
                          playLesson: playingLesson!)),
                if (_getMediaTypeList(playingLesson!.media).contains('txt'))
                  const Expanded(child: CapabilityTextPage()),
              ],
            ),
    ));
  }

  Widget _tabView() {
    return Stack(
      alignment: Alignment.bottomLeft,
      children: [
        Positioned(
          right: 16,
          top: 0,
          bottom: 0,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              setState(() {
                _wasPlaying = false;
              });
              NavigatorUtils.push(context, StudyRouter.downloadList,
                  arguments: {
                    'chapters': chapters,
                    'course': widget.course,
                    'type': 2
                  }).then((v) {
                setState(() {
                  _wasPlaying = true;
                });
              });
            },
            child: SizedBox(
              width: 20,
              height: 20,
              child: Image.asset('assets/png/xiaoxin/audio_download.png'),
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            if (_getMediaTypeList(playingLesson!.media).contains('mp4'))
              VideoTabItem(
                title: '视频',
                isSelect: _tabIndex == 1,
                onTap: () {
                  setState(() {
                    _tabIndex = 1;
                  });
                },
              ),
            if (_getMediaTypeList(playingLesson!.media).contains('mp3'))
              VideoTabItem(
                title: '音频',
                isSelect: _tabIndex == 2,
                onTap: () {
                  setState(() {
                    _tabIndex = 2;
                  });
                },
              ),
            if (_getMediaTypeList(playingLesson!.media).contains('txt'))
              VideoTabItem(
                title: '文本',
                isSelect: _tabIndex == 3,
                onTap: () {
                  setState(() {
                    _tabIndex = 3;
                  });
                },
              ),
          ],
        ),
        IconButton(
          onPressed: () => NavigatorUtils.pop(context),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF000000),
          ),
          iconSize: 18,
        ),
      ],
    );
  }
}
