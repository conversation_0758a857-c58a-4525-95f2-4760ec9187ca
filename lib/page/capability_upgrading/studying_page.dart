import 'package:flutter/material.dart';
import 'package:npemployee/page/study/video_play_page.dart';
import 'package:npemployee/widget/common_nav.dart';

class StudyingPage extends StatefulWidget {
  final int courseId;
  const StudyingPage({super.key, required this.courseId});

  @override
  State<StudyingPage> createState() => _StudyingPageState();
}

class _StudyingPageState extends State<StudyingPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '正在学习'),
      body: VideoStudyingPage(courseId: widget.courseId),
    );
  }
}
