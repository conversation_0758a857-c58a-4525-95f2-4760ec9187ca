import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/model/study/course_watch_history_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';

class MyStartPage extends StatefulWidget {
  const MyStartPage({super.key});

  @override
  State<MyStartPage> createState() => _MyStartPageState();
}

class _MyStartPageState extends State<MyStartPage> {
  String searchQuery = '';

  List<CourseListModel> courseList = [];
  List<CourseWatchHistoryModel> courseWatchHistorys = [];

  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  List<CourseListModel> get filteredItems {
    return courseList.where((item) => item.name.contains(searchQuery)).toList();
  }

  final EasyRefreshController _refreshController = EasyRefreshController();

  FocusNode searchNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _getMyStarList();
    _getCourseWatchHistory();
  }

  void _getMyStarList({String? keyword}) {
    UserServiceProvider().getMyStarList(
      cacheCallBack: (value) {
        _formatMyStarListData(value, true);
      },
      successCallBack: (value) {
        _formatMyStarListData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  void _getCourseWatchHistory() {
    UserServiceProvider().getCourseWatchList(
      cacheCallBack: (value) {
        _formatCourseWatchHistoryData(value, false);
      },
      successCallBack: (value) {
        _formatCourseWatchHistoryData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  _formatMyStarListData(ResultData? value, bool isCache) {
    courseList.clear();
    for (var e in value?.data) {
      for (var element in e['course']) {
        courseList.add(CourseListModel.formJson(element));
      }
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatCourseWatchHistoryData(ResultData? value, bool isCache) {
    courseWatchHistorys.clear();
    for (var e in value?.data) {
      courseWatchHistorys.add(CourseWatchHistoryModel.formJson(e));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (searchNode.hasFocus) {
          searchNode.unfocus();
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        appBar: const CommonNav(title: '我的藏阅'),
        body: Column(
          children: [
            Container(
              color: Colors.white,
              child: Column(
                children: [
                  SizedBox(height: 12.h),
                  _searchView(),
                  SizedBox(height: 12.h),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            Expanded(
                child: EasyRefresh.builder(
              controller: _refreshController,
              onRefresh: () {
                _getMyStarList();
                _getCourseWatchHistory();
              },
              childBuilder: (_, phsic) => filteredItems.isEmpty
                  ? NoDataPage(physics: phsic)
                  : ListView.builder(
                      physics: phsic,
                      itemCount: filteredItems.length,
                      itemBuilder: _itemBuilder),
            ))
          ],
        ),
      ),
    );
  }

  Widget _searchView() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: TextField(
        focusNode: searchNode,
        onChanged: (value) {
          setState(() {
            searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: '请输入搜索内容',
          hintStyle: TextStyle(
            color: Color(0xFFCCCCCC),
            fontSize: 15.sp,
          ).pfMedium,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF333333),
              width: 1.5,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF333333),
              width: 1.5,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(0xFF333333),
              width: 1.5,
            ),
          ),
          suffixIcon: const Icon(Icons.search),
        ),
        style: TextStyle(fontSize: 15.sp).pfMedium,
      ),
    );
  }

  Widget _itemBuilder(c, index) {
    CourseListModel model = filteredItems[index];
    return GestureDetector(
      onTap: () {
        if (searchNode.hasFocus) {
          searchNode.unfocus();
        }
        if (model.course_type == 1) {
          //书籍
          NavigatorUtils.push(context, StudyRouter.capabilityDetail,
              arguments: {'courseId': model.id}).then((_) {
            _getCourseWatchHistory();
          });
        } else {
          NavigatorUtils.push(context, StudyRouter.playDetail, arguments: {
            'course': model,
          }).then((_) {
            _getCourseWatchHistory();
          });
        }
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h, left: 16.w, right: 16.w),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(16.r)),
        child: Row(
          children: [
            isLandscapeCache.containsKey(model.image)
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: CachedNetworkImage(
                      imageUrl: model.image,
                      fit: BoxFit.cover,
                      height: isLandscapeCache[model.image]! ? 64.h : 102.h,
                      width: isLandscapeCache[model.image]! ? 119.w : 73.w,
                    ),
                  )
                : FutureBuilder(
                    future: _isLandscape(model.image),
                    builder: (_, snapData) {
                      if (snapData.connectionState == ConnectionState.done) {
                        final isLandscape = snapData.data ?? true;
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: model.image,
                            fit: BoxFit.cover,
                            height: isLandscape ? 64.h : 102.h,
                            width: isLandscape ? 119.w : 73.w,
                          ),
                        );
                      }
                      return Container();
                    }),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Text(
                          model.name,
                          style: TextStyle(
                                  color: const Color(0xFF323640),
                                  fontSize: 15.sp)
                              .pfMedium,
                        ),
                      ),
                      if (courseWatchHistorys.isNotEmpty &&
                          courseWatchHistorys.first.course_id == model.id)
                        Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 3.w, vertical: 2.h),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6.r),
                                border:
                                    Border.all(color: const Color(0xFF0054FF))),
                            child: Text('上次学习',
                                style: TextStyle(
                                        color: const Color(0xFF0054FF),
                                        fontSize: 11.sp)
                                    .pfRegular)),
                    ],
                  ),
                  SizedBox(height: 6.h),
                  Text(
                    model.introduction
                            ?.replaceAll('<hl>', '')
                            .replaceAll('</hl>', '') ??
                        '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                            color: const Color(0xFF999999), fontSize: 12.sp)
                        .pfRegular,
                  ),
                  SizedBox(height: 6.h),
                  Row(
                    children: [
                      Text('共${model.lesson_count}节 | ',
                          style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 12.sp)
                              .pfRegular),
                      Image.asset('assets/png/xiaoxin/studyed_icon.png',
                          width: 12, height: 12),
                      Text('${model.watch_count}人已学习',
                          style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 12.sp)
                              .pfRegular),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }
}
