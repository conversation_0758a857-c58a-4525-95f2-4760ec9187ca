import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:just_audio/just_audio.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/manager/close_timer_manager.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/model/study/course_watch_history_model.dart';
import 'package:npemployee/model/study/course_watching_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/audio_bloc/audio_event.dart';
import 'package:npemployee/provider/audio_bloc/audio_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/chapters_tree_dialog.dart';
import 'package:npemployee/widget/study/video_chapter_menu.dart';
import 'package:npemployee/widget/study/video_chapter_menu2.dart';

class CapabilityDetailPage extends StatefulWidget {
  final int courseId;
  final LessonModel? lessonModel;
  final ChapterModel? chapterModel;
  final int? lessonId;
  final int? chapterId;
  const CapabilityDetailPage(
      {super.key,
      required this.courseId,
      this.lessonModel,
      this.chapterModel,
      this.lessonId,
      this.chapterId});

  @override
  State<CapabilityDetailPage> createState() => _CapabilityDetailPageState();
}

class _CapabilityDetailPageState extends State<CapabilityDetailPage>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  List<ChapterModel> chapters = []; //章节原始数据
  List<LessonModel> courseChapters = []; //所有小节数据
  List<CourseListModel> myStarList = []; //我的收藏列表
  List<CourseWatchHistoryModel> courseWatchHistorys = []; //历史学习列表
  List<CourseWatchingModel> courseWatchings = []; //正在学习列表
  List<AudioSource> audioList = [];

  LessonModel? playingLesson; //正在播放lesson

  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  late AnimationController _controller; //头像播放时转圈效果

  AudioPlayer audioPlayer = AudioPlayer();

  int currentPosition = -1;

  StreamSubscription? subscription;
  StreamSubscription? audioStateSubscription;
  StreamSubscription? audioPositionSubscription;

  CourseListModel? course;

  Timer? _timer;
  int _timerSeconds = 0;

  Timer? _closeTimer;

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_timerSeconds >= 60) {
        timer.cancel();
        _timerSeconds = 0;
        await _addWatchSaveDate();
        _startTimer();
        return;
      }
      _timerSeconds++;
    });
  }

  void _cancelTimer() {
    _timer?.cancel();
    _timer = null;
  }

  void _getCourseDetail() {
    UserServiceProvider().getCourseDetail(widget.courseId,
        cacheCallBack: (value) {
      // _formatCourseDetailData(value, true);
    }, successCallBack: (value) {
      _formatCourseDetailData(value, false);
    }, errorCallBack: (value) {});
  }

  void _getCourseChapter() {
    UserServiceProvider().getCourseChapter(
      widget.courseId,
      cacheCallBack: (value) {
        // _formatCourseChapterData(value, true);
      },
      successCallBack: (value) {
        _formatCourseChapterData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  void _getMyStarList({String? keyword}) {
    UserServiceProvider().getMyStarList(
      cacheCallBack: (value) {
        _formatMyStarListData(value, true);
      },
      successCallBack: (value) {
        _formatMyStarListData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  void _getCourseWatchHistory() {
    UserServiceProvider().getCourseWatchList(
      course_id: widget.courseId,
      cacheCallBack: (value) {
        // _formatCourseWatchHistoryData(value, false);
      },
      successCallBack: (value) {
        _formatCourseWatchHistoryData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  void _getWatchingList() {
    UserServiceProvider().getCourseWatchingList(widget.courseId,
        cacheCallBack: (value) {
      _formatCourseWatchingData(value, true);
    }, successCallBack: (value) {
      _formatCourseWatchingData(value, false);
    }, errorCallBack: (value) {});
  }

  _formatCourseDetailData(ResultData value, bool isCache) {
    course = CourseListModel.formJson(value.data);
    _getCourseChapter();
    _getMyStarList();
    _getWatchingList();
  }

  _formatCourseChapterData(ResultData value, bool isCache) async {
    chapters.clear();
    courseChapters.clear();
    audioList.clear();
    for (Map e in value.data) {
      ChapterModel chapterModel = ChapterModel.formJson(e);
      chapters.add(chapterModel);
      for (var element in chapterModel.lesson) {
        courseChapters.add(element);
        if (course?.course_type == 1) {
          int index = element.media.indexWhere((e) => e.media_type == 'mp3');
          if (index != -1) {
            audioList.add(AudioSource.uri(Uri.parse(element.media
                .firstWhere((e) => e.media_type == 'mp3')
                .media_url)));
          }
        }
      }
    }
    if (course?.course_type == 1) {
      await audioPlayer
          .setAudioSource(ConcatenatingAudioSource(children: audioList));
    }
    _getCourseWatchHistory();
  }

  _formatMyStarListData(ResultData? value, bool isCache) {
    myStarList.clear();
    for (var e in value?.data) {
      for (var element in e['course']) {
        myStarList.add(CourseListModel.formJson(element));
      }
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatCourseWatchHistoryData(ResultData? value, bool isCache) {
    courseWatchHistorys.clear();
    for (var e in value?.data ?? []) {
      courseWatchHistorys.add(CourseWatchHistoryModel.formJson(e));
    }
    if (courseWatchHistorys.isNotEmpty) {
      int index = courseChapters
          .indexWhere((e) => e.id == courseWatchHistorys.first.lesson_id);
      BlocManager().audioBloc.add(AudioPlayingEvent(courseChapters[index]));
    } else {
      BlocManager().audioBloc.add(AudioPlayingEvent(courseChapters.first));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatCourseWatchingData(ResultData? value, bool isCache) {
    courseWatchings.clear();
    for (var element in value?.data) {
      courseWatchings.add(CourseWatchingModel.formJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  //提交看课记录
  void _addWatchSave() {
    if (currentPosition > 0) {
      debugPrint('** 上传看课记录:${playingLesson?.id} -- $currentPosition');

      if (playingLesson != null) {
        UserServiceProvider().addCourseWatchSave(playingLesson!.chapter_id,
            playingLesson!.course_id, currentPosition, playingLesson!.id);
      }
    }
  }

  //初始化看课时长
  void _initWatchSave() async {
    debugPrint('** 初始化看课时长:${playingLesson?.id} -- $currentPosition');
    await UserServiceProvider()
        .initCourseWatchSaveDate(playingLesson!.course_id);
    _getWatchingList();
  }

  //添加看课时长
  Future<ResultData?> _addWatchSaveDate() async {
    debugPrint('** 上传看课时长:${playingLesson?.id} -- $currentPosition');
    ResultData? data = await UserServiceProvider().addCourseWatchSaveDate(
        playingLesson!.chapter_id,
        playingLesson!.course_id,
        currentPosition,
        playingLesson!.id);
    return data;
  }

  //音乐播放进度
  void _audioPlayerStreams() {
    //旋转动画
    audioStateSubscription = audioPlayer.playerStateStream.listen((state) {
      if (state.playing) {
        _cancelTimer();
        _startTimer();
        _controller.repeat();
      } else {
        _cancelTimer();
        _controller.stop();
      }
    });

    audioPositionSubscription = audioPlayer.positionStream.listen((data) {
      final position = data.inSeconds;
      currentPosition = position;
      BlocManager().audioBloc.add(AudioProgressEvent(position));
      if (playingLesson != null &&
          currentPosition >= playingLesson!.total_time) {
        int index = courseChapters
            .indexWhere((lesson) => lesson.id == playingLesson?.id);
        if (index != -1 && index < courseChapters.length - 1) {
          BlocManager()
              .audioBloc
              .add(AudioPlayingEvent(courseChapters[index + 1]));
        }
      }
    });
  }

  void _audioPlayingListen() {
    subscription = BlocManager().audioBloc.stream.listen((state) async {
      if (state.type == AudioEventType.playingLesson &&
          state.playingLesson != null) {
        debugPrint('capability_detail切换小节 -- ${state.playingLesson?.id}');
        debugPrint(
            'capability_detail之前小节 -- ${playingLesson?.id} -- $currentPosition');
        if (playingLesson?.id == state.playingLesson?.id) {
          return;
        }
        _addWatchSave();
        await audioPlayer.stop();
        setState(() {
          playingLesson = state.playingLesson;
        });
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          int index = courseChapters
              .indexWhere((lesson) => lesson.id == playingLesson?.id);
          if (index != -1) {
            ResultData? res = await UserServiceProvider()
                .getCourseWatchListAsync(
                    course_id: playingLesson!.course_id,
                    chapter_id: playingLesson!.chapter_id,
                    lesson_id: playingLesson!.id);
            if (res?.data != null) {
              //从上次观看历史开始
              int lastDuration = res?.data.first['duration'];
              if (lastDuration >= (playingLesson?.total_time ?? 0)) {
                //上次已看完，重新开始
                lastDuration = 0;
              }
              await audioPlayer.seek(Duration(seconds: lastDuration),
                  index: index);
            } else {
              await audioPlayer.seek(Duration.zero, index: index);
            }
          } else {
            await audioPlayer.seek(Duration.zero, index: 0);
          }
          audioPlayer.play();
          _initWatchSave();
          _cancelTimer();
          _timerSeconds = 0;
          _startTimer();
        });
      }
    });
  }

  List<String> _getMediaTypes() {
    List<String> mediaTypes = [];
    for (var lesson in courseChapters) {
      for (var media in lesson.media) {
        if (!mediaTypes.contains(media.media_type)) {
          mediaTypes.add(media.media_type);
        }
      }
    }
    return mediaTypes;
  }

  bool get onlyMp4 =>
      _getMediaTypes().isNotEmpty &&
          (_getMediaTypes().length == 1 && _getMediaTypes().first == 'mp4' ||
              _getMediaTypes().first == "m3u8") ||
      (_getMediaTypes().length == 2 &&
          _getMediaTypes().contains("mp4") &&
          _getMediaTypes().contains("m3u8"));
  bool get onlyTxt =>
      _getMediaTypes().length == 1 && _getMediaTypes().first == 'txt';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    BlocManager().audioBloc.add(AudioInitEvent(audioPlayer));
    if (widget.lessonModel != null) {
      BlocManager().audioBloc.add(AudioPlayingEvent(widget.lessonModel!));
    }
    _getCourseDetail();
    _audioPlayerStreams();
    _audioPlayingListen();
    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 5));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('App 在前台');
        break;
      case AppLifecycleState.inactive:
        debugPrint('App 非活动状态，比如打电话');
        break;
      case AppLifecycleState.paused:
        debugPrint('App 退到后台');
        _addWatchSave();
        break;
      case AppLifecycleState.detached:
        debugPrint('App 应用完全分离');
        break;
      default:
    }
  }

  @override
  void dispose() {
    CloseTimerManager.cancel();
    _cancelTimer();
    _addWatchSave();
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    audioPlayer.dispose();
    audioStateSubscription?.cancel();
    audioStateSubscription = null;
    audioPositionSubscription?.cancel();
    audioPositionSubscription = null;
    subscription?.cancel();
    subscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, //const Color(0xFFF7F8FD)
      appBar: CommonNav(title: '', rightWidget: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            if (audioPlayer.playing) {
              audioPlayer.pause();
            }
            NavigatorUtils.push(context, StudyRouter.downloadList, arguments: {
              'chapters': chapters,
              'course': course,
              'type': onlyMp4 ? 2 : 1
            }).then((v) {
              if (!audioPlayer.playing) {
                audioPlayer.play();
              }
            });
          },
          child: SizedBox(
            width: 20,
            height: 20,
            child: Image.asset('assets/png/xiaoxin/audio_download.png'),
          ),
        ),
        SizedBox(width: 34.w),
        GestureDetector(
          onTap: () {
            EasyLoading.show();
            UserServiceProvider()
                .starOrCancelCourse(widget.courseId)
                .then((value) {
              EasyLoading.dismiss();
              if (value?.code != 0) {
                EasyLoading.showError(value!.msg);
                return;
              }
              _getMyStarList();
            });
          },
          child: SizedBox(
            width: 20,
            height: 20,
            child:
                myStarList.indexWhere((star) => star.id == widget.courseId) !=
                        -1
                    ? Image.asset('assets/png/xiaoxin/collect.png')
                    : Image.asset('assets/png/xiaoxin/uncollect.png'),
          ),
        ),
        SizedBox(width: 16.w),
      ]),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _topView(),
          SizedBox(height: 16.h),
          if (chapters.isNotEmpty)
            Expanded(
              child: VideoChapterMenu2(
                datas: chapters,
                selected: playingLesson,
                courseId: widget.courseId,
                lessons: courseChapters,
                onChange: (l) {
                  if (onlyMp4) {
                    NavigatorUtils.push(context, StudyRouter.playDetail,
                        arguments: {
                          'course': course,
                          'lesson': l,
                        }).then((value) {
                      Timer.periodic(const Duration(seconds: 1), (t) {
                        t.cancel();
                        _getCourseWatchHistory();
                        _getWatchingList();
                      });
                    });
                  } else if (onlyTxt) {
                  } else {
                    BlocManager().audioBloc.add(AudioPlayingEvent(l));
                  }
                },
              ),
            ),
        ],
      ),
      bottomNavigationBar: _playView(),
    );
  }

  Widget? _playView() {
    if (onlyMp4 || onlyTxt || playingLesson == null) {
      return null;
    }
    return Container(
      padding: EdgeInsets.fromLTRB(
          16.w, 10.h, 16.w, 10.h + ScreenUtil().bottomBarHeight),
      color: Colors.white,
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              NavigatorUtils.push(context, StudyRouter.playDetail,
                      arguments: {'course': course, 'lesson': playingLesson})
                  .then((value) {
                Timer.periodic(const Duration(seconds: 1), (t) {
                  t.cancel();
                  _getCourseWatchHistory();
                });
              });
            },
            child: Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  width: 42,
                  height: 42,
                  decoration: BoxDecoration(
                      color: const Color(0xFF161214),
                      borderRadius: BorderRadius.circular(21)),
                ),
                if (course?.image != null)
                  ClipOval(
                    child: Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                          color: const Color(0xFF161214),
                          borderRadius: BorderRadius.circular(15)),
                      child: RotationTransition(
                          turns: _controller,
                          child: CachedNetworkImage(
                              imageUrl: course!.image, fit: BoxFit.cover)),
                    ),
                  )
              ],
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: GestureDetector(
              onTap: () {
                NavigatorUtils.push(context, StudyRouter.playDetail,
                        arguments: {'course': course, 'lesson': playingLesson})
                    .then((value) {
                  Timer.periodic(const Duration(seconds: 1), (t) {
                    t.cancel();
                    _getCourseWatchHistory();
                  });
                });
              },
              child: Text(playingLesson == null ? "" : playingLesson!.title,
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 14.sp)
                          .pfRegular),
            ),
          ),
          SizedBox(width: 8.w),
          GestureDetector(
            onTap: () {
              if (course?.course_type == 1) {
                if (audioPlayer.playing) {
                  audioPlayer.pause();
                } else {
                  audioPlayer.play();
                }
              }
              setState(() {});
            },
            behavior: HitTestBehavior.opaque,
            child: SizedBox(
              width: 21,
              height: 21,
              child: Image.asset(audioPlayer.playing
                  ? 'assets/png/xiaoxin/audio_pause.png'
                  : 'assets/png/xiaoxin/audio_play.png'),
            ),
          ),
          SizedBox(width: 20.w),
          GestureDetector(
            onTap: () => _showMenuBottomSheet(),
            behavior: HitTestBehavior.opaque,
            child: SizedBox(
              width: 21,
              height: 21,
              child: Image.asset('assets/png/xiaoxin/audio_menu.png'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _topView() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Row(
        children: [
          if (course?.image != null)
            isLandscapeCache.containsKey(course?.image)
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: CachedNetworkImage(
                      imageUrl: course!.image,
                      fit: BoxFit.cover,
                      height: isLandscapeCache[course?.image]! ? 64.h : 162.h,
                      width: isLandscapeCache[course?.image]! ? 119.w : 116.w,
                    ),
                  )
                : FutureBuilder(
                    future: _isLandscape(course!.image),
                    builder: (_, snapData) {
                      if (snapData.connectionState == ConnectionState.done) {
                        final isLandscape = snapData.data ?? true;
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: course!.image,
                            fit: BoxFit.cover,
                            height: isLandscape ? 64.h : 162.h,
                            width: isLandscape ? 119.w : 116.w,
                          ),
                        );
                      }
                      return Container();
                    }),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (course?.name != null)
                      Expanded(
                        child: Text(
                          course!.name,
                          style: TextStyle(
                                  color: const Color(0xFF323640),
                                  fontSize: 17.sp)
                              .pfMedium,
                        ),
                      ),
                  ],
                ),
                if (course?.introduction != null) SizedBox(height: 10.h),
                if (course?.introduction != null)
                  RichText(
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      text: ValidatorUtils.delHL(course?.introduction).length >
                              24
                          ? '${ValidatorUtils.delHL(course?.introduction).substring(0, 24)}...'
                          : ValidatorUtils.delHL(course?.introduction),
                      style: TextStyle(
                              color: const Color(0xFF999999), fontSize: 13.sp)
                          .pfRegular,
                      children: [
                        if (ValidatorUtils.delHL(course?.introduction).length >
                            24)
                          WidgetSpan(
                            alignment: PlaceholderAlignment.baseline,
                            baseline: TextBaseline.alphabetic,
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () => _showBottomSheet(),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(width: 5.w),
                                  Text('展开',
                                      style: TextStyle(
                                              color: const Color(0xFF4A7FE9),
                                              fontSize: 13.sp)
                                          .pfRegular),
                                  SizedBox(width: 3.w),
                                  Image.asset(
                                      'assets/png/xiaoxin/audio_drop.png',
                                      width: 8,
                                      height: 8),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                SizedBox(height: 6.h),
                if (course?.lesson_count != null)
                  Row(
                    children: [
                      Text('${course?.lesson_count}节课时  |  ',
                          style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 12.sp)
                              .pfRegular),
                      Image.asset('assets/png/xiaoxin/studyed_icon.png',
                          width: 12, height: 12),
                      Text('${course?.watch_count}人已学习',
                          style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 12.sp)
                              .pfRegular),
                    ],
                  ),
                SizedBox(height: 20.h),
                Container(
                  decoration: BoxDecoration(
                      color: const Color(0xFFF2F4FF),
                      borderRadius: BorderRadius.circular(14.r)),
                  padding:
                      EdgeInsets.symmetric(horizontal: 13.w, vertical: 6.h),
                  child: Text(
                    '${courseWatchings.length}人正在学习',
                    style: TextStyle(
                            color: const Color(0xFF0054FF), fontSize: 12.sp)
                        .pfRegular,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  void _showBottomSheet() {
    String formatIntro = course?.introduction?.replaceAll('<hl>', '<h1>') ?? '';
    formatIntro = formatIntro.replaceAll('</hl>', '</h1>');
    showModalBottomSheet(
        context: context,
        builder: (_) {
          return ClipRRect(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r)),
            child: Container(
              color: Colors.white,
              width: ScreenUtil().screenWidth,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 16.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Stack(
                        children: [
                          Positioned(
                              top: 14.h,
                              child: Image.asset(
                                  'assets/png/xiaoxin/sort_bac.png',
                                  width: 125.w)),
                          Text(
                            '书籍简介',
                            style: TextStyle(
                                color: const Color(0xFF333333),
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w500),
                          )
                        ],
                      ),
                      IconButton(
                          onPressed: () => NavigatorUtils.pop(_),
                          icon: const Icon(Icons.close))
                    ],
                  ),
                  Html(data: formatIntro),
                ],
              ),
            ),
          );
        });
  }

  void _showMenuBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return FractionallySizedBox(
          heightFactor: 0.5,
          child: ChaptersTreeDialog(
            datas: chapters,
            selected: playingLesson,
            courseId: widget.courseId,
          ),
        );
      },
    );
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }
}
