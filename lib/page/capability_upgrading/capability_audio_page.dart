import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:just_audio/just_audio.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/model/study/course_watch_history_model.dart';
import 'package:npemployee/model/study/course_watching_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/audio_bloc/audio_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/capability_upgrading/capability_timer.dart';
import 'package:npemployee/widget/capability_upgrading/custom_drop_down.dart';
import 'package:npemployee/widget/capability_upgrading/music_progress.dart';
import 'package:npemployee/widget/study/audio_studying_widget.dart';
import 'package:npemployee/widget/study/chapters_tree_dialog.dart';
import 'package:npemployee/widget/study/video_catalog_item.dart';
import 'package:share_plus/share_plus.dart';
// import 'package:share_plus/share_plus.dart';

class CapabilityAudioPage extends StatefulWidget {
  final int courseId; //当前课程
  final CourseListModel course;
  final LessonModel playLesson;

  const CapabilityAudioPage(
      {super.key,
      required this.courseId,
      required this.playLesson,
      required this.course});

  @override
  State<CapabilityAudioPage> createState() => _CapabilityAudioPageState();
}

class _CapabilityAudioPageState extends State<CapabilityAudioPage> {
  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  StreamSubscription? subscription;

  late LessonModel playLesson;
  late CourseListModel course;
  List<ChapterModel> chapters = [];
  List<LessonModel> lessons = [];
  List<CourseWatchHistoryModel> watchs = [];

  late AudioPlayer audioPlayer;

  void _getCourseDetail() {
    UserServiceProvider().getCourseDetail(widget.courseId,
        cacheCallBack: (value) {
      // _formatCourseDetailData(value, true);
    }, successCallBack: (value) {
      _formatCourseDetailData(value, false);
    }, errorCallBack: (value) {});
  }

  void _getCourseChapter() {
    UserServiceProvider().getCourseChapter(widget.courseId,
        cacheCallBack: (value) {
      _formatCourseChapterData(value, true);
    }, successCallBack: (value) {
      _formatCourseChapterData(value, false);
    }, errorCallBack: (value) {});
  }

  void _getCourseWatchHistory() {
    UserServiceProvider().getCourseWatchList(
      course_id: widget.courseId,
      cacheCallBack: (value) {
        _formatCourseWatchHistoryData(value, true);
      },
      successCallBack: (value) {
        _formatCourseWatchHistoryData(value, false);
      },
      errorCallBack: (value) {},
    );
  }

  _formatCourseDetailData(ResultData value, bool isCache) {
    course = CourseListModel.formJson(value.data);
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatCourseChapterData(ResultData value, bool isCache) {
    chapters.clear();
    lessons.clear();
    for (var e in value.data) {
      ChapterModel chapterModel = ChapterModel.formJson(e);
      chapters.add(chapterModel);
      for (var lesson in chapterModel.lesson) {
        lessons.add(lesson);
      }
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  _formatCourseWatchHistoryData(ResultData? value, bool isCache) {
    watchs.clear();
    for (var e in value?.data ?? []) {
      watchs.add(CourseWatchHistoryModel.formJson(e));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _audioPlayingListen() {
    subscription = BlocManager().audioBloc.stream.listen((state) {
      if (state.type == AudioEventType.playingLesson &&
          state.playingLesson != null) {
        setState(() {
          playLesson = state.playingLesson!;
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    playLesson = widget.playLesson;
    course = widget.course;
    audioPlayer = BlocManager().audioBloc.audioPlayer!;

    _getCourseDetail();
    _getCourseChapter();
    _getCourseWatchHistory();

    _audioPlayingListen();
  }

  @override
  void dispose() {
    subscription?.cancel();
    subscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // if (course == null || lessons.isEmpty) {
    //   return const NoDataPage();
    // }
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            SizedBox(height: 35.h),
            isLandscapeCache.containsKey(course?.image)
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: CachedNetworkImage(
                      imageUrl: course!.image,
                      fit: BoxFit.cover,
                      height: isLandscapeCache[course?.image]! ? 150.h : 208.h,
                      width: isLandscapeCache[course?.image]! ? 200.w : 149.w,
                    ),
                  )
                : FutureBuilder(
                    future: _isLandscape(course!.image),
                    builder: (_, snapData) {
                      if (snapData.connectionState == ConnectionState.done) {
                        final isLandscape = snapData.data ?? true;
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: course.image,
                            fit: BoxFit.cover,
                            height: isLandscape ? 150.h : 208.h,
                            width: isLandscape ? 200.w : 149.w,
                          ),
                        );
                      }
                      return Container();
                    }),
            SizedBox(height: 39.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Text(
                    playLesson.title,
                    style: TextStyle(
                            color: const Color(0xFF323640), fontSize: 15.sp)
                        .pfSemiBold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 6.h),
            SizedBox(
              width: double.infinity,
              child: Text(
                ValidatorUtils.delHL(course.introduction),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style:
                    TextStyle(color: const Color(0xFF999999), fontSize: 12.sp)
                        .pfRegular,
              ),
            ),
            SizedBox(height: 55.h),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 25.0),
              child: _playView(),
            ),
            SizedBox(height: 27.h),
            MusicProgress(
              totalTime: playLesson.total_time,
              lessons: lessons,
              lesson: playLesson,
            ),
            const Expanded(child: SizedBox()),
            AudioStudyingWidget(courseId: widget.courseId),
            SizedBox(height: 16.h),
          ],
        ));
  }

  Widget _playView() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _topOptionsButton('下载', 'audio_download1.png'),
            _topOptionsButton('倍速', 'audio_speed.png'),
            _topOptionsButton('定时', 'audio_timer.png'),
            _topOptionsButton('分享', 'audio_share.png'),
            _topOptionsButton('目录', 'audio_menu.png')
          ],
        )
      ],
    );
  }

  Widget _topOptionsButton(String title, String imageUrl) {
    if (title == '倍速') {
      return CustomDropdown(
        value: audioPlayer.speed,
        onChange: (p0) {
          audioPlayer.setSpeed(p0);
        },
      );
    } else if (title == '定时') {
      return const CapabilityTimer();
    } else {
      return GestureDetector(
        onTap: () {
          if (title == '分享') {
            Clipboard.setData(
                ClipboardData(text: playLesson.media.first.media_url));
            ToastUtils.show('音频链接已复制到剪贴板');
            // Share.shareUri(Uri.parse(playLesson.media.first.media_url));
          } else if (title == '目录') {
            _showMenuBottomSheet();
          } else {
            if (audioPlayer.playing) {
              audioPlayer.pause();
            }
            NavigatorUtils.push(context, StudyRouter.downloadList, arguments: {
              'chapters': chapters,
              'course': course,
              'type': 1
            }).then((v) {
              if (!audioPlayer.playing) {
                audioPlayer.play();
              }
            });
          }
        },
        child: Column(
          children: [
            SizedBox(
                height: 21, child: Image.asset('assets/png/xiaoxin/$imageUrl')),
            const SizedBox(height: 5),
            Text(title,
                style:
                    TextStyle(color: const Color(0xFF737373), fontSize: 10.sp)
                        .pfMedium),
          ],
        ),
      );
    }
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }

  void _showMenuBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return FractionallySizedBox(
          heightFactor: 0.5,
          child: ChaptersTreeDialog(
            datas: chapters,
            selected: playLesson,
            courseId: widget.courseId,
          ),
        );
      },
    );
  }
}
