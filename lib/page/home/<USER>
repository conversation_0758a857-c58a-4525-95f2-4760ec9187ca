import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/mine/guest_model.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:npemployee/widget/mine/profile_option_cell.dart';

class GuestPage extends StatefulWidget {
  final GuestModel? guestModel;
  const GuestPage({super.key, required this.guestModel});

  @override
  State<GuestPage> createState() => _GuestPageState();
}

class _GuestPageState extends State<GuestPage> {
  GuestModel? model;

  void _getGuestInfo() {
    UserServiceProvider().getGuestInfo().then((res) {
      if (res?.code == 0) {
        model = GuestModel.fromJson(res?.data);
        setState(() {});
      }
    });
  }

  bool get hasMenus => model?.menus != null && model!.menus!.isNotEmpty;

  @override
  void initState() {
    super.initState();
    if (widget.guestModel == null) {
      model = GlobalPreferences().guestInfo;
    } else {
      model = widget.guestModel;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor, // Light background color
      body: Stack(
        children: [
          // SVG background at the top
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SvgPicture.asset(
              'assets/svg/mine/profile/profile_top_bg.svg',
              fit: BoxFit.contain,
              width: ScreenUtil().screenWidth,
            ),
          ),
          // Content overlay
          Positioned(
              top: 50, // Adjust this value based on the height of the SVG
              left: 20,
              right: 20,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '欢迎来到新途径',
                        style: TextStyle(
                                fontSize: 22.sp,
                                color: AppTheme.colorBlackTitle)
                            .pfSemiBold,
                      ),
                      GestureDetector(
                        onTap: () {
                          // Navigate to the PersonalSettingsPage
                          NavigatorUtils.push(
                              context, MineRouter.personalSettingsPage,
                              arguments: {
                                'avatar':
                                    '${GlobalPreferences().userInfo?.user.avatar}'
                              }).then((va) {
                            _getGuestInfo();
                          });
                        },
                        child: SvgPicture.asset(
                          'assets/svg/mine/profile/profile_setting.svg',
                          width: 24,
                          height: 24,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 17.h),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 20.w, vertical: 24.h),
                    width: 343.w,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        color: Colors.white),
                    child: Row(
                      children: [
                        GestureDetector(
                          onDoubleTap: () {
                            Clipboard.setData(ClipboardData(
                                text:
                                    jsonEncode(GlobalPreferences().userInfo)));
                            EasyLoading.showInfo('账号信息已复制到剪贴板');
                          },
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12.r),
                            child: CachedNetworkImage(
                                imageUrl: GlobalPreferences()
                                        .userLoginModel
                                        ?.avatar ??
                                    ValidatorUtils.testImageUrl,
                                width: 60,
                                height: 60),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              model?.user?.name ?? '',
                              style: AppTheme.getTextStyle(
                                      baseSize: 18,
                                      color: AppTheme.colorPrimaryBlack)
                                  .pfSemiBold,
                            ),
                            Text(
                              '特邀嘉宾',
                              style: AppTheme.getTextStyle(
                                      baseSize: 12,
                                      color: AppTheme.colorLightGrey)
                                  .pfRegular,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (hasMenus) SizedBox(height: 16.h),
                  if (hasMenus)
                    Container(
                      padding: EdgeInsets.symmetric(
                          vertical: 15.h, horizontal: 34.w),
                      width: 343.w,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15.r)),
                      child: Wrap(
                        spacing: 34.w,
                        runSpacing: 19.h,
                        children: [
                          ...model!.menus!.map((e) => functionView(e))
                        ],
                      ),
                    ),
                  SizedBox(height: 16.h),
                  SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ProfileOptionCell(
                          title: '联系客服',
                          svgAssetPath: 'assets/svg/mine/profile/feedback.svg',
                          onTap: () {
                            WeChatService().launchWeChatWork();
                          },
                        ),
                        const SizedBox(height: 12),
                        ProfileOptionCell(
                          title: '关于我们',
                          svgAssetPath: 'assets/svg/mine/profile/about_us.svg',
                          onTap: () {
                            NavigatorUtils.push(
                                context, MineRouter.aboutUsPage);
                          },
                        ),
                        SizedBox(height: ScreenUtil().bottomBarHeight),
                      ],
                    ),
                  ),
                ],
              )),
        ],
      ),
    );
  }

  Widget functionView(MenuModel menu) {
    String iconStr = menu.icon ?? '';
    String nameStr = menu.name ?? '';
    double width = (ScreenUtil().screenWidth - 40 - 68.w - 68.w) / 3;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        NavigatorPushUtils.to(context, menu.jump, menu.style, () {
          _getGuestInfo();
        });
      },
      child: SizedBox(
        width: width,
        child: Column(
          children: [
            CachedNetworkImage(imageUrl: iconStr, width: 25, height: 25),
            SizedBox(height: 4.h),
            Text(nameStr,
                style:
                    TextStyle(color: const Color(0xFF1D1C1F), fontSize: 11.sp)
                        .pfRegular),
          ],
        ),
      ),
    );
  }
}
