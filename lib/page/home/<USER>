import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/page/webview_screen_page.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/custom_dialog.dart';
import 'package:permission_handler/permission_handler.dart';

class QrcodeScanPage extends StatefulWidget {
  const QrcodeScanPage({super.key});

  @override
  State<QrcodeScanPage> createState() => _QrcodeScanPageState();
}

class _QrcodeScanPageState extends State<QrcodeScanPage>
    with WidgetsBindingObserver {
  final MobileScannerController controller = MobileScannerController(
    detectionSpeed: DetectionSpeed.noDuplicates,
    autoStart: false, // 改为false，等权限检查通过后再启动
    torchEnabled: false,
    formats: [BarcodeFormat.qrCode],
  );
  bool flashStatus = false;
  bool _hasPermission = false; // 添加权限状态标记
  bool _isCheckingPermission = true; // 添加权限检查状态标记

  double _zoomFactor = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _checkCameraPermission(); // 检查相机权限
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!controller.value.isInitialized) {
      return;
    }

    switch (state) {
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
        return;
      case AppLifecycleState.resumed:
        unawaited(controller.start());
      case AppLifecycleState.inactive:
        unawaited(controller.stop());
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // controller?.dispose();
    super.dispose();
    controller.dispose();
  }

  /// 检查相机权限
  Future<void> _checkCameraPermission() async {
    try {
      var status = await Permission.camera.status;
      if (status.isGranted) {
        // 有权限，直接启动扫描
        setState(() {
          _hasPermission = true;
          _isCheckingPermission = false;
        });
        await controller.start();
      } else {
        // 没有权限，显示权限申请弹框
        setState(() {
          _hasPermission = false;
          _isCheckingPermission = false;
        });
        _showPermissionDialog();
      }
    } catch (e) {
      // 权限检查失败，返回上一页
      setState(() {
        _isCheckingPermission = false;
      });
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  /// 显示权限申请弹框
  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // 不允许点击外部关闭
      builder: (_) => CustomDialog(
        title: "提示",
        content: "扫描二维码需要使用相机权限，请允许应用访问您的相机",
        cancelButtonText: "取消",
        confirmButtonText: "确定",
        cancelButtonColor: AppTheme.colorButtonGrey,
        confirmButtonColor: AppTheme.colorBlue,
        onCancel: () {
          Navigator.of(context).pop(); // 关闭弹框
          Navigator.of(context).pop(); // 返回上一页
        },
        onConfirm: () async {
          Navigator.of(context).pop(); // 关闭弹框
          await _requestCameraPermission(); // 请求权限
        },
      ),
    );
  }

  /// 请求相机权限
  Future<void> _requestCameraPermission() async {
    try {
      var status = await Permission.camera.request();
      if (status.isGranted) {
        // 权限获取成功，启动扫描
        setState(() {
          _hasPermission = true;
        });
        await controller.start();
      } else if (status.isPermanentlyDenied) {
        // 权限被永久拒绝，引导用户去设置
        _showSettingsDialog();
      } else {
        // 权限被拒绝，返回上一页
        if (mounted) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      // 权限请求失败，返回上一页
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  /// 显示设置弹框
  void _showSettingsDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CustomDialog(
        title: "权限被拒绝",
        content: "相机权限已被拒绝，请前往设置中手动开启相机权限",
        cancelButtonText: "取消",
        confirmButtonText: "去设置",
        cancelButtonColor: AppTheme.colorButtonGrey,
        confirmButtonColor: AppTheme.colorBlue,
        onCancel: () {
          Navigator.of(context).pop(); // 关闭弹框
          Navigator.of(context).pop(); // 返回上一页
        },
        onConfirm: () {
          Navigator.of(context).pop(); // 关闭弹框
          openAppSettings(); // 打开应用设置
          Navigator.of(context).pop(); // 返回上一页
        },
      ),
    );
  }

  Widget _buildZoomScaleSlider() {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, state, child) {
        if (!state.isInitialized || !state.isRunning) {
          return const SizedBox.shrink();
        }

        final TextStyle labelStyle = Theme.of(context)
            .textTheme
            .headlineMedium!
            .copyWith(color: Colors.white);

        return Column(
          children: [
            Text(
              '+',
              overflow: TextOverflow.fade,
              style: labelStyle,
            ),
            RotatedBox(
                quarterTurns: 3,
                child: SliderTheme(
                  data: SliderThemeData(
                    thumbShape: RoundSliderThumbShape(enabledThumbRadius: 5),
                    overlayShape: RoundSliderOverlayShape(overlayRadius: 10),
                    trackHeight: 3.w,
                  ),
                  child: Slider(
                    value: _zoomFactor,
                    thumbColor: Colors.white,
                    activeColor: Colors.white,
                    onChanged: (value) {
                      setState(() {
                        _zoomFactor = value;
                        controller.setZoomScale(value);
                      });
                    },
                  ),
                )),
            Text(
              '-',
              overflow: TextOverflow.fade,
              style: labelStyle,
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // 如果正在检查权限，显示加载界面
    if (_isCheckingPermission) {
      return Material(
        child: Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(
              color: Colors.white,
            ),
          ),
        ),
      );
    }

    // 如果没有权限，显示黑屏（权限弹框会自动显示）
    if (!_hasPermission) {
      return Material(
        child: Container(
          color: Colors.black,
          child: const Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: TeamManagerNav(
                  title: '扫一扫',
                  titleColor: Colors.white,
                  iconColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Material(
        child: /* GestureDetector(
        onScaleStart: (details) {
          baseZoomScale = zoomScale;
        },
        onScaleUpdate: (details) {
          _handleZoom(details.scale);
        },
        child: )*/

            GestureDetector(
      onDoubleTap: () {
        if (_zoomFactor < 0.35) {
          Timer.periodic(const Duration(microseconds: 300), (Timer timer) {
            setState(() {
              _zoomFactor += 0.01;
              var value = _zoomFactor;
              if (value >= 0.35) {
                timer.cancel();
              }
              controller.setZoomScale(value);
            });
          });
        } else {
          Timer.periodic(const Duration(microseconds: 300), (Timer timer) {
            setState(() {
              _zoomFactor -= 0.01;
              var value = _zoomFactor;
              if (value < 0.03) {
                value = 0;
                timer.cancel();
              }
              controller.setZoomScale(value);
            });
          });
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildQrView(context),
          Positioned.fill(
            child: Center(
              child: ClipPath(
                clipper: TransparentAreaClipper(),
                child: Container(
                  color: Colors.black.withOpacity(0.7),
                ),
              ),
            ),
          ),
          const Positioned(
            top: 0,
            child: TeamManagerNav(
              title: '扫一扫',
              titleColor: Colors.white,
              iconColor: Colors.white,
            ),
          ),
          Positioned(
              bottom: 0,
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: 180.w,
                    decoration: BoxDecoration(
                        color: '#000000'.toColor().withOpacity(0.5),
                        borderRadius: BorderRadius.circular(16.r)),
                    padding: EdgeInsets.symmetric(vertical: 6.h),
                    child: Text('扫描二维码',
                        style: TextStyle(color: Colors.white, fontSize: 13.sp)
                            .pfSemiBold),
                  ),
                  SizedBox(height: 50.h),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () async {
                      await controller.toggleTorch();

                      setState(() {
                        flashStatus = !flashStatus;
                      });
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.fromLTRB(10, 10, 10, 5),
                      child: Image.asset(
                        flashStatus
                            ? 'assets/png/home/<USER>'
                            : 'assets/png/home/<USER>',
                        width: 30,
                        height: 30,
                      ),
                    ),
                  ),
                  Text(
                    flashStatus ? '轻触关闭' : '轻触照亮',
                    style: TextStyle(color: Colors.white, fontSize: 11.sp)
                        .pfRegular,
                  ),
                  SizedBox(height: 50.h),
                ],
              )),
          Image.asset('assets/png/home/<USER>',
              width: 300, height: 300),
          Positioned(
              right: Platform.isIOS ? 70.w : 50.w,
              child: _buildZoomScaleSlider())
        ],
      ),
    ));
  }

  Widget _buildQrView(BuildContext context) {
    var scanArea = (MediaQuery.of(context).size.width < 400 ||
            MediaQuery.of(context).size.height < 400)
        ? 290.0
        : 290.0;
    return Stack(
      alignment: Alignment.center,
      children: [
        MobileScanner(
          controller: controller,
          onDetect: _handleBarcode,
          fit: BoxFit.cover,
          placeholderBuilder: (p0, p1) {
            return Container(
              width: ScreenUtil().screenWidth,
              height: ScreenUtil().screenHeight,
              color: Colors.black,
            );
          },
        ),
        Lottie.asset('assets/lottie/qrcode_scan.json',
            width: scanArea, height: scanArea),
      ],
    );
  }

  void _handleBarcode(BarcodeCapture barcodes) {
    Barcode? result;
    result = barcodes.barcodes.firstOrNull;
    if (result != null) {
      String? codeStr = result.rawValue;
      if (codeStr != null) {
        if (codeStr.contains('https://m.xtjzx.cn')) {
          String type = codeStr.split("@").first;
          String title = codeStr.split("@")[1];
          String path = codeStr.split("@").last;
          if (type == 'h5') {
            Navigator.of(context)
                .pushReplacement(MaterialPageRoute(builder: (_) {
              return WebviewScreenPage(
                url: path,
                title: title,
                keepAlive: false,
                enableLongPress: false,
              );
            }));
          }
        } else if (codeStr.substring(0, 8) == 'https://') {
          Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) {
            return WebviewScreenPage(
              url: codeStr,
              title: '',
              keepAlive: false,
              enableLongPress: false,
            );
          }));
        } else {
          ToastUtils.show('二维码无效');
        }
      } else {
        ToastUtils.show('二维码无效');
      }
    } else {
      ToastUtils.show('二维码无效');
    }
  }
}

class TransparentAreaClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));
    path.addRect(
      Rect.fromCenter(
        center: Offset(size.width / 2, size.height / 2),
        width: 290,
        height: 290,
      ),
    );
    path.fillType = PathFillType.evenOdd;
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
