import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/page/webview_screen_page.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';

class QrcodeScanPage extends StatefulWidget {
  const QrcodeScanPage({super.key});

  @override
  State<QrcodeScanPage> createState() => _QrcodeScanPageState();
}

class _QrcodeScanPageState extends State<QrcodeScanPage>
    with WidgetsBindingObserver {
  final MobileScannerController controller = MobileScannerController(
    detectionSpeed: DetectionSpeed.noDuplicates,
    autoStart: true,
    torchEnabled: false,
    formats: [BarcodeFormat.qrCode],
  );
  bool flashStatus = false;

  double _zoomFactor = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!controller.value.isInitialized) {
      return;
    }

    switch (state) {
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
        return;
      case AppLifecycleState.resumed:
        unawaited(controller.start());
      case AppLifecycleState.inactive:
        unawaited(controller.stop());
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // controller?.dispose();
    super.dispose();
    controller.dispose();
  }

  Widget _buildZoomScaleSlider() {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, state, child) {
        if (!state.isInitialized || !state.isRunning) {
          return const SizedBox.shrink();
        }

        final TextStyle labelStyle = Theme.of(context)
            .textTheme
            .headlineMedium!
            .copyWith(color: Colors.white);

        return Column(
          children: [
            Text(
              '+',
              overflow: TextOverflow.fade,
              style: labelStyle,
            ),
            RotatedBox(
                quarterTurns: 3,
                child: SliderTheme(
                  data: SliderThemeData(
                    thumbShape: RoundSliderThumbShape(enabledThumbRadius: 5),
                    overlayShape: RoundSliderOverlayShape(overlayRadius: 10),
                    trackHeight: 3.w,
                  ),
                  child: Slider(
                    value: _zoomFactor,
                    thumbColor: Colors.white,
                    activeColor: Colors.white,
                    onChanged: (value) {
                      setState(() {
                        _zoomFactor = value;
                        controller.setZoomScale(value);
                      });
                    },
                  ),
                )),
            Text(
              '-',
              overflow: TextOverflow.fade,
              style: labelStyle,
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: /* GestureDetector(
        onScaleStart: (details) {
          baseZoomScale = zoomScale;
        },
        onScaleUpdate: (details) {
          _handleZoom(details.scale);
        },
        child: )*/

            GestureDetector(
      onDoubleTap: () {
        if (_zoomFactor < 0.35) {
          Timer.periodic(const Duration(microseconds: 300), (Timer timer) {
            setState(() {
              _zoomFactor += 0.01;
              var value = _zoomFactor;
              if (value >= 0.35) {
                timer.cancel();
              }
              controller.setZoomScale(value);
            });
          });
        } else {
          Timer.periodic(const Duration(microseconds: 300), (Timer timer) {
            setState(() {
              _zoomFactor -= 0.01;
              var value = _zoomFactor;
              if (value < 0.03) {
                value = 0;
                timer.cancel();
              }
              controller.setZoomScale(value);
            });
          });
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          _buildQrView(context),
          Positioned.fill(
            child: Center(
              child: ClipPath(
                clipper: TransparentAreaClipper(),
                child: Container(
                  color: Colors.black.withOpacity(0.7),
                ),
              ),
            ),
          ),
          const Positioned(
            top: 0,
            child: TeamManagerNav(
              title: '扫一扫',
              titleColor: Colors.white,
              iconColor: Colors.white,
            ),
          ),
          Positioned(
              bottom: 0,
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: 180.w,
                    decoration: BoxDecoration(
                        color: '#000000'.toColor().withOpacity(0.5),
                        borderRadius: BorderRadius.circular(16.r)),
                    padding: EdgeInsets.symmetric(vertical: 6.h),
                    child: Text('扫描二维码',
                        style: TextStyle(color: Colors.white, fontSize: 13.sp)
                            .pfSemiBold),
                  ),
                  SizedBox(height: 50.h),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () async {
                      await controller.toggleTorch();

                      setState(() {
                        flashStatus = !flashStatus;
                      });
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.fromLTRB(10, 10, 10, 5),
                      child: Image.asset(
                        flashStatus
                            ? 'assets/png/home/<USER>'
                            : 'assets/png/home/<USER>',
                        width: 30,
                        height: 30,
                      ),
                    ),
                  ),
                  Text(
                    flashStatus ? '轻触关闭' : '轻触照亮',
                    style: TextStyle(color: Colors.white, fontSize: 11.sp)
                        .pfRegular,
                  ),
                  SizedBox(height: 50.h),
                ],
              )),
          Image.asset('assets/png/home/<USER>',
              width: 300, height: 300),
          Positioned(
              right: Platform.isIOS ? 70.w : 50.w,
              child: _buildZoomScaleSlider())
        ],
      ),
    ));
  }

  Widget _buildQrView(BuildContext context) {
    var scanArea = (MediaQuery.of(context).size.width < 400 ||
            MediaQuery.of(context).size.height < 400)
        ? 290.0
        : 290.0;
    return Stack(
      alignment: Alignment.center,
      children: [
        MobileScanner(
          controller: controller,
          onDetect: _handleBarcode,
          fit: BoxFit.cover,
          placeholderBuilder: (p0, p1) {
            return Container(
              width: ScreenUtil().screenWidth,
              height: ScreenUtil().screenHeight,
              color: Colors.black,
            );
          },
        ),
        Lottie.asset('assets/lottie/qrcode_scan.json',
            width: scanArea, height: scanArea),
      ],
    );
  }

  void _handleBarcode(BarcodeCapture barcodes) {
    Barcode? result;
    result = barcodes.barcodes.firstOrNull;
    if (result != null) {
      String? codeStr = result.rawValue;
      if (codeStr != null) {
        if (codeStr.contains('https://m.xtjzx.cn')) {
          String type = codeStr.split("@").first;
          String title = codeStr.split("@")[1];
          String path = codeStr.split("@").last;
          if (type == 'h5') {
            Navigator.of(context)
                .pushReplacement(MaterialPageRoute(builder: (_) {
              return WebviewScreenPage(
                url: path,
                title: title,
                keepAlive: false,
                enableLongPress: false,
              );
            }));
          }
        } else if (codeStr.substring(0, 8) == 'https://') {
          Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) {
            return WebviewScreenPage(
              url: codeStr,
              title: '',
              keepAlive: false,
              enableLongPress: false,
            );
          }));
        } else {
          ToastUtils.show('二维码无效');
        }
      } else {
        ToastUtils.show('二维码无效');
      }
    } else {
      ToastUtils.show('二维码无效');
    }
  }
}

class TransparentAreaClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    path.addRect(Rect.fromLTWH(0, 0, size.width, size.height));
    path.addRect(
      Rect.fromCenter(
        center: Offset(size.width / 2, size.height / 2),
        width: 290,
        height: 290,
      ),
    );
    path.fillType = PathFillType.evenOdd;
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
