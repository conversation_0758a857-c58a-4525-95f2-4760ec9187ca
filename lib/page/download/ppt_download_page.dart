import 'dart:isolate';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/manager/preferences_manager.dart';
import 'package:npemployee/model/download/task_info.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/study/annual_meeting_ppt_item.dart';
import 'package:share_plus/share_plus.dart';

import '../../widget/study/my_download_annual_meeting_ppt_item.dart';

class PptDownloadPage extends StatefulWidget {
  final bool isEdit;
  final bool isAllSelected;
  final Function(List<TaskInfo>) onSelectedChange;
  const PptDownloadPage(
      {super.key,
      required this.isEdit,
      required this.isAllSelected,
      required this.onSelectedChange});

  @override
  State<PptDownloadPage> createState() => PptDownloadPageState();
}

class PptDownloadPageState extends State<PptDownloadPage> {
  Map<String, List<TaskInfo>> groupedTasks = {};

  String selectedTag = '';
  final ReceivePort _port = ReceivePort();
  void refreshUI() {
    _getDownloadList();
  }

  void _getDownloadList() async {
    List<TaskInfo> tasks =
        await PreferencesManager().getMeetingPptDownloads() ?? [];

    final allDownloadTasks = await FlutterDownloader.loadTasks();
    if (allDownloadTasks != null) {
      for (final task in allDownloadTasks) {
        for (final info in tasks) {
          String taskUrl = Uri.decodeFull(task.url);
          if (info.link == taskUrl) {
            info
              ..taskId = task.taskId
              ..status = task.status
              ..progress = task.progress;
          }
        }
      }
    }

    groupedTasks = {};
    for (var task in tasks) {
      if (groupedTasks.containsKey(task.pptTag)) {
        groupedTasks[task.pptTag]!.add(task);
      } else {
        groupedTasks[task.pptTag!] = [task];
      }
    }
    if (selectedTag.isEmpty||groupedTasks.keys.contains(selectedTag)==false) {
      selectedTag = groupedTasks.keys.first;
    }
    List<TaskInfo> finalTasks = [];
    for (var item in groupedTasks[selectedTag] ?? []) {
      finalTasks.add(item);
    }
    widget.onSelectedChange(finalTasks);
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _bindBackgroundIsolate();
    _getDownloadList();
  }

  void _bindBackgroundIsolate() {
    final isSuccess = IsolateNameServer.registerPortWithName(
      _port.sendPort,
      'downloader_ppt_send_port',
    );
    if (!isSuccess) {
      _unbindBackgroundIsolate();
      _bindBackgroundIsolate();
      return;
    }
    _port.listen((dynamic data) {
      final taskId = (data as List<dynamic>)[0] as String;
      final status = DownloadTaskStatus.fromInt(data[1] as int);
      final progress = data[2] as int;

      debugPrint(
        'Callback on UI isolate: '
        'task ($taskId) is in status ($status) and process ($progress)',
      );

      if (groupedTasks.isNotEmpty) {
        groupedTasks.forEach((key, tasks) {
          final task = tasks.firstWhere((task) => task.taskId == taskId);
          setState(() {
            task
              ..status = status
              ..progress = progress;
          });
        });
      }
    });
  }

  void _unbindBackgroundIsolate() {
    IsolateNameServer.removePortNameMapping('downloader_ppt_send_port');
  }

  @pragma('vm:entry-point')
  static void downloadCallback(
    String id,
    int status,
    int progress,
  ) {
    debugPrint(
      'Callback on background isolate: '
      'task ($id) is in status ($status) and process ($progress)',
    );

    IsolateNameServer.lookupPortByName('downloader_ppt_send_port')
        ?.send([id, status, progress]);
  }

  @override
  void dispose() {
    _unbindBackgroundIsolate();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            width: ScreenUtil().screenWidth,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: groupedTasks.keys
                    .map((tag) => GestureDetector(
                          onTap: () {
                            selectedTag = tag;
                            _getDownloadList();
                          },
                          child: _tagItem(tag),
                        ))
                    .toList(),
              ),
            ),
          ),
          Expanded(
            child: groupedTasks.isEmpty
                ? const NoDataPage()
                : ListView.builder(
                    itemCount: (groupedTasks[selectedTag] ?? []).length,
                    itemBuilder: _itemBuilder,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _tagItem(String tag) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        margin: EdgeInsets.only(
            left: groupedTasks.keys.toList().indexOf(tag) == 0 ? 0 : 16.w),
        decoration: BoxDecoration(
            color: selectedTag == tag
                ? const Color(0xFFE8F0FF)
                : const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(12.r)),
        child: Text(tag,
            style: TextStyle(
                color: selectedTag == tag
                    ? const Color(0xFF3366FF)
                    : const Color(0xFF999999),
                fontSize: 13.sp)));
  }

  Widget _itemBuilder(BuildContext context, int index) {
    TaskInfo task = groupedTasks[selectedTag]![index];
    return GestureDetector(
      onTap: () {
        if (widget.isEdit) {
          setState(() {
            task.isSelected = !task.isSelected;
            List<TaskInfo> infos = groupedTasks.values
                .expand((e) => e)
                .where((e) => e.isSelected)
                .toList();
            widget.onSelectedChange(infos);
          });
        }
      },
      child: Container(
        // margin: EdgeInsets.symmetric(horizontal: 15.w),
        padding: EdgeInsets.symmetric(vertical: 17.h),
        // decoration: const BoxDecoration(
        //     border: Border(
        //         bottom: BorderSide(color: Color(0xFFEEEEEE), width: 0.5))),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (widget.isEdit)
              Padding(
                padding: EdgeInsets.only(right: 10.w),
                child: Container(
                  alignment: Alignment.center,
                  width: 32,
                  height: 32,
                  child: Image.asset(
                      task.isSelected
                          ? 'assets/png/download/select.png'
                          : 'assets/png/download/unselect.png',
                      width: 16,
                      height: 16),
                ),
              ),
            // Image.asset('assets/png/xiaoxin/ic_pdf.png', width: 27, height: 27),
            // SizedBox(width: 10.w),

            Expanded(child: MyDownloadAnnualMeetingPPTItem(task: task))

            // Expanded(
            //   child: Text(task.name!,
            //       style:
            //       TextStyle(color: const Color(0xFF333333), fontSize: 15.sp)),
            // ),
            // if (!widget.isEdit) ...[
            //
            //   GestureDetector(
            //     behavior: HitTestBehavior.opaque,
            //     onTap: () async {
            //       NavigatorUtils.push(context, CommonRouter.pdfPreviewPage,
            //           arguments: {
            //             'filePath':
            //             '${await AppInfo().pptDownloadDir}/${task.name}.pdf',
            //             'title': task.name!,
            //           });
            //     },
            //     child: Container(
            //       padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 7.h),
            //       decoration: BoxDecoration(
            //           color: const Color(0xFFEFF4FF),
            //           borderRadius: BorderRadius.circular(10.r)),
            //       child: Text('预览',
            //           style: TextStyle(
            //               color: const Color(0xFF0054FF), fontSize: 14.sp)),
            //     ),
            //   ),
            //   SizedBox(width: 15.w),
            //   GestureDetector(
            //     onTap: () async {
            //       XFile xFile =
            //       XFile('${await AppInfo().pptDownloadDir}/${task.name}.pdf');
            //       Share.shareXFiles([xFile]);
            //     },
            //     child: Container(
            //         padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 7.h),
            //         decoration: BoxDecoration(
            //           color: const Color(0xFFFFF4ED),
            //           borderRadius: BorderRadius.circular(10.r),
            //         ),
            //         child: const Icon(Icons.share,
            //             size: 19.5, color: Color(0xFFFF7000))),
            //   ),
            // ],
          ],
        ),
      ),
    );
  }
}
