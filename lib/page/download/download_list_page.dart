import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/dialog/input_dialog.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';

class DownloadListPage extends StatefulWidget {
  final List<ChapterModel> chapters;
  final CourseListModel course;
  final int type; // 1-音频 2-视频 3-文本
  const DownloadListPage(
      {super.key,
      required this.chapters,
      required this.course,
      required this.type});

  @override
  State<DownloadListPage> createState() => _DownloadListPageState();
}

class _DownloadListPageState extends State<DownloadListPage> {
  List<ChapterModel> chapters = [];
  List<ChapterModel> checkedChapters = [];
  List<DownloadTask> _tasks = [];

  bool isAllSelected = false;

  int checked = 0; //选择节数
  double checkSize = 0; //选择文件大小

  List<String> _dropList = ['高清', '标清'];
  String _dropValue = '高清';

  // 章节选择处理
  void toggleChapterSelection(int chapterIndex) {
    setState(() {
      chapters[chapterIndex].isSelected = !chapters[chapterIndex].isSelected;
      chapters[chapterIndex].lesson.forEach((lesson) {
        // 检查是否已缓存
        bool isDownloaded = false;
        if (widget.type == 1) {
          isDownloaded = mp3IsComplete(lesson.media);
        } else if (widget.type == 2) {
          isDownloaded = mp4IsComplete(lesson.media);
        }
        if (!isDownloaded) {
          lesson.isSelected = chapters[chapterIndex].isSelected;
        }
      });
      checkedResult();
      updateAllSelectedStatus();
    });
  }

  // 子内容选择处理
  void toggleContentSelection(int chapterIndex, int contentIndex) {
    setState(() {
      final content = chapters[chapterIndex].lesson[contentIndex];
      content.isSelected = !content.isSelected;

      // 如果所有未下载的内容都被选中，则章节也选中；反之亦然
      chapters[chapterIndex].isSelected =
          chapters[chapterIndex].lesson.every((lesson) {
        bool isDownloaded = false;
        if (widget.type == 1) {
          isDownloaded = mp3IsComplete(lesson.media);
        } else if (widget.type == 2) {
          isDownloaded = mp4IsComplete(lesson.media);
        }
        // bool isDownloaded = lesson.media.any((media) => _tasks.any((task) =>
        //     task.url == media.media_url &&
        //     task.status == DownloadTaskStatus.complete));
        return isDownloaded || lesson.isSelected;
      });
      checkedResult();
      updateAllSelectedStatus();
    });
  }

  // 全选/取消全选
  void toggleAllSelection() {
    setState(() {
      isAllSelected = !isAllSelected;
      chapters.forEach((chapter) {
        chapter.isSelected = isAllSelected;
        chapter.lesson.forEach((lesson) {
          // 检查是否已缓存
          bool isDownloaded = false;
          if (widget.type == 1) {
            isDownloaded = mp3IsComplete(lesson.media);
          } else if (widget.type == 2) {
            isDownloaded = mp4IsComplete(lesson.media);
          }
          if (!isDownloaded) {
            lesson.isSelected = isAllSelected;
          }
        });
      });
      checkedResult();
    });
  }

  // 更新底部全选按钮状态
  void updateAllSelectedStatus() {
    setState(() {
      isAllSelected = chapters.every((chapter) {
        return chapter.lesson.every((lesson) {
          bool isDownloaded = false;
          if (widget.type == 1) {
            isDownloaded = mp3IsComplete(lesson.media);
          } else if (widget.type == 2) {
            isDownloaded = mp4IsComplete(lesson.media);
          }
          return isDownloaded || lesson.isSelected;
        });
      });
    });
  }

  // 切换章节展开/收缩状态
  void toggleChapterExpansion(int chapterIndex) {
    setState(() {
      chapters[chapterIndex].isExpanded = !chapters[chapterIndex].isExpanded;
    });
  }

  //选择结果
  void checkedResult() {
    checked = 0;
    checkSize = 0;
    chapters.forEach((ChapterModel c) {
      c.lesson.forEach((LessonModel d) {
        bool isDownloaded = false;
        if (widget.type == 1) {
          isDownloaded = mp3IsComplete(d.media);
        } else if (widget.type == 2) {
          isDownloaded = mp4IsComplete(d.media);
        }
        if (d.isSelected && !isDownloaded) {
          checked++;
          if (_dropValue == '高清') {
            checkSize += d.vf5Size + d.getMp3Size;
          } else {
            checkSize += d.vf6Size + d.getMp3Size;
          }
        }
      });
    });
  }

  //下载数据
  void addSelectedToDownloaded() async {
    checkedChapters.clear();
    // 临时存储本次选中的章节
    List<ChapterModel> newChapters = [];

    for (var chapter in chapters) {
      // 筛选出选中的章节和内容
      List<LessonModel> selectedItems = chapter.lesson
          .where((item) {
            bool isDownloaded = false;
            if (widget.type == 1) {
              isDownloaded = mp3IsComplete(item.media);
            } else if (widget.type == 2) {
              isDownloaded = mp4IsComplete(item.media);
            }
            // bool isDownloaded = item.media.any((media) => _tasks.any((task) =>
            //     task.url == media.media_url &&
            //     task.status == DownloadTaskStatus.complete));
            return item.isSelected && !isDownloaded;
          })
          .map((item) => LessonModel(
                item.id,
                item.chapter_id,
                item.course_id,
                item.phrase,
                item.sort_order,
                item.title,
                item.total_time,
                item.media, /* item.url */
              ))
          .toList();

      if (selectedItems.isNotEmpty) {
        ChapterModel newChapter = ChapterModel(chapter.course_id, chapter.id,
            chapter.phrase, chapter.sort_order, chapter.title, selectedItems);

        newChapters.add(newChapter);
      }
    }

    // 合并去重
    for (var newChapter in newChapters) {
      // 检查 A 数组中是否已经存在此章节
      int existingChapterIndex = checkedChapters.indexWhere(
        (chapter) => chapter.title == newChapter.title,
      );

      if (existingChapterIndex != -1) {
        // 如果章节已存在，合并内容
        for (var newItem in newChapter.lesson) {
          bool isDuplicate = checkedChapters[existingChapterIndex].lesson.any(
              (item) =>
                  item.id ==
                  newItem
                      .id); //item.title == newItem.title && item.url == newItem.url

          if (!isDuplicate) {
            checkedChapters[existingChapterIndex].lesson.add(newItem);
          }
        }
      } else {
        // 如果章节不存在，直接添加
        checkedChapters.add(newChapter);
      }
    }

    // 根据清晰度过滤视频
    for (var chapter in checkedChapters) {
      for (var lesson in chapter.lesson) {
        // 深拷贝原始media列表
        List<MediaModel> originalMedia = lesson.media
            .map((media) => MediaModel(
                id: media.id,
                media_type: media.media_type,
                media_url: media.media_url,
                size: media.size))
            .toList();

        // 过滤MP4类型的媒体
        List<MediaModel> filteredMedia = originalMedia.where((media) {
          if (media.media_type != 'mp4') return true; // 保留非MP4类型

          if (_dropValue == '高清') {
            return media.media_url.contains('v.f5'); // 高清只保留f5
          } else {
            return media.media_url.contains('v.f6'); // 标清只保留f6
          }
        }).toList();

        lesson.media = filteredMedia; // 直接赋值新列表,避免clear()
      }
    }

    // Debug 打印下载列表
    print('Downloaded Chapters: ${checkedChapters.length}');
    for (var chapter in checkedChapters) {
      print('Chapter: ${chapter.title}, Items: ${chapter.lesson.length}');
    }
  }

  void _getDownloadTasks() async {
    final tasks = await FlutterDownloader.loadTasks();
    _tasks = tasks ?? [];

    // 更新选中状态
    for (var chapter in chapters) {
      for (var lesson in chapter.lesson) {
        bool isDownloaded = false;
        if (widget.type == 1) {
          isDownloaded = mp3IsComplete(lesson.media);
        } else if (widget.type == 2) {
          isDownloaded = mp4IsComplete(lesson.media);
        }
        // bool isDownloaded = _tasks.any((task) =>
        //     task.url == lesson.media.first.media_url &&
        //     task.status == DownloadTaskStatus.complete);
        if (isDownloaded) {
          lesson.isSelected = false;
          chapter.isSelected = false;
        }
      }
    }

    checkedResult();
    updateAllSelectedStatus();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    chapters = widget.chapters;
    _getDownloadTasks();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        appBar: const CommonNav(title: '下载'),
        body: Column(
          children: [
            _topView(),
            SizedBox(height: 16.h),
            _listView(),
          ],
        ),
        bottomNavigationBar: _bottomView());
  }

  Widget _bottomView() {
    return Container(
      color: Colors.white,
      height: 64.h,
      child: Row(
        children: [
          TextButton(
              onPressed: () => toggleAllSelection(),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 4.h),
                    alignment: Alignment.center,
                    width: 32,
                    height: 32,
                    child: Image.asset(
                        isAllSelected
                            ? 'assets/png/download/select.png'
                            : 'assets/png/download/unselect.png',
                        width: 16,
                        height: 16),
                  ),
                  Text('全选',
                      style: TextStyle(
                          color: const Color(0xFF333333), fontSize: 14.sp))
                ],
              )),
          if (checked != 0) SizedBox(width: 8.w),
          if (checked != 0)
            Text(
              '已选$checked节 ${checkSize.toStringAsFixed(1)}M',
              style: TextStyle(color: const Color(0xFF999999), fontSize: 14.sp),
            ),
          const Expanded(child: SizedBox()),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              addSelectedToDownloaded();
              if (checkedChapters.isEmpty) {
                ToastUtils.show('请选择下载项');
                return;
              }
              NavigatorUtils.push(context, StudyRouter.downloadDetail,
                  arguments: {
                    'course': widget.course,
                    'checkedChapters': checkedChapters,
                  }).then((value) {
                _getDownloadTasks();
              });
            },
            child: Container(
              margin: EdgeInsets.only(right: 16.w),
              padding: EdgeInsets.symmetric(horizontal: 54.w, vertical: 14.h),
              decoration: BoxDecoration(
                  color: const Color(0xFF0054FF),
                  borderRadius: BorderRadius.circular(16.r)),
              child: Text('下载',
                  style: TextStyle(
                      color: const Color(0xFFFFFFFF), fontSize: 16.sp)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _listView() {
    return Expanded(
        child: ListView.builder(
            itemCount: chapters.length,
            itemBuilder: (c, index) {
              // return _buildChapterTile(chapters[index]);
              return _itemBuilder(c, index);
            }));
  }

  Widget _itemBuilder(c, index) {
    final chapter = chapters[index];
    String chapterTime = ValidatorUtils.formatDuration(
        chapter.lesson.fold(0, (total, time) => total + time.total_time));
    double size = 0;
    for (var lesson in chapter.lesson) {
      if (_dropValue == '高清') {
        size += lesson.vf5Size + lesson.getMp3Size;
      } else {
        size += lesson.vf6Size + lesson.getMp3Size;
      }
    }

    return Container(
      margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(16.r)),
      child: Column(
        children: [
          // Chapter Header with Expansion Control
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 6.w),
            child: Row(
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => toggleChapterExpansion(index),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () => toggleChapterSelection(index),
                        behavior: HitTestBehavior.opaque,
                        child: Container(
                          margin: EdgeInsets.only(top: 4.h),
                          alignment: Alignment.topCenter,
                          width: 32,
                          height: 32,
                          child: Image.asset(
                              chapterIsComplete(chapter)
                                  ? 'assets/png/download/disable_select.png'
                                  : chapter.isSelected
                                      ? 'assets/png/download/select.png'
                                      : 'assets/png/download/unselect.png',
                              width: 16,
                              height: 16),
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                              width: 260.w,
                              child: Text(chapter.title,
                                  style: TextStyle(
                                    color: const Color(0xFF333333),
                                    fontSize: 16.sp,
                                  ).pfMedium)),
                          SizedBox(height: 2.h),
                          Row(children: [
                            _subWidget(chapterTime, imgPath: 'time.png'),
                            SizedBox(width: 15.w),
                            _subWidget('${size.toStringAsFixed(1)}MB',
                                imgPath: 'memory.png'),
                          ])
                        ],
                      ),
                    ],
                  ),
                ),
                const Expanded(child: SizedBox()),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => toggleChapterExpansion(index),
                  child: Container(
                    alignment: Alignment.topCenter,
                    width: 32,
                    height: 32,
                    child: Image.asset(
                        chapter.isExpanded
                            ? 'assets/png/download/drop_down.png'
                            : 'assets/png/download/drop_open.png',
                        width: 16,
                        height: 16),
                  ),
                ),
              ],
            ),
          ),
          // Contents of the Chapter
          if (chapter.isExpanded)
            ...chapter.lesson.map((content) {
              int contentIndex = chapter.lesson.indexOf(content);
              bool isDownloadComplete = false;

              // 检查下载状态
              bool hasCompleteMp3 = false;
              bool hasCompleteVideo = false;

              if (widget.type == 1) {
                hasCompleteMp3 = mp3IsComplete(content.media);
                isDownloadComplete = hasCompleteMp3;
              } else if (widget.type == 2) {
                hasCompleteVideo = mp4IsComplete(content.media);
                isDownloadComplete = hasCompleteVideo;
              } else {}

              if (isDownloadComplete) {
                chapter.lesson[contentIndex].isSelected = false;
              }

              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  toggleContentSelection(index, contentIndex);
                },
                child: Padding(
                  padding: EdgeInsets.only(
                      left: 30.w, top: 8.h, bottom: 8.h, right: 16.w),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          toggleContentSelection(index, contentIndex);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: Container(
                          alignment: Alignment.topCenter,
                          width: 32,
                          height: 32,
                          child: Image.asset(
                              isDownloadComplete
                                  ? 'assets/png/download/disable_select.png'
                                  : (chapter.lesson[contentIndex].isSelected
                                      ? 'assets/png/download/select.png'
                                      : 'assets/png/download/unselect.png'),
                              width: 16,
                              height: 16),
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                              width: 260.w,
                              child: Text(content.title,
                                  style: TextStyle(
                                          color: const Color(0xFF222222),
                                          fontSize: 14.sp)
                                      .pfRegular)),
                          SizedBox(height: 2.h),
                          Row(children: [
                            _subWidget(
                                ValidatorUtils.formatDuration(
                                    content.total_time),
                                imgPath: 'time.png'),
                            SizedBox(width: 15.w),
                            _subWidget(
                                '${((_dropValue == '高清' ? content.vf5Size : content.vf6Size) + content.getMp3Size).toStringAsFixed(1)}MB',
                                imgPath: 'memory.png'),
                            SizedBox(width: 15.w),
                            if (isDownloadComplete) _subWidget('已缓存'),
                          ])
                        ],
                      )
                    ],
                  ),
                ),
              );
            }).toList(),
        ],
      ),
    );
  }

  Widget _subWidget(String title, {String? imgPath}) {
    return Row(
      children: [
        if (imgPath != null)
          Image.asset('assets/png/download/$imgPath', width: 10, height: 10),
        SizedBox(width: 3.w),
        Text(title,
            style: TextStyle(color: const Color(0xFF808080), fontSize: 12.sp)),
      ],
    );
  }

  Widget _topView() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          if (widget.type == 2)
            Text(
              '当前清晰度: ',
              style: TextStyle(color: const Color(0xFF333333), fontSize: 13.sp),
            ),
          if (widget.type == 2)
            DropdownButton(
                isDense: true,
                borderRadius: BorderRadius.circular(16.r),
                dropdownColor: const Color.fromRGBO(0, 0, 0, 0.6),
                iconSize: 18,
                iconEnabledColor: const Color(0xFF0054FF),
                underline: const SizedBox.shrink(),
                value: _dropValue,
                selectedItemBuilder: (context) {
                  return _dropList.map((e) {
                    return Text(e,
                        style: TextStyle(
                            color: const Color(0xFF0054FF), fontSize: 14.sp));
                  }).toList();
                },
                items: _dropList.map((e) {
                  return DropdownMenuItem(
                    value: e,
                    child: Text(
                      e,
                      style: TextStyle(
                          color: e == _dropValue
                              ? const Color(0xFF85ADFF)
                              : Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500),
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _dropValue = value as String;
                  });
                }),
          const Expanded(child: SizedBox()),
          SizedBox(
            height: 40.h,
            child: TextButton(
                onPressed: () =>
                    NavigatorUtils.push(context, StudyRouter.myDownload)
                        .then((value) {
                      _getDownloadTasks();
                    }),
                style: const ButtonStyle(
                    padding: WidgetStatePropertyAll(EdgeInsets.zero)),
                child: Text('我的下载',
                    style: TextStyle(
                        color: const Color(0xFF0054FF),
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w500))),
          )
        ],
      ),
    );
  }

  bool mp4IsComplete(List<MediaModel> medias) {
    for (var media in medias) {
      if (media.media_type == 'mp4') {
        List targetQualitys = ['v.f5', 'v.f6'];
        for (var targetQuality in targetQualitys) {
          if (media.media_url.contains(targetQuality)) {
            for (var task in _tasks) {
              if (task.url == media.media_url &&
                  task.status == DownloadTaskStatus.complete) {
                return true;
              }
            }
          }
        }
      }
    }
    return false;
  }

  bool mp3IsComplete(List<MediaModel> medias) {
    for (var media in medias) {
      if (media.media_type == 'mp3') {
        for (var task in _tasks) {
          if (task.url == media.media_url &&
              task.status == DownloadTaskStatus.complete) {
            return true;
          }
        }
      }
    }
    return false;
  }

  bool chapterIsComplete(ChapterModel chapter) {
    for (var lesson in chapter.lesson) {
      bool isDownloaded = false;
      if (widget.type == 1) {
        isDownloaded = mp3IsComplete(lesson.media);
      } else if (widget.type == 2) {
        isDownloaded = mp4IsComplete(lesson.media);
      }
      if (!isDownloaded) {
        return false;
      }
    }
    return true;
  }
}
