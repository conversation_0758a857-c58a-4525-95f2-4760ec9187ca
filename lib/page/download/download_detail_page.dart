import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:just_audio/just_audio.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/manager/preferences_manager.dart';
import 'package:npemployee/model/download/download_local_model.dart';
import 'package:npemployee/model/download/task_info.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/page/download/cache_audio_player.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';
import 'package:permission_handler/permission_handler.dart';

class DownloadDetailPage extends StatefulWidget {
  final CourseListModel course;
  final List<ChapterModel> checkedChapters;
  const DownloadDetailPage({
    super.key,
    required this.course,
    required this.checkedChapters,
  });

  @override
  State<DownloadDetailPage> createState() => _DownloadDetailPageState();
}

class _DownloadDetailPageState extends State<DownloadDetailPage>
    with WidgetsBindingObserver {
  List<ChapterModel> audioChapters = [];
  List<ChapterModel> videoChapters = [];
  bool isAllSelected = false;

  int _tabIndex = 1;
  bool _isEditing = false;
  int _deleteNum = 0;

  List<DownloadTask>? downloadTasks = [];
  List<TaskInfo>? _tasks;
  final ReceivePort _port = ReceivePort();
  int downloaded = 0;
  int preDownload = 0;
  bool allPause = false;

  String? audioPlayingName;
  bool audioBacHidden = false; // 是否隐藏音频播放背景 默认不隐藏

  List<ChapterModel> currentChapters() {
    if (_tabIndex == 1) {
      return audioChapters;
    }
    return videoChapters;
  }

  // 章节选择处理
  void toggleChapterSelection(int chapterIndex) {
    setState(() {
      currentChapters()[chapterIndex].isSelected =
          !currentChapters()[chapterIndex].isSelected;
      currentChapters()[chapterIndex].lesson.forEach((content) {
        content.isSelected = currentChapters()[chapterIndex].isSelected;
      });
      updateAllSelectedStatus();
      checkedResult();
    });
  }

  // 子内容选择处理
  void toggleContentSelection(int chapterIndex, int contentIndex) {
    setState(() {
      final content = currentChapters()[chapterIndex].lesson[contentIndex];
      content.isSelected = !content.isSelected;

      // 如果所有内容都被选中，则章节也选中；反之亦然
      currentChapters()[chapterIndex].isSelected =
          currentChapters()[chapterIndex]
              .lesson
              .every((content) => content.isSelected);
      updateAllSelectedStatus();
      checkedResult();
    });
  }

  // 全选/取消全选
  void toggleAllSelection() {
    setState(() {
      isAllSelected = !isAllSelected;
      currentChapters().forEach((chapter) {
        chapter.isSelected = isAllSelected;
        chapter.lesson.forEach((content) {
          content.isSelected = isAllSelected;
        });
      });
      checkedResult();
    });
  }

  // 更新底部全选按钮状态
  void updateAllSelectedStatus() {
    setState(() {
      isAllSelected = currentChapters().every((chapter) => chapter.isSelected);
    });
  }

  // 切换章节展开/收缩状态
  void toggleChapterExpansion(int chapterIndex) {
    setState(() {
      currentChapters()[chapterIndex].isExpanded =
          !currentChapters()[chapterIndex].isExpanded;
    });
  }

  //选择结果
  void checkedResult() {
    _deleteNum = 0;
    currentChapters().forEach((ChapterModel c) {
      c.lesson.forEach((LessonModel d) {
        if (d.isSelected) {
          _deleteNum++;
        }
      });
    });
  }

  //待缓存，已缓存计算
  void downloadedNum() {
    downloaded = 0;
    preDownload = 0;
    for (final task in _tasks ?? []) {
      if (task.status == DownloadTaskStatus.complete) {
        downloaded++;
      } else if (task.status == DownloadTaskStatus.paused ||
          task.status == DownloadTaskStatus.running ||
          task.status == DownloadTaskStatus.enqueued) {
        preDownload++;
      }
    }
    setState(() {});
  }

  void _tabChange(int i) {
    _tabIndex = i;
    _getDownloadData();
    _prepare();
  }

  void _getDownloadData() {
    List<ChapterModel> videos = [];
    List<ChapterModel> audios = [];
    List<ChapterModel> chapterList = widget.checkedChapters;

    if (chapterList.isNotEmpty) {
      for (var chapter in chapterList) {
        List<LessonModel> videoItems = [];
        List<LessonModel> audioItems = [];
        // 按照 URL 判断类型，将内容分开
        for (var lesson in chapter.lesson) {
          for (var media in lesson.media) {
            if (media.media_type == 'mp4') {
              videoItems.add(lesson);
            } else if (media.media_type == 'mp3') {
              audioItems.add(lesson);
            }
          }
        }

        // 如果存在视频内容，创建新的章节加入视频列表
        if (videoItems.isNotEmpty) {
          videos.add(ChapterModel(
            chapter.course_id,
            chapter.id,
            chapter.phrase,
            chapter.sort_order,
            chapter.title,
            chapter.lesson,
          ));
        }

        // 如果存在音频内容，创建新的章节加入音频列表
        if (audioItems.isNotEmpty) {
          audios.add(ChapterModel(
            chapter.course_id,
            chapter.id,
            chapter.phrase,
            chapter.sort_order,
            chapter.title,
            chapter.lesson,
          ));
        }
      }

      // 打印结果或进一步处理
      print('Video Chapters: ${videos.length}');
      print('Audio Chapters: ${audios.length}');
      if (_tabIndex == 1) {
        audioChapters = audios;
      } else {
        videoChapters = videos;
      }
      setState(() {});
    }
  }

  ///----------------------------------------- 下载相关
  Future<bool> _checkPermission() async {
    if (Platform.isIOS) {
      return true;
    }

    if (Platform.isAndroid) {
      final info = await DeviceInfoPlugin().androidInfo;
      if (info.version.sdkInt > 28) {
        return true;
      }

      final status = await Permission.storage.status;
      if (status == PermissionStatus.granted) {
        return true;
      }

      final result = await Permission.storage.request();
      return result == PermissionStatus.granted;
    }

    throw StateError('unknown platform');
  }

  void _prepare() async {
    downloadTasks = await FlutterDownloader.loadTasks();

    _tasks = [];

    for (var chapter in currentChapters()) {
      for (var lesson in chapter.lesson) {
        for (var media in lesson.media) {
          if (_tabIndex == 1) {
            if (media.media_type == 'mp3') {
              _tasks?.add(TaskInfo(
                  name: lesson.title,
                  link: media.media_url,
                  size: media.getSize));
            }
          } else {
            if (media.media_type == 'mp4') {
              _tasks?.add(TaskInfo(
                  name: lesson.title,
                  link: media.media_url,
                  size: media.getSize));
            }
          }
        }
      }
    }

    for (final task in downloadTasks ?? []) {
      for (final info in _tasks ?? []) {
        if (task.url == info.link) {
          info
            ..taskId = task.taskId
            ..status = task.status
            ..progress = task.progress;
        }
      }
    }

    if (await _checkPermission()) {
      for (var element in _tasks ?? []) {
        int index = (downloadTasks ?? [])
            .indexWhere((info) => info.url == element.link);
        if (index == -1) {
          //新增下载任务
          for (final chapter in widget.checkedChapters) {
            for (final lesson in chapter.lesson) {
              for (var media in lesson.media) {
                if (element.link == media.media_url) {
                  element.status = DownloadTaskStatus.enqueued;
                  _requestDownload(element, media.media_type);
                }
              }
            }
          }
        }
      }
    }
    downloadedNum();
  }

  void _bindBackgroundIsolate() {
    final isSuccess = IsolateNameServer.registerPortWithName(
      _port.sendPort,
      'downloader_send_port',
    );
    if (!isSuccess) {
      _unbindBackgroundIsolate();
      _bindBackgroundIsolate();
      return;
    }
    _port.listen((dynamic data) {
      final taskId = (data as List<dynamic>)[0] as String;
      final status = DownloadTaskStatus.fromInt(data[1] as int);
      final progress = data[2] as int;

      print(
        'Callback on UI isolate: '
        'task ($taskId) is in status ($status) and process ($progress)',
      );

      if (_tasks != null && _tasks!.isNotEmpty) {
        final task = _tasks!.firstWhere((task) => task.taskId == taskId);
        setState(() {
          task
            ..status = status
            ..progress = progress;
        });
      }
      if (progress == 100 && status == DownloadTaskStatus.complete) {
        downloadedNum();
      }
    });
  }

  void _unbindBackgroundIsolate() {
    IsolateNameServer.removePortNameMapping('downloader_send_port');
  }

  @pragma('vm:entry-point')
  static void downloadCallback(
    String id,
    int status,
    int progress,
  ) {
    debugPrint(
      'Callback on background isolate: '
      'task ($id) is in status ($status) and process ($progress)',
    );
    IsolateNameServer.lookupPortByName('downloader_send_port')
        ?.send([id, status, progress]);
  }

  ///下载
  Future<void> _requestDownload(TaskInfo task, String type) async {
    for (DownloadTask downloadTask in downloadTasks ?? []) {
      if (downloadTask.filename == '${task.name}.$type') {
        await FlutterDownloader.remove(taskId: downloadTask.taskId);
      }
    }
    task.taskId = await FlutterDownloader.enqueue(
      url: task.link!,
      savedDir: await AppInfo().capabilityDownloadDir,
      saveInPublicStorage: false,
      openFileFromNotification: false,
      fileName: '${task.name}.$type',
    );
  }

  ///全部暂停或开始
  Future<void> _allTasksPauseResume() async {
    for (final task in _tasks ?? []) {
      if (task.status == DownloadTaskStatus.enqueued ||
          task.status == DownloadTaskStatus.running) {
        await FlutterDownloader.pause(taskId: task.taskId);
      } else if (task.status == DownloadTaskStatus.paused) {
        final newTaskId = await FlutterDownloader.resume(taskId: task.taskId);
        task.taskId = newTaskId;
      }
    }
  }

  ///删除任务同时删除缓存文件
  Future<void> _deleteTasks() async {
    List<LessonModel> toRemove = [];
    Directory cacheDir = Directory(await AppInfo().capabilityDownloadDir);
    final files = cacheDir.listSync();

    // 检查是否全选
    bool isAllSelected = currentChapters().every(
        (chapter) => chapter.lesson.every((lesson) => lesson.isSelected));

    if (isAllSelected) {
      // 删除所有下载任务和缓存文件
      for (TaskInfo task in _tasks ?? []) {
        // 删除下载任务
        if (task.taskId != null) {
          await FlutterDownloader.remove(taskId: task.taskId!);
          debugPrint('--------- 删除下载任务 = ${task.name}');
        }

        // 删除本地缓存文件
        for (var file in files) {
          String filename = file.path.split('/').last;
          if (filename == '${task.name}.${task.link?.split('.').last}') {
            await file.delete();
            debugPrint('--------- 删除本地缓存文件 = $filename');
          }
        }
      }

      // 删除本地存储的课程信息
      List<DownloadLocalModel> localDownloads =
          await PreferencesManager().getCapabilityDownloadsLocal();
      int index =
          localDownloads.indexWhere((e) => e.course.id == widget.course.id);
      if (index != -1) {
        localDownloads.removeAt(index);
        await PreferencesManager().setCapabilityDownloadsLocal(localDownloads);
      }

      // 返回上一页
      if (mounted) {
        Navigator.of(context).pop();
      }
      return;
    }

    // 非全选情况下的删除逻辑
    for (var chapter in currentChapters()) {
      for (var lesson in chapter.lesson) {
        int index = _tasks!.indexWhere((task) =>
            lesson.media.any((media) => task.link == media.media_url));
        if (lesson.isSelected && index != -1) {
          // 删除本地缓存文件
          for (var file in files) {
            String filename = file.path.split('/').last;
            if (filename ==
                '${_tasks?[index].name}.${_tasks?[index].link?.split('.').last}') {
              await file.delete();
              debugPrint('--------- 删除本地缓存文件 = $filename');
            }
          }

          // 删除下载任务
          await FlutterDownloader.remove(taskId: _tasks![index].taskId!);
          debugPrint('--------- 删除下载任务 = ${_tasks![index].name}');
          toRemove.add(lesson);
        }
      }
    }

    // 从章节中移除选中的课程
    for (var i = currentChapters().length - 1; i >= 0; i--) {
      ChapterModel chapter = currentChapters()[i];
      for (var j = chapter.lesson.length - 1; j >= 0; j--) {
        LessonModel lesson = chapter.lesson[j];
        if (toRemove.any((item) => item.id == lesson.id)) {
          chapter.lesson.removeAt(j);
        }
      }

      // 如果章节内所有课程都被删除,则移除该章节
      if (chapter.lesson.isEmpty) {
        currentChapters().removeAt(i);
      }
    }

    // 更新本地存储
    await PreferencesManager().removeCapabilityDownloadLocal(
        DownloadLocalModel(widget.course, currentChapters()));

    setState(() {});
  }

  void _saveDownloadLocal() {
    DownloadLocalModel downloadLocalModel =
        DownloadLocalModel(widget.course, widget.checkedChapters);
    PreferencesManager()
        .saveOrUpdateCapabilityDownloadLocal(downloadLocalModel);
  }

  @override
  void initState() {
    super.initState();

    _saveDownloadLocal();

    if (widget.checkedChapters.first.lesson.first.media.first.media_type ==
        'mp4') {
      _tabIndex = 2;
    } else {
      _tabIndex = 1;
    }

    _getDownloadData();

    _bindBackgroundIsolate();
    FlutterDownloader.registerCallback(downloadCallback, step: 1);
    _prepare();
  }

  @override
  void dispose() {
    _unbindBackgroundIsolate();
    audioPlayingName = null;

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
            backgroundColor: const Color(0xFFF7F8FD),
            appBar: CommonNav(
              title: widget.course.name,
              rightWidget: [
                IconButton(
                    onPressed: () {
                      setState(() {
                        _isEditing = !_isEditing;
                      });
                    },
                    icon: const Icon(Icons.edit,
                        color: Color(0xFF000000), size: 18))
              ],
            ),
            body: Column(
              children: [
                _tabView(),
                _middleView(),
                _listView(),
              ],
            ),
            bottomNavigationBar: _isEditing ? _bottomView() : null),
        if (_tabIndex == 1 &&
            !audioBacHidden &&
            !_isEditing &&
            audioPlayingName != null)
          GestureDetector(
            onTap: () {
              setState(() {
                audioBacHidden = true;
              });
            },
            child: Container(
              width: ScreenUtil().screenWidth,
              height: ScreenUtil().screenHeight,
              color: const Color(0xFF000000).withOpacity(0.6),
            ),
          ),
        if (_tabIndex == 1 && audioPlayingName != null && !_isEditing)
          Positioned(
              bottom: 0,
              child: CacheAudioPlayer(
                fileName: audioPlayingName,
                bacHidden: audioBacHidden,
                onLowerViewTap: () {
                  setState(() {
                    audioBacHidden = false;
                  });
                },
                onClose: () {
                  setState(() {
                    audioBacHidden = true;
                  });
                },
              )),
      ],
    );
  }

  Widget _tabView() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          VideoTabItem(
            title: '音频列表',
            isSelect: _tabIndex == 1,
            onTap: () => _tabChange(1),
          ),
          VideoTabItem(
            title: '视频列表',
            isSelect: _tabIndex == 2,
            onTap: () => _tabChange(2),
          ),
        ],
      ),
    );
  }

  Widget _middleView() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '已下载$downloaded节 待下载$preDownload节',
            style: TextStyle(color: const Color(0xFF999999), fontSize: 14.sp),
          ),
          Visibility(
            visible: preDownload > 0,
            child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  setState(() {
                    allPause = !allPause;
                  });
                  _allTasksPauseResume();
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Text(
                    !allPause ? '全部暂停' : '全部开始',
                    style: TextStyle(
                        color: const Color(0xFF0054FF),
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500),
                  ),
                )),
          ),
        ],
      ),
    );
  }

  Widget _bottomView() {
    return Container(
      decoration: const BoxDecoration(color: Colors.white, boxShadow: [
        BoxShadow(
            color: Color.fromRGBO(230, 230, 230, 0.5),
            offset: Offset(0, -0.5),
            blurRadius: 0,
            spreadRadius: 0)
      ]),
      height: 64.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: ScreenUtil().screenWidth / 2,
            child: TextButton(
                onPressed: () => toggleAllSelection(),
                child: Text(
                  '全选',
                  style: TextStyle(
                      color: const Color(0xFF333333), fontSize: 16.sp),
                )),
          ),
          SizedBox(
            width: ScreenUtil().screenWidth / 2,
            child: TextButton(
                isSemanticButton: false,
                onPressed: () => _deleteTasks(),
                child: Text(
                  _deleteNum == 0 ? '删除' : '删除($_deleteNum)',
                  style: TextStyle(
                      color: const Color(0xFFE02020), fontSize: 16.sp),
                )),
          )
        ],
      ),
    );
  }

  Widget _listView() {
    return Expanded(
        child: currentChapters().isEmpty
            ? const NoDataPage()
            : ListView.builder(
                itemCount: currentChapters().length,
                itemBuilder: (c, index) {
                  // return _buildChapterTile(chapters[index]);
                  return _itemBuilder(c, index);
                }));
  }

  Widget _itemBuilder(c, index) {
    final chapter = currentChapters()[index];
    String chapterTime = ValidatorUtils.formatDuration(
        chapter.lesson.fold(0, (total, time) => total + time.total_time));
    double size = 0;
    for (var lesson in chapter.lesson) {
      for (var media in lesson.media) {
        size += media.getSize;
      }
    }
    return GestureDetector(
      onTap: () => toggleChapterExpansion(index),
      child: Container(
        margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 16.h),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(16.r)),
        child: Column(
          children: [
            // Chapter Header with Expansion Control
            Container(
              padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 6.w),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => toggleChapterExpansion(index),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (_isEditing)
                          GestureDetector(
                            onTap: () => toggleChapterSelection(index),
                            behavior: HitTestBehavior.opaque,
                            child: Container(
                              margin: EdgeInsets.only(top: 4.h),
                              alignment: Alignment.topCenter,
                              width: 32,
                              height: 32,
                              child: Image.asset(
                                  chapter.isSelected
                                      ? 'assets/png/download/select.png'
                                      : 'assets/png/download/unselect.png',
                                  width: 16,
                                  height: 16),
                            ),
                          ),
                        if (!_isEditing) SizedBox(width: 16.w),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                                width: 260.w,
                                child: Text(chapter.title,
                                    style: TextStyle(
                                            color: const Color(0xFF333333),
                                            fontSize: 16.sp)
                                        .pfMedium)),
                            SizedBox(height: 2.h),
                            Row(children: [
                              _subWidget(chapterTime, imgPath: 'time.png'),
                              SizedBox(width: 15.w),
                              _subWidget('${size.toStringAsFixed(1)}MB',
                                  imgPath: 'memory.png'),
                              SizedBox(width: 15.w),
                            ])
                          ],
                        ),
                      ],
                    ),
                  ),
                  const Expanded(child: SizedBox()),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () => toggleChapterExpansion(index),
                    child: Container(
                      alignment: Alignment.topCenter,
                      width: 32,
                      height: 32,
                      child: Image.asset(
                          chapter.isExpanded
                              ? 'assets/png/download/drop_down.png'
                              : 'assets/png/download/drop_open.png',
                          width: 16,
                          height: 16),
                    ),
                  ),
                ],
              ),
            ),
            // Contents of the Chapter
            if (chapter.isExpanded)
              ...chapter.lesson.map((content) {
                int contentIndex = chapter.lesson.indexOf(content);
                String status = '';
                TaskInfo? taskInfo;
                for (final task in _tasks ?? []) {
                  for (var media in content.media) {
                    if (task.link == media.media_url) {
                      taskInfo = task;
                      if (task.status == DownloadTaskStatus.complete) {
                        status = '已缓存';
                      } else if (task.status == DownloadTaskStatus.paused) {
                        status = '已暂停';
                      } else if (task.status == DownloadTaskStatus.failed) {
                        status = '缓存失败';
                      } else if (task.status == DownloadTaskStatus.running) {
                        status = '缓存中 ${task.progress}%';
                      } else if (task.status == DownloadTaskStatus.enqueued) {
                        status = '待缓存';
                      }
                    }
                  }
                }
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () async {
                    if (!_isEditing) {
                      if (taskInfo?.status == DownloadTaskStatus.paused) {
                        final newTaskId = await FlutterDownloader.resume(
                            taskId: taskInfo!.taskId!);
                        if (newTaskId != null) taskInfo.taskId = newTaskId;
                      } else if (taskInfo?.status ==
                          DownloadTaskStatus.running) {
                        FlutterDownloader.pause(taskId: taskInfo!.taskId!);
                      } else if (taskInfo?.status ==
                          DownloadTaskStatus.failed) {
                        final newTaskId = await FlutterDownloader.retry(
                            taskId: taskInfo!.taskId!);
                        if (newTaskId != null) taskInfo.taskId = newTaskId;
                      } else if (taskInfo?.status ==
                          DownloadTaskStatus.complete) {
                        if (_tabIndex == 1) {
                          setState(() {
                            audioPlayingName = Uri.encodeComponent(
                                '${taskInfo?.name ?? ''}.mp3');
                          });
                        } else {
                          String fileName = '${taskInfo?.name ?? ''}.mp4';
                          NavigatorUtils.push(
                              context, StudyRouter.cacheChewiePlayer,
                              arguments: {
                                'fileName': Uri.encodeComponent(fileName)
                              });
                        }
                      }
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 30.w, top: 8.h, bottom: 8.h, right: 16.w),
                    child: Row(
                      children: [
                        if (_isEditing)
                          GestureDetector(
                            onTap: () =>
                                toggleContentSelection(index, contentIndex),
                            behavior: HitTestBehavior.opaque,
                            child: Container(
                              alignment: Alignment.topCenter,
                              width: 32,
                              height: 32,
                              child: Image.asset(
                                  chapter.lesson[contentIndex].isSelected
                                      ? 'assets/png/download/select.png'
                                      : 'assets/png/download/unselect.png',
                                  width: 16,
                                  height: 16),
                            ),
                          ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                                width: 260.w,
                                child: Text(content.title,
                                    style: TextStyle(
                                            color: audioPlayingName ==
                                                    Uri.encodeComponent(
                                                        '${taskInfo?.name ?? ''}.mp3')
                                                ? const Color(0xFF0054FF)
                                                : const Color(0xFF222222),
                                            fontSize: 14.sp)
                                        .pfRegular)),
                            SizedBox(height: 2.h),
                            Row(children: [
                              _subWidget(
                                  ValidatorUtils.formatDuration(
                                      content.total_time),
                                  imgPath: 'time.png'),
                              SizedBox(width: 15.w),
                              _subWidget(
                                  '${content.media.fold<double>(0, (p, e) => p + e.getSize).toStringAsFixed(1)}MB',
                                  imgPath: 'memory.png'),
                              SizedBox(width: 15.w),
                              _subWidget(status),
                            ])
                          ],
                        )
                      ],
                    ),
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _subWidget(String title, {String? imgPath}) {
    return Row(
      children: [
        if (imgPath != null)
          Image.asset('assets/png/download/$imgPath', width: 10, height: 10),
        SizedBox(width: 3.w),
        Text(title,
            style: TextStyle(color: const Color(0xFF808080), fontSize: 12.sp)),
      ],
    );
  }
}
