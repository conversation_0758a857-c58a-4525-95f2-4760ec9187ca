import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:just_audio/just_audio.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/routers/navigator_utils.dart';

enum CacheAudioOptions {
  forward,
  backward,
}

class CacheAudioPlayer extends StatefulWidget {
  final String? fileName;
  final void Function()? onClose;
  final bool bacHidden;
  final void Function()? onLowerViewTap;

  const CacheAudioPlayer(
      {super.key,
      required this.fileName,
      this.onClose,
      required this.bacHidden,
      this.onLowerViewTap});

  @override
  State<CacheAudioPlayer> createState() => _CacheAudioPlayerState();
}

class _CacheAudioPlayerState extends State<CacheAudioPlayer> {
  final AudioPlayer _audioPlayer = AudioPlayer();
  late String path;
  StreamSubscription? audioStateSubscription;
  StreamSubscription? audioPositionSubscription;

  double _currentSliderValue = 0; // 当前进度
  double _totalDuration = 0;
  late bool _bacHidden;

  void _initializePlayer() async {
    if (widget.fileName == null) {
      return;
    }
    path =
        '${await AppInfo().capabilityDownloadDir}/${Uri.decodeComponent(widget.fileName!)}';
    await _audioPlayer.setFilePath(path);
    _totalDuration = _audioPlayer.duration?.inSeconds.toDouble() ?? 0;
    _audioPlayer.play();
    setState(() {});
  }

  //音乐播放进度
  void _audioPlayerStreams() {
    audioStateSubscription = _audioPlayer.playerStateStream.listen((event) {
      if (event.processingState == ProcessingState.completed) {
        _audioPlayer.stop();
      }
    });
    audioPositionSubscription = _audioPlayer.positionStream.listen((data) {
      final position = data.inSeconds;
      _currentSliderValue = double.parse(position.toString());
      if (_currentSliderValue > _totalDuration) {
        _currentSliderValue = _totalDuration;
      }
      setState(() {});
    });
  }

  @override
  void initState() {
    super.initState();
    _bacHidden = widget.bacHidden;
    _audioPlayerStreams();
    _initializePlayer();
  }

  @override
  void didUpdateWidget(covariant CacheAudioPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.fileName != widget.fileName) {
      _audioPlayer.stop();
      _initializePlayer();
    }
    if (widget.bacHidden != oldWidget.bacHidden) {
      setState(() {
        _bacHidden = widget.bacHidden;
      });
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    audioStateSubscription?.cancel();
    audioStateSubscription = null;
    audioPositionSubscription?.cancel();
    audioPositionSubscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return !_bacHidden ? _higherView() : _lowerView();
  }

  Widget _higherView() {
    return Container(
      width: ScreenUtil().screenWidth,
      height: 287.5.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.1),
            blurRadius: 26,
            spreadRadius: 0,
            offset: const Offset(-2.5, -4.5),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          SizedBox(height: 16.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: Text(
                  '${Uri.decodeComponent(widget.fileName!).replaceAll('.mp3', '')}',
                  style:
                      TextStyle(color: const Color(0xFF222222), fontSize: 17.sp)
                          .pfSemiBold,
                )),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    widget.onClose?.call();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    // width: ScreenUtil().screenWidth,
                    height: 30,
                    alignment: Alignment.centerRight,
                    child: SvgPicture.asset(
                      'assets/svg/mine/close_icon.svg',
                      width: 30,
                      height: 30,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 进度条
          Container(
            // padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: [
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    trackHeight: 3.h,
                    thumbShape: RoundSliderThumbShape(enabledThumbRadius: 6),
                  ),
                  child: Slider(
                    value: _currentSliderValue,
                    min: 0,
                    max: _totalDuration,
                    activeColor: Colors.black,
                    inactiveColor: Colors.grey[300],
                    onChanged: (value) {
                      setState(() {
                        _currentSliderValue = value;
                      });
                    },
                    onChangeEnd: (value) {
                      _audioPlayer.seek(Duration(seconds: value.toInt()));
                    },
                  ),
                ),
                // 显示当前时间和总时间
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _formatDuration(_currentSliderValue),
                        style: TextStyle(color: Colors.grey, fontSize: 10.sp)
                            .pfSemiBold,
                      ),
                      Text(
                        _formatDuration(_totalDuration),
                        style: TextStyle(color: Colors.grey, fontSize: 10.sp)
                            .pfSemiBold,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 30.h),
          // 控制按钮
          Container(
            width: ScreenUtil().screenWidth,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(width: 20.w),
                _buildControlButton('assets/png/xiaoxin/audio_back.png',
                    CacheAudioOptions.backward, 20), // 倒退 10 秒
                /* SizedBox(width: 16),
            _buildControlButton('assets/png/xiaoxin/audio_previous.png',
                AudioOptions.prev, 27), // 上一首 */
                SizedBox(width: 16),
                _buildPlayPauseButton(), // 播放/暂停
                /* SizedBox(width: 16),
            _buildControlButton('assets/png/xiaoxin/audio_next.png',
                AudioOptions.next, 27), // 下一首 */
                SizedBox(width: 16),
                _buildControlButton('assets/png/xiaoxin/audio_forward.png',
                    CacheAudioOptions.forward, 20), // 前进 10 秒
                SizedBox(width: 20.w),
              ],
            ),
          ),
          SizedBox(height: 16.h + ScreenUtil().bottomBarHeight),
        ],
      ),
    );
  }

  Widget _lowerView() {
    return GestureDetector(
      onTap: () {
        widget.onLowerViewTap?.call();
      },
      child: Container(
        color: Colors.white,
        width: ScreenUtil().screenWidth,
        padding: EdgeInsets.fromLTRB(
            16.w, 16.h, 16.w, ScreenUtil().bottomBarHeight + 16.h),
        child: Row(
          children: [
            Expanded(
                child: Text(
              Uri.decodeComponent(widget.fileName!).replaceAll('.mp3', ''),
              style: TextStyle(color: const Color(0xFF333333), fontSize: 14.sp)
                  .pfRegular,
            )),
            SizedBox(width: 8.w),
            GestureDetector(
              onTap: () {
                if (_audioPlayer.playing) {
                  _audioPlayer.pause();
                } else {
                  _audioPlayer.play();
                }
                setState(() {});
              },
              behavior: HitTestBehavior.opaque,
              child: SizedBox(
                width: 21,
                height: 21,
                child: Image.asset(_audioPlayer.playing
                    ? 'assets/png/xiaoxin/audio_pause.png'
                    : 'assets/png/xiaoxin/audio_play.png'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 格式化时间显示
  String _formatDuration(double seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final secs = (seconds % 60).toStringAsFixed(0).padLeft(2, '0');
    return '$minutes:$secs';
  }

  // 播放/暂停按钮
  Widget _buildPlayPauseButton() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[800],
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: _audioPlayer.playing
            ? const Icon(Icons.pause, color: Colors.white)
            : const Icon(Icons.play_arrow, color: Colors.white),
        iconSize: 45,
        onPressed: () {
          // 这里可以添加播放/暂停的逻辑
          if (_audioPlayer.playing) {
            _audioPlayer.pause();
          } else {
            if (_currentSliderValue >= _totalDuration) {
              _audioPlayer.seek(Duration.zero);
            }
            _audioPlayer.play();
          }
          setState(() {});
        },
      ),
    );
  }

  // 其他控制按钮（倒退10秒、上一首、下一首、前进10秒）
  Widget _buildControlButton(
      String iconNamed, CacheAudioOptions option, double size) {
    return IconButton(
      icon: ImageIcon(
        AssetImage(
          iconNamed,
        ),
        size: size,
      ),
      iconSize: 30,
      onPressed: () async {
        switch (option) {
          case CacheAudioOptions.forward:
            skipForward();
            break;
          case CacheAudioOptions.backward:
            skipBackward();
            break;
          default:
        }
      },
    );
  }

  void skipForward() {
    final currentPosition = _audioPlayer.position; // 当前播放位置
    final duration = _audioPlayer.duration; // 总时长（可能为 null）

    if (duration != null) {
      // 计算快进后的位置
      final newPosition = currentPosition + const Duration(seconds: 10);
      if (newPosition <= duration) {
        _audioPlayer.seek(newPosition);
      } else {
        _audioPlayer.seek(duration); // 如果超出总时长，则跳到结尾
      }
    }
  }

  void skipBackward() async {
    final currentPosition = _audioPlayer.position; // 当前播放位置

    // 计算倒退后的位置
    final newPosition = currentPosition - const Duration(seconds: 10);
    if (newPosition >= Duration.zero) {
      _audioPlayer.seek(newPosition);
    } else {
      _audioPlayer.seek(Duration.zero); // 如果小于 0，则跳到开头
    }
  }
}
