import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/page/download/cache_custom_control.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/study/chewie/custom_control.dart';
import 'package:video_player/video_player.dart';

class CacheChewiePlayer extends StatefulWidget {
  final String fileName;
  const CacheChewiePlayer({super.key, required this.fileName});

  @override
  State<CacheChewiePlayer> createState() => _CacheChewiePlayerState();
}

class _CacheChewiePlayerState extends State<CacheChewiePlayer>
    with WidgetsBindingObserver {
  late String path;
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;

  void _initializePlayer() async {
    path =
        '${await AppInfo().capabilityDownloadDir}/${Uri.decodeComponent(widget.fileName)}';
    File file = File(path);
    _videoPlayerController = VideoPlayerController.file(file);
    await _videoPlayerController?.initialize();

    _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: true,
        autoInitialize: true,
        showControlsOnInitialize: false,
        bufferingBuilder: (context) => Container(),
        customControls: CacheCustomControl(),
        allowMuting: false,
        showOptions: false,
        materialProgressColors: ChewieProgressColors(
            playedColor: Colors.white,
            bufferedColor: Colors.white.withOpacity(0.4),
            handleColor: Colors.white,
            backgroundColor: Colors.white.withOpacity(0.3)));

    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializePlayer();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('App 在前台');
        break;
      case AppLifecycleState.inactive:
        debugPrint('App 非活动状态，比如打电话');
        break;
      case AppLifecycleState.paused:
        debugPrint('App 退到后台');
        // _videoPlayerController?.pause();
        break;
      case AppLifecycleState.detached:
        debugPrint('App 应用完全分离');
        break;
      default:
    }
  }

  @override
  void dispose() {
    _videoPlayerController?.dispose().then((v) {
      _videoPlayerController = null;
    });
    _chewieController?.dispose();
    _chewieController = null;
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: ScreenUtil().screenWidth,
          height: ScreenUtil().screenHeight,
          color: Colors.black,
          child: _chewieController != null &&
                  _chewieController!.videoPlayerController.value.isInitialized
              ? Chewie(
                  controller: _chewieController!,
                )
              : const Center(
                  child: SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(
                        color: Colors.white, strokeWidth: 3),
                  ),
                ),
        ),
        Positioned(
          top: ScreenUtil().statusBarHeight,
          left: 5.w,
          child: Container(
            child: IconButton(
              onPressed: () {
                NavigatorUtils.pop(context);
              },
              icon: const Icon(Icons.arrow_back_ios,
                  size: 18, color: Colors.white),
            ),
          ),
        )
      ],
    );
  }
}
