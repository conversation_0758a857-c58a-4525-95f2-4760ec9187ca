import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:disk_space_plus/disk_space_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/manager/preferences_manager.dart';
import 'package:npemployee/model/download/download_local_model.dart';
import 'package:npemployee/model/download/task_info.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/page/download/ppt_download_page.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';

class MyDownloadPage extends StatefulWidget {
  const MyDownloadPage({super.key});

  @override
  State<MyDownloadPage> createState() => _MyDownloadPageState();
}

class _MyDownloadPageState extends State<MyDownloadPage> {
  int _tabIndex = 1;
  bool _autoClear = false;
  bool _isEditing = false;
  bool _isAllSelect = false;
  int _deleteNum = 0;

  List<ChapterModel> chapters = [];
  List<DownloadTask> downloadList = [];
  List<DownloadLocalModel> courseList = [];
  List<TaskInfo> pptDeleteList = [];

  String _total = '--';
  String _used = '--';

  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  final GlobalKey<PptDownloadPageState> pptItemState =
      GlobalKey<PptDownloadPageState>();

  void _getDownloadList() async {
    downloadList.clear();
    downloadList = await FlutterDownloader.loadTasks() ?? [];
    if (_tabIndex == 1) {
      courseList.clear();

      courseList = await PreferencesManager().getCapabilityDownloadsLocal();
    }
    _getMemory();
  }

  void _delete() async {
    if (_deleteNum == 0) {
      ToastUtils.show('请选择要删除的文件');
      return;
    }

    if (_tabIndex == 1) {
      List<DownloadLocalModel> localDownloads =
          await PreferencesManager().getCapabilityDownloadsLocal();
      for (DownloadLocalModel model in courseList) {
        if (model.isSelect) {
          // 删除下载任务和缓存文件
          Directory cacheDir = Directory(await AppInfo().capabilityDownloadDir);
          final files = cacheDir.listSync();
          for (var chapter in model.chapters) {
            for (var lesson in chapter.lesson) {
              for (var media in lesson.media) {
                for (DownloadTask task in downloadList) {
                  if (task.url == media.media_url) {
                    await FlutterDownloader.remove(taskId: task.taskId);
                    debugPrint('--------- 删除下载任务 = ${task.filename}');
                  }
                }
                for (var file in files) {
                  if (file.path.split('/').last ==
                      '${lesson.title}.${media.media_type}') {
                    await file.delete();
                    debugPrint(
                        '--------- 删除本地文件 = ${file.path.split('/').last}');
                  }
                }
              }
            }
          }
          // 删除本地存储的课程信息
          int index =
              localDownloads.indexWhere((e) => e.course.id == model.course.id);
          if (index != -1) {
            localDownloads.removeAt(index);
          }
        }
      }
      await PreferencesManager().setCapabilityDownloadsLocal(localDownloads);
    } else {
      for (TaskInfo task in pptDeleteList) {
        await FlutterDownloader.remove(taskId: task.taskId!);
        debugPrint('--------- 删除下载任务 = ${task.name}');

        // 删除本地缓存文件
        Directory cacheDir = Directory(await AppInfo().pptDownloadDir);
        final files = cacheDir.listSync();
        for (var file in files) {
          String filename = file.path.split('/').last;
          if (filename == '${task.name}.${task.link?.split('.').last}') {
            await file.delete();
            debugPrint('--------- 删除本地缓存文件 = $filename');
          }
        }

        await PreferencesManager().removeMeetingPptDownload(task);
        debugPrint('--------- 删除本地缓存数据 = ${task.name}');
        pptItemState.currentState?.refreshUI();
      }
    }
    _getDownloadList();
    _isEditing = false;
    _isAllSelect = false;
    _deleteNum = 0;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();

    _getDownloadList();
  }

  void _getMemory() async {
    String path;
    double diskSpace = 0;

    if (_tabIndex == 1) {
      path = await AppInfo().capabilityDownloadDir;
    } else {
      path = await AppInfo().pptDownloadDir;
    }

    diskSpace = await DiskSpacePlus.getFreeDiskSpaceForPath(path) ?? 0;

    Directory dir = Directory(path);
    double folderSize = 0;
    await for (var entity in dir.list(recursive: true, followLinks: false)) {
      if (entity is File) {
        folderSize += await entity.length();
      }
    }
    debugPrint('文件夹大小: ${folderSize / (1024 * 1024)} MB');
    _total = diskSpace >= 1024
        ? '${(diskSpace / 1024).toStringAsFixed(1)}G'
        : '${diskSpace.toStringAsFixed(1)}M';
    _used = (folderSize / (1024 * 1024)) >= 1024
        ? '${(folderSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}G'
        : '${(folderSize / (1024 * 1024)).toStringAsFixed(1)}M';

    if (!mounted) return;
    setState(() {});
  }

  // 已选
  void _checkedItem() {
    _deleteNum = 0;
    if (_tabIndex == 1) {
      courseList.forEach((e) {
        if (e.isSelect) {
          _deleteNum++;
        }
      });
    } else {
      pptDeleteList.forEach((e) {
        if (e.isSelected) {
          _deleteNum++;
        }
      });
    }
    setState(() {});
  }

  //全选
  void _checkedAll() {
    setState(() {
      _isAllSelect = !_isAllSelect;
      if (_tabIndex == 1) {
        courseList.forEach((e) {
          e.isSelect = _isAllSelect;
        });
      } else {
        pptDeleteList.forEach((e) {
          e.isSelected = _isAllSelect;
        });
      }
      _checkedItem();
    });
  }

  //全不选
  void _unCheckedAll() {
    setState(() {
      _isAllSelect = false;
      for (var e in courseList) {
        e.isSelect = false;
      }
      for (var e in pptDeleteList) {
        e.isSelected = false;
      }
      _deleteNum = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        appBar: CommonNav(
          title: '我的下载',
          rightWidget: [
            IconButton(
                onPressed: () {
                  setState(() {
                    _isEditing = !_isEditing;
                    if (!_isEditing) {
                      _unCheckedAll();
                    }
                  });
                },
                icon:
                    const Icon(Icons.edit, color: Color(0xFF000000), size: 18))
          ],
        ),
        body: Column(
          children: [
            _tabView(),
            _middleView(),
            _tabIndex == 1
                ? courseList.isEmpty
                    ? const Expanded(child: NoDataPage())
                    : _listView()
                : PptDownloadPage(
                    key: pptItemState,
                    isEdit: _isEditing,
                    isAllSelected: _isAllSelect,
                    onSelectedChange: (tasks) {
                      pptDeleteList = tasks;
                      _checkedItem();
                    }),
          ],
        ),
        bottomNavigationBar: _isEditing ? _bottomView() : null);
  }

  Widget _bottomView() {
    return Container(
      decoration: const BoxDecoration(color: Colors.white, boxShadow: [
        BoxShadow(
            color: Color.fromRGBO(230, 230, 230, 0.5),
            offset: Offset(0, -0.5),
            blurRadius: 0,
            spreadRadius: 0)
      ]),
      height: 64.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: ScreenUtil().screenWidth / 2,
            child: TextButton(
                onPressed: () {
                  _checkedAll();
                },
                child: Text(
                  '全选',
                  style: TextStyle(
                      color: const Color(0xFF333333), fontSize: 16.sp),
                )),
          ),
          SizedBox(
            width: ScreenUtil().screenWidth / 2,
            child: TextButton(
                isSemanticButton: false,
                onPressed: () => _delete(),
                child: Text(
                  _deleteNum == 0 ? '删除' : '删除($_deleteNum)',
                  style: TextStyle(
                      color: const Color(0xFFE02020), fontSize: 16.sp),
                )),
          )
        ],
      ),
    );
  }

  Widget _listView() {
    return Expanded(
        child: downloadList.isEmpty
            ? const NoDataPage()
            : Container(
                color: Colors.white,
                child: ListView.builder(
                    itemCount: courseList.length, itemBuilder: _itemBuilder),
              ));
  }

  Widget _itemBuilder(c, index) {
    DownloadLocalModel model = courseList[index];
    return GestureDetector(
        onTap: () {
          if (_isEditing) {
            setState(() {
              courseList[index].isSelect = !courseList[index].isSelect;
              _checkedItem();
            });
          } else {
            NavigatorUtils.push(context, StudyRouter.downloadDetail,
                arguments: {
                  'course': model.course,
                  'checkedChapters': model.chapters, //同课程中，章节内容都一样
                }).then((value) {
              _getDownloadList();
            });
          }
        },
        child: Stack(
          children: [
            Container(
              margin: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16.r),
                  boxShadow: const [
                    BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.06),
                        offset: Offset(0, 2),
                        blurRadius: 11,
                        spreadRadius: 0)
                  ]),
              child: Row(
                children: [
                  isLandscapeCache.containsKey(model.course.image)
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CachedNetworkImage(
                            imageUrl: model.course.image,
                            fit: BoxFit.cover,
                            height: isLandscapeCache[model.course.image]!
                                ? 64.h
                                : 102.h,
                            width: isLandscapeCache[model.course.image]!
                                ? 119.w
                                : 73.w,
                          ),
                        )
                      : FutureBuilder(
                          future: _isLandscape(model.course.image),
                          builder: (_, snapData) {
                            if (snapData.connectionState ==
                                ConnectionState.done) {
                              final isLandscape = snapData.data ?? true;
                              return ClipRRect(
                                borderRadius: BorderRadius.circular(10.r),
                                child: CachedNetworkImage(
                                  imageUrl: model.course.image,
                                  fit: BoxFit.cover,
                                  height: isLandscape ? 64.h : 102.h,
                                  width: isLandscape ? 119.w : 73.w,
                                ),
                              );
                            }
                            return Container();
                          }),
                  SizedBox(width: 12.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        model.course.name,
                        style: TextStyle(
                            color: const Color(0xFF323640),
                            fontSize: 15.sp,
                            fontWeight: FontWeight.w500),
                      ),
                      if (model.audioNum(downloadList) > 0)
                        SizedBox(height: 6.h),
                      if (model.audioNum(downloadList) > 0)
                        Text(
                          '音频：已缓存${(model.audioNum(downloadList))}节 共${model.audioSize(downloadList)}M',
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              color: const Color(0xFF999999), fontSize: 11.sp),
                        ),
                      if (model.videoNum(downloadList) > 0)
                        SizedBox(height: 6.h),
                      if (model.videoNum(downloadList) > 0)
                        Text(
                            '视频：已缓存${model.videoNum(downloadList)}节 共${model.videoSize(downloadList)}M',
                            style: TextStyle(
                                color: const Color(0xFF999999),
                                fontSize: 11.sp)),
                    ],
                  ),
                ],
              ),
            ),
            if (_isEditing)
              Positioned(
                bottom: 0,
                right: 16.w,
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    if (_isEditing) {
                      setState(() {
                        courseList[index].isSelect =
                            !courseList[index].isSelect;
                        _checkedItem();
                      });
                    }
                  },
                  child: Container(
                    alignment: Alignment.topCenter,
                    width: 32,
                    height: 32,
                    child: Image.asset(
                        model.isSelect
                            ? 'assets/png/download/select.png'
                            : 'assets/png/download/unselect.png',
                        width: 16,
                        height: 16),
                  ),
                ),
              )
          ],
        ));
  }

  Widget _middleView() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '已占用$_used/$_total',
            style: TextStyle(color: const Color(0xFF999999), fontSize: 14.sp),
          ),
          /* if (_isEditing && _tabIndex == 1)
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                setState(() {
                  _autoClear = !_autoClear;
                });
              },
              child: Row(
                children: [
                  Text(
                    '自动清理完播内容',
                    style: TextStyle(
                        color: const Color(0xFF999999), fontSize: 14.sp),
                  ),
                  SizedBox(width: 6.w),
                  Image.asset(
                      _autoClear
                          ? 'assets/png/download/select.png'
                          : 'assets/png/download/unselect.png',
                      width: 14,
                      height: 14),
                ],
              ),
            ), */
        ],
      ),
    );
  }

  Widget _tabView() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          VideoTabItem(
            title: '能力提升',
            isSelect: _tabIndex == 1,
            onTap: () {
              setState(() {
                _tabIndex = 1;
                _isAllSelect = false;
                courseList.forEach((e) {
                  e.isSelect = _isAllSelect;
                });
                pptDeleteList.forEach((e) {
                  e.isSelected = _isAllSelect;
                });
                _deleteNum = 0;
              });
              _getMemory();
            },
          ),
          VideoTabItem(
            title: '年会PPT',
            isSelect: _tabIndex == 2,
            onTap: () {
              setState(() {
                _tabIndex = 2;
                _isAllSelect = false;
                courseList.forEach((e) {
                  e.isSelect = _isAllSelect;
                });
                pptDeleteList.forEach((e) {
                  e.isSelected = _isAllSelect;
                });
                _deleteNum = 0;
              });
              _getMemory();
            },
          ),
        ],
      ),
    );
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }
}
