import 'dart:async';
import 'dart:io';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/manager/pedometer_manager.dart';
import 'package:npemployee/methodchannel/AndroidProxy.dart';
import 'package:npemployee/methodchannel/auth_service.dart';
import 'package:npemployee/model/mine/guest_model.dart';
import 'package:npemployee/model/mine/me_nodel.dart';
import 'package:npemployee/model/mine/old_person_model.dart';
import 'package:npemployee/model/mine/user_sub_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/login/login_bloc.dart';
import 'package:npemployee/page/login/login_event.dart';
import 'package:npemployee/page/login/login_state.dart';
import 'package:npemployee/page/login/old_personal_info_page.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/tabs.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
// import 'package:ali_auth/ali_auth.dart';

import '../../Utils/validator_utils.dart';
import '../../common/dialog/privacy_policy_dialog.dart';
import '../../manager/wechat_login_manager.dart';
import '../../provider/user_service_provider.dart';
import '../../service/wechat_service.dart';

class LoginPage extends StatefulWidget {
  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  bool isChangingPhone = true; // 用于切换一键登录和验证码登录界面
  bool isAgreed = false;
  bool showErrorMessage = false;
  bool isCodeSent = false;
  bool isInitialized = false; // AliAuth SDK 是否初始化
  bool isLoggingIn = false; // 登录状态
  int countdown = 30;
  Timer? _timer; // Track the timer

  FocusNode _mobileNode = FocusNode();
  FocusNode _codeNode = FocusNode();
  bool _isWeChatInstalled = false;

  StreamSubscription? _loginSubScription;

  // /// Android 密钥
  // late String androidSk;
  // /// iOS 密钥
  // late String iosSk;

  @override
  void initState() {
    super.initState();
    _checkWeChatInstalled();
    // androidSk =
    // "3SnOXQ8eDWWVlZ+FZPI5hj4rU+BEtFnZ5y5k0mgMw8GZPqTwuVLWh1ijUdBDoisB2ete7SKAeuyEQqa5fkXCZjPDbbul2g4pJiXTI9GmC776S7Kecg7q+xDC7tSlDIUVrP7u40dX8Eoy/JKNAKQ+BHX7Y0CHiaeIuJ4TCPDETSnoWGcg6ghb+o2Nd3QWX6ZK74xj1bh5VpO5D1eaTvlOBOrNNz6TuK3Vm6QH5AmhdOfe5oR50c0Unfl+DP6CJt/NuiPF2SIPNm6ToZZ1bUbGZOwVwEUHLBLNrUQpseMeHv0=";
    // iosSk =
    // "g4R0oqln2F+p5UsBhxpLKCAzNmDpsplolrcyj76rotws297/kRsKQA/aJc6xKYEulqA6alWpOdwBx4c2GHuzI0I9ozXM2RM/IuyDM8dO/kRl+L/Z9WsXFNrccfXZr9blLMjCdMX8sZ4C+SM0mf5/OZUMF/ijpbZr/1q8Sku9E+1HCqM9FAvavj7TKn/Fc+uxx8JKzTeAjsWduTlOWhmNzS/oRP5kYv4L74fkItAriVyOjWIcj5fID3i/uxVjdfGdcHha0AOAagY=";
    //
    // _initializeAliAuth(); // 初始化AliAuth SDK
    // _setupLoginEventListener(); // 先设置监听器

    print('=============== 进入登录页面');

    _subscriptionLoginBloc();

    // MiniProgramManager().onStatusChange = (newStatusCode) { //微信登录成功监听
    //   if (newStatusCode == 203) {
    //     Navigator.of(context).pop(); // Close the dialog
    //   }
    // };

    if (!AppInfo().hasLogined) {
      if (GlobalPreferences().isShouldShowProxy && Platform.isAndroid) {
        Future.delayed(Duration.zero, () {
          //执行代码写在这里
          _showPrivacyPolicyDialog(context);
        });
      } else {
        _fastLogin();
      }
    }
  }

  Future<void> _checkWeChatInstalled() async {
    bool isInstalled = await WeChatService().checkIfWeChatInstalled();
    setState(() {
      _isWeChatInstalled = isInstalled;
    });
  }

  void _showPrivacyPolicyDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PrivacyPolicyDialog(
          onAgree: () {
            // 执行同意操作
            GlobalPreferences().isShouldShowProxy = false;
            debugPrint('初始化友盟');
            UmengCommonSdk.initCommon(
                AppInfo().umAndroidId, AppInfo().umIosID, AppInfo().umChannel);
            if (Platform.isAndroid) {
              AndroidProxy.registerProxyPlugin();
            }
            Navigator.of(context).pop();
            _fastLogin();
            // 你可以在这里调用初始化函数或其他功能
            if (Platform.isAndroid) {
              PedometerManager();
            }
          },
          onDisagree: () {
            // 执行不同意操作
            Navigator.of(context).pop();
            SystemNavigator.pop(); // 使用 SystemNavigator.pop() 来结束应用程序
            // exit(0); // 或者使用 exit(0) 来结束应用程序

            // 你可以在这里结束应用程序或其他操作
          },
        );
      },
    );
  }

  void _subscriptionLoginBloc() {
    final navContext = Navigator.of(context);
    _loginSubScription?.cancel();
    _loginSubScription = BlocManager().loginBloc.stream.listen((state) async {
      debugPrint(" -- login state change = ${state.isLogin}");
      if (state.isLogin) {
        String guid = GlobalPreferences().userLoginModel?.guid ?? '';
        UmengCommonSdk.onProfileSignIn(guid);
        //判断是否是新途径人
        ResultData? resData = await UserServiceProvider().getUserInfo();
        if (resData?.code == 1) {
          if (resData?.msg == '未注册的用户') {
            navContext.pushAndRemoveUntil(MaterialPageRoute(builder: (_) {
              return const Tabs(index: 4);
            }), (route) => false);
          } else {
            ToastUtils.show(resData?.msg ?? '');
          }
          return;
        }
        if (resData?.user_type == 'user' &&
                resData?.data['user']['status'] == 'normal' ||
            resData?.data['user']['status'] == "frozen") {
          GlobalPreferences().userInfo = MeModel.fromJson(resData?.data);
          navContext.pushNamedAndRemoveUntil(
              CommonRouter.tabs, (route) => false);
        } else if (resData?.user_type == 'old_user') {
          navContext.pushAndRemoveUntil(MaterialPageRoute(builder: (_) {
            OldPersonModel? oldPerson = OldPersonModel.fromJson(resData?.data);
            GlobalPreferences().oldUserInfo = oldPerson;
            return OldPersonalInfoPage(oldPersonModel: oldPerson);
          }), (route) => false);
        } else if (resData?.user_type == 'sub_user') {
          UserSubModel userSubModel = UserSubModel.fromJson(resData?.data);
          GlobalPreferences().userSubInfo = userSubModel;
          navContext.pushNamedAndRemoveUntil(
              CommonRouter.userSubPage, (route) => false);
        } else if (resData?.user_type == 'guest') {
          GuestModel guestModel = GuestModel.fromJson(resData?.data);
          GlobalPreferences().guestInfo = guestModel;
          navContext.pushNamedAndRemoveUntil(
              CommonRouter.guestPage, (route) => false,
              arguments: {'guestModel': guestModel});
        } else {
          navContext.pushAndRemoveUntil(MaterialPageRoute(builder: (_) {
            return const Tabs(index: 4);
          }), (route) => false);
        }
      }
    });
  }

  void _fastLogin() async {
    AuthService.handleLogin(
      onTokenReceived: (String token) {
        UserServiceProvider().identifyMobile(token).then((value) {
          if (value != null) {
            GlobalPreferences().tokenValue = value.token;
            GlobalPreferences().userLoginModel = value;
            BlocManager().loginBloc.add(LoginSuccessEvent(true));
            AuthService.quitAliAuth(); // Close AliAuth page if login succeeds
          } else {
            print('Login failed');
          }
        });
      },
      onEventReceived: (int eventCode, String description) {
        _handleLoginEvents(eventCode, description, context);
      },
      onError: (String error) {
        print("Error occurred: $error");
        BlocManager().loginBloc.add(LoginSuccessEvent(false));
      },
    );
  }

  void _handleLoginEvents(
      int eventCode, String description, BuildContext context) {
    if (eventCode == 1001) {
      print("Phone change event: $description");
      // Navigator.of(context, rootNavigator: true).push(
      //   MaterialPageRoute(builder: (context) => LoginPage()),
      // );
    } else if (eventCode == 1002) {
      if (!isAgreed) {
        EasyLoading.showError('请先勾选同意后登录');
      } else {
        print("WeChat login event: $description");
        WeChatLoginManager().handleWeChatLogin(context);
      }
    }
  }

  /// Step 1: 设置 AliAuth SDK 的登录监听器
  // void _setupLoginEventListener() {
  //   AliAuth.loginListen(
  //     onEvent: (event) {
  //       print("AliAuth 登录事件: $event");
  //     },
  //     onError: (error) {
  //       print("AliAuth 错误事件: $error");
  //     },
  //   );
  // }

  void _keyboardDismiss() {
    if (_mobileNode.hasFocus) {
      _mobileNode.unfocus();
    } else if (_codeNode.hasFocus) {
      _codeNode.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _keyboardDismiss(),
      child: Scaffold(
        extendBodyBehindAppBar: true,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          // leading: IconButton(
          //   icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          //   onPressed: () => Navigator.of(context).pop(),
          // ),
        ),
        body: Stack(
          children: [
            // 背景图片
            Positioned.fill(
              child: Image.asset(
                'assets/png/login/login_bg.png',
                fit: BoxFit.cover,
              ),
            ),
            SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height,
                ),
                child: IntrinsicHeight(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 33),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 100),
                        Text(
                          '成为\n受人尊重的教育品牌！',
                          style: TextStyle(
                            fontSize: 27.sp,
                            color: Colors.white,
                            // Automatically selects the 'Bold' version.
                          ).phBold,
                        ),
                        const SizedBox(height: 80),
                        // 根据 isChangingPhone 切换不同的登录界面
                        isChangingPhone
                            ? _buildPhoneChangeSection()
                            : _buildQuickLoginSection(),
                        const SizedBox(height: 18),
                        _buildAgreementSection(),
                        const SizedBox(height: 90),
                        if (_isWeChatInstalled) _buildOtherLoginSection(),
                        const SizedBox(height: 100),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Quick Login Section
  /// 一键登录界面
  Widget _buildQuickLoginSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '155****4326',
          style: TextStyle(
            fontSize: 40,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 68),
        _buildRoundedButton(
          title: '一键登录',
          onPressed: (phoneNumber, codeNumber) {},
          color: Colors.white,
          textColor: const Color(0xFF161B1C),
        ),
        const SizedBox(height: 16),
        _buildRoundedButton(
          title: '更换手机号',
          onPressed: (phoneNumber, codeNumber) {
            setState(() {
              isChangingPhone = true; // 用户手动切换到验证码登录
            });
          },
          color: Colors.white.withOpacity(0.2),
          textColor: const Color(0xFFFFFFFF),
        ),
      ],
    );
  }

// Phone Change Section
  /// 验证码登录界面
  Widget _buildPhoneChangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4), // 整行左侧和右侧的padding
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 使用 SizedBox 固定宽度为 60
                  SizedBox(
                    width: 60,
                    child: Text(
                      '+86',
                      style: TextStyle(fontSize: 17.sp, color: Colors.white)
                          .phMedium,
                    ),
                  ),
                  const SizedBox(width: 10),
                  BlocBuilder<LoginBloc, LoginState>(
                      buildWhen: (previous, current) {
                    return previous.phoneNumber != current.phoneNumber;
                  }, builder: (context, state) {
                    return Expanded(
                      child: TextField(
                        focusNode: _mobileNode,
                        style: TextStyle(fontSize: 17.sp, color: Colors.white)
                            .pfMedium,
                        maxLength: 11,
                        decoration: InputDecoration(
                          counter: const Offstage(),
                          border: InputBorder.none,
                          hintText: '输入手机号',
                          hintStyle:
                              TextStyle(color: Colors.white38, fontSize: 17.sp)
                                  .phMedium,
                        ),
                        onChanged: (value) {
                          BlocProvider.of<LoginBloc>(context)
                              .add(CommitPhoneEvent(value));
                        },
                        cursorColor: Colors.white,
                        keyboardType: TextInputType.phone,
                      ),
                    );
                  }),
                ],
              ),
              Container(
                margin: const EdgeInsets.only(top: 10), // 可根据需要调整下划线与输入框之间的距离
                height: 0.5,
                color: Colors.white.withOpacity(0.33), // 白色透明线
              ),
            ],
          ),
        ),
        const SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4), // 整行左侧和右侧的padding
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 使用 SizedBox 固定宽度为 60
                  SizedBox(
                    width: 60,
                    child: Text(
                      '验证码',
                      style: TextStyle(fontSize: 17.sp, color: Colors.white)
                          .pfMedium,
                    ),
                  ),
                  const SizedBox(width: 10),
                  BlocBuilder<LoginBloc, LoginState>(
                      buildWhen: (previous, current) {
                    return previous.codeNumber != current.codeNumber;
                  }, builder: (context, state) {
                    return Expanded(
                      child: TextField(
                        focusNode: _codeNode,
                        style: TextStyle(fontSize: 17.sp, color: Colors.white)
                            .phMedium,
                        maxLength: 6,
                        onChanged: (value) {
                          BlocProvider.of<LoginBloc>(context)
                              .add(CommitCodeEvent(value));
                          if (value.length == 6) {
                            FocusScope.of(context).unfocus();
                          }
                        },
                        decoration: InputDecoration(
                          counter: const Offstage(),
                          border: InputBorder.none,
                          hintText: '输入验证码',
                          hintStyle:
                              TextStyle(color: Colors.white38, fontSize: 17.sp)
                                  .phMedium,
                        ),
                        cursorColor: Colors.white,
                        keyboardType: TextInputType.number,
                      ),
                    );
                  }),

                  const SizedBox(width: 10),
                  SizedBox(width: 110, child: _buildVerificationButton()),
                ],
              ),
              Container(
                margin: const EdgeInsets.only(top: 10), // 可根据需要调整下划线与输入框之间的距离
                height: 0.5,
                color: Colors.white.withOpacity(0.33), // 白色透明线
              ),
            ],
          ),
        ),
        if (showErrorMessage)
          const Padding(
            padding: EdgeInsets.only(top: 8, left: 4),
            child: Text(
              '验证码错误，请重新输入',
              style: TextStyle(color: Color(0xFFFE4646), fontSize: 12),
            ),
          ),
        const SizedBox(height: 63),
        // 构建登录按钮
        _buildRoundedButton(
          title: '登录',
          onPressed: (phoneNumber, codeNumber) {
            // 调用 smsLogin 并根据返回值动态设置 showErrorMessage
            if (!isAgreed) {
              EasyLoading.showError('请先勾选同意后登录');
              return;
            }
            EasyLoading.show(status: '登录中');
            UserServiceProvider()
                .smsLogin(phoneNumber!, codeNumber!)
                .then((value) {
              EasyLoading.dismiss();
              if (value != null) {
                print('登录成功，token: ${value.token}');
                GlobalPreferences().tokenValue = value.token;
                GlobalPreferences().userLoginModel = value;
                BlocManager().loginBloc.add(LoginSuccessEvent(true));
                setState(() {
                  showErrorMessage = false; // 登录成功，隐藏错误信息
                });
              } else {
                print('登录失败');
                BlocManager().loginBloc.add(LoginSuccessEvent(false));
                setState(() {
                  showErrorMessage = true; // 登录失败，显示错误信息
                });
              }
            });
          },
          color: Colors.white,
          textColor: const Color(0xFF161B1C),
        ),
      ],
    );
  }

// Agreement Section
  Widget _buildAgreementSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.1), // Black with 0.1 opacity
        borderRadius:
            BorderRadius.circular(15), // Rounded corners with radius 15
      ),
      padding: const EdgeInsets.all(20), // Padding for the content
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start, // Align items to the top
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                isAgreed = !isAgreed;
              });
            },
            child: Align(
              alignment: Alignment.topCenter,
              child: Image.asset(
                isAgreed
                    ? 'assets/png/login/check_selected.png'
                    : 'assets/png/login/check_unselected.png',
                fit: BoxFit.cover,
                width: 20,
                height: 20,
              ),
            ),
          ),
          const SizedBox(width: 10), // Space between checkbox and text
          Expanded(
            child: RichText(
              text: TextSpan(
                text: '我已阅读并同意',
                style:
                    TextStyle(color: Colors.white70, fontSize: 13.sp).phRegular,
                children: [
                  TextSpan(
                    text: '《用户协议》',
                    style:
                        TextStyle(color: Colors.white, fontSize: 12.sp).phBold,
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        NavigatorUtils.push(context, CommonRouter.webview,
                            arguments: {
                              'title': '用户协议',
                              'url':
                                  'https://web.xtjstatic.cn/agreement/protocol.html',
                              'keep_alive': false,
                            });
                      },
                  ),
                  TextSpan(
                    text: ' 和 ',
                    style: TextStyle(color: Colors.white70, fontSize: 13.sp)
                        .phRegular,
                  ),
                  TextSpan(
                    text: '《隐私政策》',
                    style:
                        TextStyle(color: Colors.white, fontSize: 12.sp).phBold,
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        NavigatorUtils.push(context, CommonRouter.webview,
                            arguments: {
                              'title': '隐私政策',
                              'url':
                                  'https://web.xtjstatic.cn/agreement/privacyAgreement.htm',
                              'keep_alive': false,
                            });
                      },
                  ),
                  TextSpan(
                      text: '并同意协议内容',
                      style: TextStyle(color: Colors.white70, fontSize: 13.sp)
                          .phRegular),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

// Other Login Methods Section
  Widget _buildOtherLoginSection() {
    return Column(
      children: [
        Text(
          '其他登录方式',
          style: TextStyle(color: Colors.white, fontSize: 12.sp).phRegular,
        ),
        const SizedBox(height: 6.5),
        Center(
          child: SizedBox(
            width: 40,
            height: 40,
            child: IconButton(
              icon: Image.asset(
                  'assets/png/login/wechat_login.png'), // Adjusted to fit the parent container
              iconSize: 40, // Icon size remains 40 but constrained by SizedBox
              padding: EdgeInsets.zero, // Remove any additional padding
              onPressed: () async {
                if (!isAgreed) {
                  EasyLoading.showError('请先勾选同意后登录');
                  return;
                }
                bool isInstalled =
                    await WeChatService().checkIfWeChatInstalled();
                if (!isInstalled) {
                  ToastUtils.show("未安装微信~");
                } else {
                  WeChatLoginManager().handleWeChatLogin(context);
                }
              } // Handle WeChat login
              , // 调用登录逻辑
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRoundedButton({
    required String title,
    required Function(String? phoneNumber, String? codeNumber) onPressed,
    required Color color,
    required Color textColor,
  }) {
    return BlocBuilder<LoginBloc, LoginState>(builder: (context, state) {
      return SizedBox(
        height: 60, // Set the fixed height for the button
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            elevation: 0, // Remove shadow effect
            padding: EdgeInsets
                .zero, // Remove internal padding to respect the fixed height
          ),
          onPressed: () => onPressed(state.phoneNumber, state.codeNumber),
          child: Center(
            child: Text(
              title,
              style: TextStyle(fontSize: 16.sp, color: textColor).phMedium,
            ),
          ),
        ),
      );
    });
  }

  Widget _buildVerificationButton() {
    return BlocBuilder<LoginBloc, LoginState>(builder: (context, state) {
      return GestureDetector(
        onTap: isCodeSent
            ? null
            : () {
                // Check if the phone number is valid
                if (ValidatorUtils.isPhoneNumber(state.phoneNumber!)) {
                  UserServiceProvider()
                      .requestSmsCode(state.phoneNumber!)
                      .then((value) => {});
                  setState(() {
                    isCodeSent = true;
                    countdown = 30;
                  });
                  // Start countdown logic
                  _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
                    setState(() {
                      countdown--;
                    });
                    if (countdown == 0) {
                      timer.cancel();
                      if (mounted) {
                        setState(() {
                          isCodeSent = false;
                        });
                      }
                    }
                  });
                } else {
                  // Show an error message to the user if the phone number is invalid
                  EasyLoading.showError('手机号格式错误');
                }
              },
        child: Container(
          alignment: Alignment.center, // 使内容居中
          decoration: BoxDecoration(
            color: isCodeSent
                ? Colors.grey.withOpacity(0.23)
                : Colors.black.withOpacity(0.23),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          child: Text(
            isCodeSent ? '${countdown}s后重试' : '获取验证码',
            style: TextStyle(color: Colors.white, fontSize: 15.sp).phMedium,
          ),
        ),
      );
    });
  }

  @override
  void dispose() {
    // AliAuth.dispose(); // Clean up resources
    _loginSubScription?.cancel();
    print('=============== 离开登录页面');
    _timer
        ?.cancel(); // Cancel the timer to prevent it from running after the widget is disposed
    print('----------- login 页面消失');
    super.dispose();
  }
}
