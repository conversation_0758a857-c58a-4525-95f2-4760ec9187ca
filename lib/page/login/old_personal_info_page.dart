import 'dart:async';

import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/mine/old_person_model.dart';
import 'package:npemployee/page/login/login_page.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:npemployee/tabs.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/login/bottom_privacy_section.dart';
import 'package:npemployee/widget/login/position_selection_dialog.dart';
import 'package:npemployee/widget/login/selection_widget.dart';
import 'package:npemployee/widget/login/single_selection_dialog.dart';
import 'package:npemployee/widget/login/tree_selection_dialog.dart';

class OldPersonalInfoPage extends StatefulWidget {
  final OldPersonModel? oldPersonModel;
  const OldPersonalInfoPage({super.key, required this.oldPersonModel});

  @override
  State<OldPersonalInfoPage> createState() => _OldPersonalInfoPageState();
}

class _OldPersonalInfoPageState extends State<OldPersonalInfoPage> {
  final FocusNode _focusNode = FocusNode();
  final FocusNode _idFocusNode = FocusNode();
  final FocusNode _mobileNode = FocusNode();
  TextEditingController _mobileTextEditController = TextEditingController();
  TextEditingController _nameTextEditController = TextEditingController();
  TextEditingController _idNumberTextEditController = TextEditingController();

  Map? department;
  List? reviewerList = [];
  List<String> reviewers = [];
  List? roleList = [];
  List<String> roles = [];
  OldPersonModel? oldModel;

  bool _isAgreed = false;
  Map<String, List<dynamic>> selectedPositionsMap = {
    '入职时间': [DateTime.now().toString().split(" ").first],
    '选择部门': [],
    '选择岗位': [],
    '选择审批人': [],
  };

  late StreamSubscription<bool> keyboardSubscription;

  void _getPhoneNUmber() {
    UserServiceProvider().getPhoneNumberRegister().then((v) {
      if (v?.found == true) {
        setState(() {
          _mobileTextEditController.text = v?.mobile;
        });
      } else {
        ToastUtils.show('获取手机号失败, 请联系管理员');
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _keyboardListener();

    if (widget.oldPersonModel == null) {
      oldModel = GlobalPreferences().oldUserInfo;
    } else {
      oldModel = widget.oldPersonModel;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getPhoneNUmber();
      // _mobileTextEditController.text = oldModel?.mobile ?? '';
      _idNumberTextEditController.text = oldModel?.id_card ?? '';
      _nameTextEditController.text = oldModel?.real_name ?? '';
    });

    _getData();
  }

  @override
  void dispose() {
    keyboardSubscription.cancel();
    super.dispose();
  }

  void _keyboardListener() {
    var keyboardVisibilityController = KeyboardVisibilityController();
    // Subscribe
    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      if (!visible) {
        _nodesUnfocus();
      }
      print('Keyboard visibility update. Is visible: $visible');
    });
  }

  void _nodesUnfocus() {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
    }
    if (_idFocusNode.hasFocus) {
      _idFocusNode.unfocus();
    }
    if (_mobileNode.hasFocus) {
      _mobileNode.unfocus();
    }
  }

  void _getData() {
    UserServiceProvider()
        .oldDepartToNewDepart(oldModel?.be_did ?? '')
        .then((value) {
      if (value?.code == 0) {
        department = value?.data;
        _getRoleByDepartmentId(department?['id']);
      }
      if (mounted) {
        setState(() {
          selectedPositionsMap['选择部门'] = [department];
        });
      }
    });
  }

  void _getRoleByDepartmentId(int departmentId) {
    UserServiceProvider().getRoleList(departmentId).then((value) {
      roleList = value?.data;
      roles.clear();
      setState(() {
        roleList?.forEach((e) {
          e['roles'].forEach((r) {
            roles.add(r['role']['name']);
          });
        });
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    const double originalWidth = 1125.0;
    const double originalHeight = 714.0;
    final double aspectRatio = originalWidth / originalHeight;
    final double calculatedHeight = screenWidth / aspectRatio;

    return GestureDetector(
      onTap: () {
        _nodesUnfocus();
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Stack(
          children: [
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Image.asset(
                'assets/png/login/personal_info_bg.png',
                fit: BoxFit.cover,
                width: screenWidth,
                height: calculatedHeight,
              ),
            ),
            Positioned.fill(
              top: 93.5.h,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildIntroductionSection(),
                    SizedBox(height: 47.5.h),
                    _buildEditableField(
                        '当前登录手机号', '', '请输入手机号', _mobileTextEditController,
                        isEditable: false),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    _buildAccountIdField(),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    _buildEditableField(
                        '姓名', '', '请填写本人姓名', _nameTextEditController,
                        isEditable: true, must: true),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    _buildEditableField(
                        '身份证号', '', '请填写本人身份证', _idNumberTextEditController,
                        isEditable: true, must: true),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    SelectionWidget(
                      title: '入职时间',
                      description: '',
                      clickableText: '',
                      placeholder: '请选择',
                      selectedPositionsMap: selectedPositionsMap,
                      onClickableTextTap: () {},
                      onTap: () {
                        _showSelectionDialog(context, '入职时间');
                      },
                    ),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    SelectionWidget(
                      title: '选择部门',
                      enable: false,
                      description: '如需修改，请确认后联系客服',
                      clickableText: '',
                      placeholder: '',
                      selectedPositionsMap: selectedPositionsMap,
                      onClickableTextTap: () {},
                      onTap: () {},
                    ),
                    _buildDivider(),
                    SizedBox(height: 25.h),
                    SelectionWidget(
                      title: '选择岗位',
                      description: '如无对应岗位，请点击反馈',
                      clickableText: '请点击反馈',
                      placeholder: '校长、管理员、客服',
                      selectedPositionsMap: selectedPositionsMap,
                      onClickableTextTap: () {
                        WeChatService().launchWeChatWork();
                      },
                      onTap: () {
                        if (selectedPositionsMap['选择部门']!.isEmpty) {
                          ToastUtils.show('请选择部门');
                          return;
                        }
                        _showSelectionDialog(context, '选择岗位');
                      },
                    ),
                    _buildDivider(),
                    SizedBox(height: 30.h),
                    BottomPrivacySection(
                      isAgreed: _isAgreed,
                      submitText: '确认登录',
                      onAgreementChanged: () {
                        setState(() {
                          _isAgreed = !_isAgreed;
                        });
                      },
                      onSubmitForm: _submitForm,
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 53.h,
              left: 5.w,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  AppInfo().clearLoginStatu();
                  Navigator.of(context).pushNamedAndRemoveUntil(
                      CommonRouter.loginPage, (route) => false);
                },
                child: Container(
                  width: 30.w,
                  height: 30.h,
                  alignment: Alignment.center,
                  child: SvgPicture.asset(
                      'assets/svg/mine/personal_info/back.svg',
                      width: 16,
                      height: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showSelectionDialog(BuildContext context, String title) async {
    if (title == '选择部门') {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7,
            child: TreeSelectionDialog(
              departments: [department],
              onSelected: (value) {
                setState(() {
                  selectedPositionsMap[title] = [value];
                });
              },
            ),
          );
        },
      );
    } else if (title == '选择岗位') {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7,
            child: PositionSelectionDialog(
              options: roles,
              onSelectedPositionsChanged: (selectedPositions) {
                setState(() {
                  selectedPositionsMap[title] = selectedPositions;
                });
              },
            ),
          );
        },
      );
    } else if (title == '选择审批人') {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return FractionallySizedBox(
            heightFactor: 0.7,
            child: SingleSelectionDialog(
              title: title,
              options: reviewers,
              showSearch: false,
              onSelectedOptionChanged: (selectedOption) {
                setState(() {
                  selectedPositionsMap[title] = [selectedOption];
                });
              },
            ),
          );
        },
      );
    } else if (title == '入职时间') {
      final config = CalendarDatePicker2WithActionButtonsConfig(
          calendarType: CalendarDatePicker2Type.single,
          selectedDayHighlightColor: AppTheme.colorBlue,
          daySplashColor: Colors.transparent,
          hideMonthPickerDividers: true,
          hideYearPickerDividers: true);
      List<DateTime?>? dates = await showCalendarDatePicker2Dialog(
          context: context,
          config: config,
          value: [DateTime.now()],
          dialogSize: Size(ScreenUtil().screenWidth - 64.w, 300.h));
      if (dates != null && dates.isNotEmpty) {
        setState(() {
          selectedPositionsMap[title] = [
            dates.first.toString().split(" ").first
          ];
        });
      }
    }
  }

  Widget _buildIntroductionSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '请完善以下个人信息',
            style: TextStyle(
              fontSize: 25.sp,
              color: const Color(0xFF222222),
            ).phBold,
          ),
          SizedBox(height: 5.h),
          Text(
            '请认真填写以下信息，填写后信息不可修改',
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF8B90A0),
            ).pfRegular,
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: const Divider(
        color: Color(0xFFE6E6E6),
        thickness: 0.5,
      ),
    );
  }

  Widget _buildAccountIdField() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('账号ID',
              style: AppTheme.getTextStyle(
                      baseSize: 17.sp, color: AppTheme.colorBlack2)
                  .pfSemiBold),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                oldModel?.guid ?? '',
                style: AppTheme.getTextStyle(
                  baseSize: 15.sp,
                  color: const Color(0xFFBABABA),
                ).pfRegular,
              ),
              GestureDetector(
                  onTap: () {
                    String accountId =
                        '${GlobalPreferences().userLoginModel?.guid}';
                    Clipboard.setData(ClipboardData(text: accountId));
                    ToastUtils.show('账号ID已复制到剪贴板');
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: 30,
                    child: SvgPicture.asset(
                      'assets/svg/mine/copy_icon.svg',
                      width: 15,
                      height: 15,
                    ),
                  )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditableField(String label, String initialValue, String hintText,
      TextEditingController c,
      {bool isEditable = false, bool must = false}) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (!isEditable) {
          ToastUtils.show('该项不可编辑');
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 32.5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (must)
                  Text(
                    '* ',
                    style: TextStyle(
                            color: const Color(0xFFE02020), fontSize: 17.sp)
                        .pfSemiBold,
                  ),
                Text(
                  label,
                  style: AppTheme.getTextStyle(
                          baseSize: 17.sp, color: AppTheme.colorBlack2)
                      .pfSemiBold,
                ),
              ],
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: c,
              maxLength: c == _mobileTextEditController
                  ? 11
                  : (c == _idNumberTextEditController ? 18 : null),
              focusNode: c == _nameTextEditController
                  ? _focusNode
                  : (c == _idNumberTextEditController
                      ? _idFocusNode
                      : _mobileNode),
              enabled: isEditable,
              cursorColor: AppTheme.primaryColor,
              style: TextStyle(
                      color: isEditable ? Color(0xFF606266) : Color(0xFFBABABA),
                      fontSize: 15.sp)
                  .pfRegular,
              decoration: InputDecoration(
                counter: const Offstage(),
                isCollapsed: true,
                border: InputBorder.none,
                hintText: hintText,
                hintStyle: TextStyle(color: Color(0xFFBABABA), fontSize: 15.sp)
                    .pfRegular,
              ),
              onEditingComplete: () {
                _nodesUnfocus();
              },
            ),
          ],
        ),
      ),
    );
  }

  List<int>? _getRoleId() {
    List<int>? ids = [];
    selectedPositionsMap['选择岗位']?.forEach((select) {
      roleList?.forEach((e) {
        e['roles'].forEach((r) {
          if (r['role']['name'] == select) {
            ids.add(r['id']);
          }
        });
      });
    });
    return ids;
  }

  void _submitForm() {
    if (_mobileTextEditController.text.isEmpty) {
      ToastUtils.show('手机号错误');
      return;
    }
    if (_nameTextEditController.text.isEmpty) {
      ToastUtils.show('请输入姓名');
      return;
    }
    if (_idNumberTextEditController.text.isEmpty ||
        !ValidatorUtils.validate(_idNumberTextEditController.text)) {
      ToastUtils.show('身份证号格式错误');
      return;
    }
    if (selectedPositionsMap['选择岗位']!.isEmpty) {
      ToastUtils.show('请选择岗位');
      return;
    }

    _submitRegister();
  }

  void _submitRegister() {
    EasyLoading.show();

    Map<String, dynamic>? params = {
      'mobile': _mobileTextEditController.text,
      'name': _nameTextEditController.text,
      'id_no': _idNumberTextEditController.text,
      'dep_role_ids': _getRoleId(),
      'join_at': '${selectedPositionsMap['入职时间']?.first} 00:00:00'
    };

    UserServiceProvider().oldCommitRegister(params).then((value) {
      EasyLoading.dismiss();
      if (value?.code == 0) {
        GlobalPreferences().oldUserInfo = null;
        Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (_) {
          return const Tabs();
        }), (route) => false);
      } else {
        EasyLoading.showError(value?.msg ?? '提交失败, 请联系管理员');
      }
    });
  }
}
