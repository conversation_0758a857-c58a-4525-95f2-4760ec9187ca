import 'package:npemployee/model/mine/user_login_model.dart';

abstract class LoginEvent {}

class CommitPhoneEvent extends LoginEvent {
  final String phoneNumber;

  CommitPhoneEvent(this.phoneNumber);
}

class CommitCodeEvent extends LoginEvent {
  final String codeNumber;

  CommitCodeEvent(this.codeNumber);
}

class LoginSuccessEvent extends LoginEvent {
  final bool isLogin;

  LoginSuccessEvent(this.isLogin);
}

class ProfileUpdateEvent extends LoginEvent {
  final UserLoginModel model;

  ProfileUpdateEvent(this.model);
}
