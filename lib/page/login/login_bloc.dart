import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:npemployee/page/login/login_event.dart';
import 'package:npemployee/page/login/login_state.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  LoginBloc() : super(LoginState().init()) {
    on<CommitPhoneEvent>(_commitPhoneEvent);
    on<CommitCodeEvent>(_commitCodeEvent);
    on<LoginSuccessEvent>(_loginSuccessEvent);
  }

  void _commitPhoneEvent(event, emit) {
    state.phoneNumber = event.phoneNumber;
    emit(state.clone());
  }

  void _commitCodeEvent(event, emit) {
    state.codeNumber = event.codeNumber;
    emit(state.clone());
  }

  void _loginSuccessEvent(event, emit) {
    state.isLogin = event.isLogin;
    emit(state.clone());
  }
}
