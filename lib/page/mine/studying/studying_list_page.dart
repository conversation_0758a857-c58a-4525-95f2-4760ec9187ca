import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/page/studying_no_data_page.dart';
import 'package:npemployee/model/mine/course_studying_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';

class StudyingListPage extends StatefulWidget {
  const StudyingListPage({super.key});

  @override
  State<StudyingListPage> createState() => _StudyingListPageState();
}

class _StudyingListPageState extends State<StudyingListPage> {
  List<CourseStudyingModel> capabilityUpgradingList = [];
  List<CourseStudyingModel> schoolTrainingList = [];
  int _tabIndex = 1;
  List<String> tagTabs = ['小新学院', '校区培训'];
  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  late PageController _pageController;

  late EasyRefreshController _refreshController;
  int page = 1;
  int size = 10;

  void _getLearningList({bool isRefresh = true}) {
    page = isRefresh ? 1 : page + 1;
    int? tag_id;
    if (_tabIndex == 1) {
      tag_id = 1; // 0-小新学院
    } else {
      tag_id = 30; //30-校区培训
    }
    UserServiceProvider().getWatchingList(
      tag_id: tag_id,
      page: page,
      size: size,
      cacheCallBack: (value) {
        // _formatLearningListData(value, true);
      },
      successCallBack: (value) {
        _formatLearningListData(value, false, isRefresh);
      },
      errorCallBack: (value) {
        EasyLoading.showError(value.msg);
        NavigatorUtils.pop(context);
      },
    );
  }

  _formatLearningListData(ResultData? value, bool isCache, bool isRefresh) {
    if (isRefresh) {
      _refreshController.finishRefresh();
      _refreshController.resetFooter();
      if (_tabIndex == 1) {
        capabilityUpgradingList.clear();
      } else {
        schoolTrainingList.clear();
      }
    } else {
      if ((value?.data ?? []).length < size) {
        _refreshController.finishLoad(IndicatorResult.noMore);
      } else {
        _refreshController.finishLoad(IndicatorResult.success);
      }
    }
    for (var e in value?.data ?? []) {
      if (_tabIndex == 1) {
        capabilityUpgradingList.add(CourseStudyingModel.fromJson(e));
      } else {
        schoolTrainingList.add(CourseStudyingModel.fromJson(e));
      }
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _refreshController = EasyRefreshController(
        controlFinishRefresh: true, controlFinishLoad: true);
    _pageController = PageController(initialPage: 0);
    _getLearningList();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        appBar: const CommonNav(title: '正在学习'),
        body: Column(
          children: [
            Container(
              width: ScreenUtil().screenWidth,
              color: Colors.white,
              child: Column(
                children: [
                  SizedBox(height: 12.h),
                  _tabView(),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            Expanded(
                child: EasyRefresh.builder(
                    controller: _refreshController,
                    onRefresh: () => _getLearningList(isRefresh: true),
                    onLoad: () => _getLearningList(isRefresh: false),
                    childBuilder: (_, p) {
                      return PageView(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _tabIndex = index + 1;
                          });
                          _getLearningList();
                        },
                        children: [
                          capabilityUpgradingList.isEmpty
                              ? StudyingNoDataPage(index: 1, physics: p)
                              : ListView.builder(
                                  physics: p,
                                  itemCount: capabilityUpgradingList.length,
                                  itemBuilder: (context, index) =>
                                      _itemBuilder(context, index)),
                          schoolTrainingList.isEmpty
                              ? StudyingNoDataPage(index: 2, physics: p)
                              : ListView.builder(
                                  physics: p,
                                  itemCount: schoolTrainingList.length,
                                  itemBuilder: (context, index) =>
                                      _itemBuilder(context, index))
                        ],
                      );
                    })),
          ],
        ));
  }

  Widget _tabView() {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Row(
          children: [
            ...tagTabs.map((e) {
              return Expanded(
                child: VideoTabItem(
                    title: e,
                    isSelect: _tabIndex == tagTabs.indexOf(e) + 1,
                    onTap: () {
                      setState(() {
                        _tabIndex = tagTabs.indexOf(e) + 1;
                      });
                      _pageController.animateToPage(
                        _tabIndex - 1,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.ease,
                      );
                      _getLearningList();
                    }),
              );
            }),
          ],
        ));
  }

  Widget _itemBuilder(c, index) {
    CourseStudyingModel model;
    if (_tabIndex == 1) {
      if (index >= capabilityUpgradingList.length) {
        return Container();
      }
      model = capabilityUpgradingList[index];
    } else {
      if (index >= schoolTrainingList.length) {
        return Container();
      }
      model = schoolTrainingList[index];
    }
    double studyDuration =
        (model.course_info.watch_duration / model.course_info.total_duration)
            .ceilToDouble();
    if (studyDuration == double.infinity) {
      studyDuration = 0;
    }

    return GestureDetector(
      onTap: () {
        if (model.course_info.course.course_type == 1) {
          //书籍
          NavigatorUtils.push(context, StudyRouter.capabilityDetail,
              arguments: {'courseId': model.course_id});
        } else {
          NavigatorUtils.push(context, StudyRouter.playDetail,
              arguments: {'course': model.course_info.course});
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              '上次学习：${DateTimeUtils.formatStudyingDate(model.updated_at)}',
              style: TextStyle(color: const Color(0xFF606266), fontSize: 12.sp)
                  .pfRegular,
            ),
          ),
          SizedBox(height: 10.h),
          Container(
            margin: EdgeInsets.only(bottom: 16.h, left: 16.w, right: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(16.r)),
            child: Row(
              children: [
                isLandscapeCache.containsKey(model.course_info.course.image)
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(10.r),
                        child: CachedNetworkImage(
                          imageUrl: model.course_info.course.image,
                          fit: BoxFit.cover,
                          height:
                              isLandscapeCache[model.course_info.course.image]!
                                  ? 64.h
                                  : 102.h,
                          width:
                              isLandscapeCache[model.course_info.course.image]!
                                  ? 119.w
                                  : 73.w,
                        ),
                      )
                    : FutureBuilder(
                        future: _isLandscape(model.course_info.course.image),
                        builder: (_, snapData) {
                          if (snapData.connectionState ==
                              ConnectionState.done) {
                            final isLandscape = snapData.data ?? true;
                            return ClipRRect(
                              borderRadius: BorderRadius.circular(10.r),
                              child: CachedNetworkImage(
                                imageUrl: model.course_info.course.image,
                                fit: BoxFit.cover,
                                height: isLandscape ? 64.h : 102.h,
                                width: isLandscape ? 119.w : 73.w,
                              ),
                            );
                          }
                          return Container();
                        }),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Expanded(
                            child: Text(
                              model.course_info.course.name,
                              style: TextStyle(
                                      color: const Color(0xFF323640),
                                      fontSize: 15.sp)
                                  .pfMedium,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 6.h),
                      Text(
                        ValidatorUtils.delHL(
                            model.course_info.course.introduction),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                                color: const Color(0xFF999999), fontSize: 12.sp)
                            .pfRegular,
                      ),
                      SizedBox(height: 6.h),
                      Row(
                        children: [
                          Text(
                              '${model.course_info.lesson_count}节课时 | 已学${studyDuration.toStringAsFixed(0)}%',
                              style: TextStyle(
                                      color: const Color(0xFF999999),
                                      fontSize: 12.sp)
                                  .pfRegular),
                        ],
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }
}
