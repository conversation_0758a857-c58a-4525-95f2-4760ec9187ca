import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/custom_dialog.dart';
import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';
import 'package:npemployee/model/knowledge_contest/paper_data_model.dart';
import 'package:npemployee/model/mine/mission_model.dart';
import 'package:npemployee/model/mine/sub_mission_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';

class TaskTranscriptPage extends StatefulWidget {
  final PaperContentModel paperContentModel;
  final PaperDataModel paperDataModel;
  final MissionListModel mission;
  const TaskTranscriptPage({
    super.key,
    required this.paperContentModel,
    required this.paperDataModel,
    required this.mission,
  });

  @override
  State<TaskTranscriptPage> createState() => _TaskTranscriptPageState();
}

class _TaskTranscriptPageState extends State<TaskTranscriptPage> {
  late PaperDataModel _paperData;
  List<SubMissionListModel> subMissions = [];

  bool get isPass =>
      int.parse(_paperData.my_score) >=
      int.parse(widget.paperContentModel.score_pass);

  bool get finishWatchTask {
    if (subMissions.isEmpty) {
      return false;
    }
    return !subMissions.any((subMission) => !subMission.finished);
  }

  void _getSubMissionList() {
    UserServiceProvider().getSubMissionList(
      widget.mission.missionUser.id,
      cacheCallBack: (data) {},
      successCallBack: (data) {
        _formatSubMissionData(data);
      },
      errorCallBack: (err) {
        EasyLoading.showError(err.msg);
      },
    );
  }

  void _formatSubMissionData(ResultData data) {
    subMissions.clear();
    for (var element in data.data ?? []) {
      subMissions.add(SubMissionListModel.fromJson(element));
    }
    int index = subMissions.indexWhere((e) => e.user_sub.last_sync_at == null);
    if (index != -1) {
      _getSubMissionList();
      return;
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    _paperData = widget.paperDataModel;
    _getSubMissionList();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          NavigatorUtils.popUntil(context, MineRouter.myTaskPage);
          return false; // 返回 false 阻止默认返回行为
        },
        child: Scaffold(
          backgroundColor: const Color(0xFFF7F8FD),
          body: Column(
            children: [
              Stack(
                children: [
                  isPass
                      ? Image.asset('assets/png/mine/task/task_exam_pass.png',
                          width: ScreenUtil().screenWidth)
                      : Image.asset('assets/png/mine/task/task_exam_fail.png',
                          width: ScreenUtil().screenWidth),
                  Positioned(
                      // left: 10.w,
                      top: ScreenUtil().statusBarHeight + 13.h,
                      child: IconButton(
                          onPressed: () {
                            NavigatorUtils.popUntil(
                                context, MineRouter.myTaskPage);
                          },
                          icon: const Icon(Icons.arrow_back_ios,
                              color: Colors.white, size: 18))),
                  Positioned(
                      left: 16.w,
                      bottom: 13.h,
                      child: Container(
                        width: 343.w,
                        height: 68.h,
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        alignment: Alignment.centerLeft,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15.r),
                        ),
                        child: Text(
                          _paperData.paper_name,
                          style: TextStyle(
                                  color: const Color(0xFF222222),
                                  fontSize: 18.sp)
                              .phMedium,
                        ),
                      ))
                ],
              ),
              SizedBox(height: 16.h),
              Expanded(
                  child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      padding: EdgeInsets.symmetric(
                          vertical: 18.h, horizontal: 24.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  Text(_paperData.my_score,
                                      style: TextStyle(
                                              color: const Color(0xFF222222),
                                              fontSize: 30.sp)
                                          .phBold),
                                  Text('/${_paperData.score_total}',
                                      style: TextStyle(
                                              color: const Color(0xFF898989),
                                              fontSize: 12.sp)
                                          .phBold)
                                ],
                              ),
                              SizedBox(height: 12.h),
                              Text('分数',
                                  style: TextStyle(
                                          color: const Color(0xFF999999),
                                          fontSize: 13.sp)
                                      .pfRegular),
                            ],
                          ),
                          Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  Text('${_paperData.right_total}',
                                      style: TextStyle(
                                              color: const Color(0xFF222222),
                                              fontSize: 30.sp)
                                          .phBold),
                                  Text('/${_paperData.question_total}',
                                      style: TextStyle(
                                              color: const Color(0xFF898989),
                                              fontSize: 12.sp)
                                          .phBold)
                                ],
                              ),
                              SizedBox(height: 12.h),
                              Text('答对题数量',
                                  style: TextStyle(
                                          color: const Color(0xFF999999),
                                          fontSize: 13.sp)
                                      .pfRegular),
                            ],
                          ),
                          Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  Text('${_paperData.rank_with_all}',
                                      style: TextStyle(
                                              color: const Color(0xFF222222),
                                              fontSize: 30.sp)
                                          .phBold),
                                  Text('/${_paperData.answer_total_with_all}',
                                      style: TextStyle(
                                              color: const Color(0xFF898989),
                                              fontSize: 12.sp)
                                          .phBold)
                                ],
                              ),
                              SizedBox(height: 12.h),
                              Text('成绩排名',
                                  style: TextStyle(
                                          color: const Color(0xFF999999),
                                          fontSize: 13.sp)
                                      .pfRegular),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      padding: EdgeInsets.symmetric(
                          vertical: 18.h, horizontal: 24.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text('答题卡',
                                  style: TextStyle(
                                          color: const Color(0xFF323640),
                                          fontSize: 18.sp)
                                      .pfMedium),
                              const Expanded(child: SizedBox()),
                              _answerCardDesView(const Color(0xFFF3F4F6), '未答'),
                              SizedBox(width: 12.w),
                              _answerCardDesView(const Color(0xFF36D176), '正确'),
                              SizedBox(width: 12.w),
                              _partView(12),
                              SizedBox(width: 3.w),
                              Text('半对',
                                  style: TextStyle(
                                          color: const Color(0xFF8B93A6),
                                          fontSize: 14.sp)
                                      .pfRegular),
                              SizedBox(width: 12.w),
                              _answerCardDesView(const Color(0xFFFE5A59), '错误'),
                            ],
                          ),
                          SizedBox(height: 28.h),
                          // Text('常识判断',
                          //     style: TextStyle(
                          //         color: const Color(0xFF9BA0AD), fontSize: 13.sp)),
                          // SizedBox(height: 16.h),
                          SingleChildScrollView(
                            child: Wrap(
                              spacing: 28.w,
                              runSpacing: 24.h,
                              children: [
                                ...(_paperData.answer_sheet).map((e) {
                                  return GestureDetector(
                                    onTap: () {
                                      NavigatorUtils.push(context,
                                          MineRouter.taskAnswerAnalysisPage,
                                          arguments: {
                                            'questions':
                                                _paperData.answer_sheet,
                                            'index': e.question_number
                                          });
                                    },
                                    child: e.answer_status == AnswerStatus.PART
                                        ? _partView(40, num: e.question_number)
                                        : Container(
                                            alignment: Alignment.center,
                                            width: 40,
                                            height: 40,
                                            decoration: BoxDecoration(
                                                color: e.answer_status ==
                                                        AnswerStatus.NOT
                                                    ? const Color(0xFFF3F4F6)
                                                    : (e.answer_status ==
                                                            AnswerStatus.RIGHT
                                                        ? const Color(
                                                            0xFF36D176)
                                                        : const Color(
                                                            0xFFFE5A59)),
                                                borderRadius:
                                                    BorderRadius.circular(20)),
                                            child: Text('${e.question_number}',
                                                style: TextStyle(
                                                        color:
                                                            e.answer_status ==
                                                                    AnswerStatus
                                                                        .NOT
                                                                ? const Color(
                                                                    0xFF333333)
                                                                : Colors.white,
                                                        fontSize: 18.sp)
                                                    .pfSemiBold),
                                          ),
                                  );
                                }),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.h),
                  ],
                ),
              ))
            ],
          ),
          bottomNavigationBar: Container(
            width: ScreenUtil().screenWidth,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            height: 98.h,
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    NavigatorUtils.push(
                        context, MineRouter.taskAnswerAnalysisPage,
                        arguments: {'questions': _paperData.answer_sheet});
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding:
                        EdgeInsets.symmetric(vertical: 14.h, horizontal: 46.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        color: Colors.transparent,
                        border: Border.all(color: AppTheme.colorBlue)),
                    child: Text('查看解析',
                        style: TextStyle(
                                color: const Color(0xFF0054FF), fontSize: 16.sp)
                            .pfRegular),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    if (isPass) {
                      NavigatorUtils.push(context, MineRouter.taskScoreRankPage,
                          arguments: {
                            'model': widget.paperDataModel,
                            'mission': widget.mission
                          });
                    } else {
                      if (finishWatchTask) {
                        NavigatorUtils.popUntil(
                            context, MineRouter.readyExamPage);
                      } else {
                        showDialog(
                            context: context,
                            builder: (_) {
                              return CustomDialog(
                                title: '提示',
                                content: '您已超过最大补考次数，请重新学习',
                                onCancel: () {
                                  NavigatorUtils.pop(_);
                                },
                                onConfirm: () {
                                  NavigatorUtils.popUntil(
                                      context, MineRouter.taskDesPage);
                                },
                              );
                            });
                      }
                    }
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding:
                        EdgeInsets.symmetric(vertical: 14.h, horizontal: 46.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        color: const Color(0xFF0054FF)),
                    child: Text(isPass ? '查看排名' : '重新考试',
                        style: TextStyle(
                                color: const Color(0xFFFFFFFF), fontSize: 16.sp)
                            .pfRegular),
                  ),
                )
              ],
            ),
          ),
        ));
  }

  Widget _answerCardDesView(Color color, String title) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: color,
          ),
        ),
        SizedBox(width: 3.w),
        Text(title,
            style: TextStyle(color: const Color(0xFF8B93A6), fontSize: 14.sp)
                .pfRegular),
      ],
    );
  }

  Widget _partView(double size, {int? num}) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 半圆绿色覆盖
        ClipOval(
          child: SizedBox(
            width: size,
            height: size,
            child: Transform.rotate(
              angle: 45 * 3.141592653589793 / 180, // 顺时针旋转120度，将角度转换为弧度
              child: ClipOval(
                child: SizedBox(
                  width: size,
                  height: size,
                  child: Stack(
                    children: [
                      // 白色部分
                      Container(
                        color: const Color(0xFFF3F4F6),
                      ),
                      // 左半部分为绿色
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          width: size / 2, // 左半部分宽度
                          color: const Color(0xFF36D176),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        // 圆心数字
        if (num != null)
          Text(
            "$num",
            style: TextStyle(fontSize: 18.sp, color: const Color(0xFF333333))
                .pfSemiBold,
          ),
      ],
    );
  }
}
