import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/task_finish_dialog.dart';
import 'package:npemployee/common/widget/submit_button.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';
import 'package:npemployee/model/knowledge_contest/paper_data_model.dart';
import 'package:npemployee/model/mine/mission_model.dart';
import 'package:npemployee/model/mine/sub_mission_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';

//任务说明
class MyTaskDesPage extends StatefulWidget {
  final MissionListModel model;
  final int mission_user_id;
  const MyTaskDesPage(
      {super.key, required this.mission_user_id, required this.model});

  @override
  State<MyTaskDesPage> createState() => _MyTaskDesPageState();
}

class _MyTaskDesPageState extends State<MyTaskDesPage> {
  List<SubMissionListModel> subMissions = [];

  bool displayfinishDialog = true; //从其他页面返回时，不弹出提示框

  bool get finishWatchTask {
    if (subMissions.isEmpty) {
      return false;
    }
    return !subMissions.any((subMission) => !subMission.finished);
  }

  void _getSubMissionList() {
    UserServiceProvider().getSubMissionList(
      widget.mission_user_id,
      cacheCallBack: (data) {},
      successCallBack: (data) {
        EasyLoading.dismiss();
        _formatSubMissionData(data);
      },
      errorCallBack: (err) {
        EasyLoading.dismiss();
        EasyLoading.showError(err.msg);
      },
    );
  }

  void _formatSubMissionData(ResultData data) {
    subMissions.clear();
    for (var element in data.data ?? []) {
      subMissions.add(SubMissionListModel.fromJson(element));
    }
    int index = subMissions.indexWhere((e) => e.user_sub.last_sync_at == null);
    if (index != -1) {
      _getSubMissionList();
      return;
    }

    // int learnedTime = subMissions.fold(0, (sum, item) => sum + item.learnTime);
    // int totalTime = subMissions.fold(
    //     0, (sum, item) => sum + item.mission_sub.required_seconds);
    var finishCount = 0;
    for (var mission in subMissions) {
      if (mission.finished) {
        finishCount++;
      }
    }
    if (mounted) {
      setState(() {});
    }
    if (finishCount >= subMissions.length &&
        !widget.model.finished &&
        displayfinishDialog) {
      showDialog(
          context: context,
          builder: (_) {
            return TaskFinishDialog(
              hasExam: widget.model.hasExam,
              onTap: () {
                NavigatorUtils.pop(_);
                NavigatorUtils.push(context, MineRouter.studyingListPage)
                    .then((v) {
                  displayfinishDialog = false;
                  _getSubMissionList();
                });
              },
            );
          });
    }
  }

  @override
  void initState() {
    super.initState();
    EasyLoading.show();
    _getSubMissionList();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                  Color(0xFF93C1FF),
                  Color(0xFFF2F8FF),
                  Color(0xFFE3EFFF)
                ])),
          ),
          Positioned(
            top: ScreenUtil().statusBarHeight,
            right: 0,
            child: Image.asset('assets/png/mine/task/task_top_bac.png',
                width: 156.w, height: 161.h),
          ),
          SingleChildScrollView(
            child: Container(
              margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 28.h),
              padding: EdgeInsets.only(left: 23.w, right: 9.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 10.h),
                  Image.asset('assets/png/mine/task/task_des.png',
                      width: 179.w, height: 67.h),
                  _taskDesView(),
                  SizedBox(height: 16.h),
                  _studyTaskView(),
                  SizedBox(
                      height: (widget.model.hasExam ? 88.h : 0) +
                          ScreenUtil().bottomBarHeight)
                ],
              ),
            ),
          ),
          if (widget.model.hasExam)
            Positioned(
              bottom: 0,
              child: Container(
                width: ScreenUtil().screenWidth,
                height: 88.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(18.r),
                      topRight: Radius.circular(18.r)),
                ),
                child: SubmitButton(
                    onTap: () {
                      if (widget.model.finished) {
                        EasyLoading.show();
                        UserServiceProvider().getPaperData(
                            widget.model.mission.paper_id!,
                            missionId: widget.model.missionUser.id,
                            cacheCallBack: (data) {}, successCallBack: (data) {
                          EasyLoading.dismiss();
                          PaperDataModel pData =
                              PaperDataModel.fromJson(data.data);
                          NavigatorUtils.push(
                              context, MineRouter.taskAnswerAnalysisPage,
                              arguments: {'questions': pData.answer_sheet});
                        }, errorCallBack: (err) {
                          EasyLoading.dismiss();
                          EasyLoading.showError(err.msg);
                        });
                      } else {
                        NavigatorUtils.push(context, MineRouter.readyExamPage,
                            arguments: {'model': widget.model}).then((v) {
                          displayfinishDialog = false;
                          _getSubMissionList();
                        });
                      }
                    },
                    name: widget.model.finished
                        ? '查看解析'
                        : (finishWatchTask ? '立即测试' : '完成学习任务，参与考试'),
                    enable: widget.model.finished ? true : finishWatchTask),
              ),
            ),
          const TeamManagerNav(title: ''),
        ],
      ),
    );
  }

  Widget _taskDesView() {
    return Column(
      children: [
        Stack(
          children: [
            Image.asset('assets/png/mine/task/task_des_title_bac.png',
                width: ScreenUtil().screenWidth - 32.w),
            Positioned(
              top: 7.h,
              left: 18.w,
              child: Text(
                '任务信息',
                style:
                    TextStyle(color: const Color(0xFF7A460C), fontSize: 18.sp)
                        .phHeavy,
              ),
            ),
          ],
        ),
        Container(
          width: 343.w,
          padding: EdgeInsets.fromLTRB(18.w, 0, 18.w, 16.h),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                  colors: [Color(0xFFFFFFFF), Color(0xFFFAFEFF)]),
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _titleNameView('任务名称', widget.model.mission.name,
                  hasRight: widget.model.finished),
              SizedBox(height: 16.h),
              _titleNameView('有无考试', widget.model.hasExam ? '有' : '无'),
              SizedBox(height: 16.h),
              _titleNameView('完成时间', '${widget.model.deadLineTime}前完成'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _titleNameView(String title, String content,
      {bool? hasRight = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$title: ',
          style: TextStyle(color: const Color(0xFF8B90A0), fontSize: 15.sp)
              .pfRegular,
        ),
        Expanded(
          child: Text(
            content,
            style: TextStyle(color: const Color(0xFF222222), fontSize: 15.sp)
                .pfMedium,
          ),
        ),
        if (hasRight!)
          Image.asset('assets/png/mine/task/task_finish.png',
              width: 48.w, height: 24.h),
      ],
    );
  }

  Widget _studyTaskView() {
    return Column(
      children: [
        Stack(
          children: [
            Image.asset('assets/png/mine/task/task_des_title_bac.png'),
            Positioned(
              top: 7.h,
              left: 18.w,
              child: Text(
                '学习任务',
                style:
                    TextStyle(color: const Color(0xFF7A460C), fontSize: 18.sp)
                        .phHeavy,
              ),
            ),
          ],
        ),
        Container(
          width: 343.w,
          padding: EdgeInsets.fromLTRB(18.w, 0, 18.w, 16.h),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                  colors: [Color(0xFFFFFFFF), Color(0xFFFAFEFF)]),
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...subMissions.map((e) => _studyTaskItemView(e)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _studyTaskItemView(SubMissionListModel subMissionModel) {
    return Column(
      children: [
        // SizedBox(height: 20.h),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('任务${subMissions.indexOf(subMissionModel) + 1}: ',
                style: TextStyle(color: Color(0xFF000000), fontSize: 14.sp)
                    .pfMedium),
            Expanded(
                child: Text(subMissionModel.mission_sub.desc,
                    style: TextStyle(color: Color(0xFF000000), fontSize: 14.sp)
                        .pfMedium)),
          ],
        ),
        SizedBox(height: 8.h),
        StudyTaskItemView(
          courseId: subMissionModel.mission_sub.course_id!,
          model: subMissionModel,
          onTap: (CourseListModel m) {
            Map argumentMap = {};
            if (subMissionModel.mission_sub.lesson_id != null) {
              argumentMap['lessonId'] = subMissionModel.mission_sub.lesson_id;
            }
            if (subMissionModel.mission_sub.chapter_id != null) {
              argumentMap['chapterId'] = subMissionModel.mission_sub.chapter_id;
            }
            if (m.course_type == 1) {
              argumentMap['courseId'] = m.id;
              NavigatorUtils.push(context, StudyRouter.capabilityDetail,
                      arguments: argumentMap)
                  .then((v) {
                _getSubMissionList();
              });
            } else {
              argumentMap['course'] = m;
              NavigatorUtils.push(context, StudyRouter.playDetail,
                      arguments: argumentMap)
                  .then((v) {
                _getSubMissionList();
              });
            }
          },
        ),
      ],
    );
  }
}

class StudyTaskItemView extends StatefulWidget {
  final int courseId;
  final SubMissionListModel model;
  final Function(CourseListModel) onTap;
  const StudyTaskItemView({
    super.key,
    required this.courseId,
    required this.model,
    required this.onTap,
  });

  @override
  State<StudyTaskItemView> createState() => _StudyTaskItemViewState();
}

class _StudyTaskItemViewState extends State<StudyTaskItemView> {
  CourseListModel? courseListModel;
  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  void _getCourseDetail() {
    UserServiceProvider().getCourseDetail(
      widget.courseId,
      cacheCallBack: (data) {
        _formatCourseDetailData(data, isCache: true);
      },
      successCallBack: (data) {
        _formatCourseDetailData(data);
      },
      errorCallBack: (err) {
        debugPrint('task des get course detail error: ${err.msg}');
      },
    );
  }

  void _formatCourseDetailData(ResultData data, {bool isCache = false}) {
    courseListModel = CourseListModel.formJson(data.data);

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getCourseDetail();
  }

  @override
  void dispose() {
    courseListModel = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return courseListModel == null
        ? const SizedBox()
        : GestureDetector(
            onTap: () => widget.onTap(courseListModel!),
            child: Container(
                width: 305.w,
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(13.r),
                    border: Border.all(
                        color: const Color(0xFFDCDCDC).withOpacity(0.5),
                        width: 0.5)),
                child: Column(
                  children: [
                    IntrinsicHeight(
                      child: Row(
                        children: [
                          isLandscapeCache.containsKey(courseListModel!.image)
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(10.r),
                                  child: CachedNetworkImage(
                                    imageUrl: courseListModel!.image,
                                    fit: BoxFit.cover,
                                    height: isLandscapeCache[
                                            courseListModel!.image]!
                                        ? 64.h
                                        : 102.h,
                                    width: isLandscapeCache[
                                            courseListModel!.image]!
                                        ? 119.w
                                        : 73.w,
                                  ),
                                )
                              : FutureBuilder(
                                  future: _isLandscape(courseListModel!.image),
                                  builder: (imgCxt, snapData) {
                                    if (snapData.connectionState ==
                                        ConnectionState.done) {
                                      final isLandscape = snapData.data ?? true;
                                      return ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(10.r),
                                        child: CachedNetworkImage(
                                          imageUrl: courseListModel!.image,
                                          fit: BoxFit.cover,
                                          height: isLandscape ? 64.h : 102.h,
                                          width: isLandscape ? 119.w : 73.w,
                                        ),
                                      );
                                    }
                                    return Container();
                                  }),
                          SizedBox(width: 10.w),
                          Expanded(
                              child: Padding(
                            padding: EdgeInsets.symmetric(vertical: 8.h),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  courseListModel!.name,
                                  maxLines: 2,
                                  style: TextStyle(
                                          color: const Color(0xFF323640),
                                          fontSize: 14.sp,
                                          overflow: TextOverflow.ellipsis)
                                      .pfSemiBold,
                                ),
                                Text(
                                  '${GlobalPreferences().userInfo?.user.name}  |  已观看${widget.model.watchedPercent}%',
                                  style: TextStyle(
                                          color: const Color(0xFF8B90A0),
                                          fontSize: 11.sp)
                                      .pfMedium,
                                ),
                              ],
                            ),
                          )),
                        ],
                      ),
                    ),
                    SizedBox(height: 8.h),
                    LearningProgressBar(
                        currentMinutes: widget.model.learnTimeMinutes,
                        totalMinutes: widget.model.totalMinutes),
                  ],
                )),
          );
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(const ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }
}

class LearningProgressBar extends StatelessWidget {
  final String currentMinutes;
  final String totalMinutes;

  const LearningProgressBar({
    Key? key,
    required this.currentMinutes,
    required this.totalMinutes,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double progress =
        double.parse(currentMinutes) / double.parse(totalMinutes);

    return Column(
      children: [
        Row(
          children: [
            // Text(
            //   '学习时长: ',
            //   style: TextStyle(color: const Color(0xFF8B90A0), fontSize: 12.sp)
            //       .pfRegular,
            // ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Stack(
                    children: [
                      Container(
                        height: 6.h,
                        decoration: BoxDecoration(
                          color: const Color(0xFFDAECFF),
                          borderRadius: BorderRadius.circular(3.r),
                        ),
                      ),
                      LayoutBuilder(
                        builder: (context, constraints) {
                          return Container(
                            height: 6.h,
                            width: constraints.maxWidth * progress,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(colors: [
                                Color(0xFF87C8FF),
                                Color(0xFF014EFF)
                              ]),
                              borderRadius: BorderRadius.circular(3.r),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(width: 10.w),
            Row(
              children: [
                Text(
                  currentMinutes,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppTheme.colorBlue,
                  ).pfSemiBold,
                ),
                Text(
                  '/$totalMinutes分钟',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF8B90A0),
                  ).pfMedium,
                ),
              ],
            )
          ],
        ),
      ],
    );
  }
}
