import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/study/depart_rank_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/top_tab_item.dart';

class TaskRankPage extends StatefulWidget {
  final int missionId;
  const TaskRankPage({super.key, required this.missionId});

  @override
  State<TaskRankPage> createState() => _TaskRankPageState();
}

class _TaskRankPageState extends State<TaskRankPage> {
  int topTabIndex = 1; //1-个人榜 2-部门榜
  List departments = []; //部门列表
  List<Map> rankMenus = [
    {'name': '累计学习榜', 'orderByTotal': true},
    {'name': '今日学习榜', 'orderByTotal': false},
  ];
  List<MenuItemButton> get dropRankItems =>
      rankMenus.map((e) => _menuRankItem(e)).toList();
  bool dropRankOpen = false;
  late Map dropRankValue;
  List<MenuItemButton> get dropItems =>
      departments.map((e) => _menuItem(e)).toList();
  bool dropIsOpen = false;
  Map? dropdownValue;
  List<TaskDepartRankModel> departRankList = [];
  Map? courseRankMap;

  final EasyRefreshController _refreshController = EasyRefreshController();

  _getAllData() {
    _getData();
  }

//获取部门列表
  void _getData() async {
    UserServiceProvider().getMissionDepartments(widget.missionId,
        cacheCallBack: (data) {
      _formatDepartmentData(data, true);
    }, successCallBack: (data) {
      if (data.code != 0) {
        EasyLoading.showError(data.msg);
        return;
      }
      _formatDepartmentData(data, false);
    }, errorCallBack: (err) {
      EasyLoading.showError(err.msg);
    });
  }

  void _formatDepartmentData(ResultData data, bool isCache) {
    departments.clear();
    List datas = data.data;
    datas.insert(0, {'name': '各部门', 'id': null});
    departments = datas;

    if (!isCache) {
      if (mounted) {
        setState(() {});
        if (topTabIndex == 1) {
          _getCourseRankList();
        } else {
          _getDepartRankList();
        }
      }
    }
  }

//获取课程排名列表
  void _getCourseRankList() async {
    Map selectDepart =
        departments.firstWhere((e) => e['id'] == dropdownValue?['id']);
    bool orderByTotal = rankMenus
        .firstWhere((e) => e['name'] == dropRankValue['name'])['orderByTotal'];

    UserServiceProvider().getMissionCourseRank(widget.missionId,
        departmentId: selectDepart['id'],
        orderByTotal: orderByTotal, cacheCallBack: (value) {
      _formatCourseRankData(value, true);
    }, successCallBack: (value) {
      _formatCourseRankData(value, false);
    }, errorCallBack: (value) {});
  }

  void _formatCourseRankData(ResultData? value, bool isCache) {
    courseRankMap = value?.data;
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

//获取部门排名列表
  void _getDepartRankList() async {
    UserServiceProvider().getMissionDepartmentsRank(widget.missionId,
        cacheCallBack: (value) {
      _formatDepartRankData(value, true);
    }, successCallBack: (value) {
      _formatDepartRankData(value, false);
    }, errorCallBack: (value) {});
  }

  _formatDepartRankData(ResultData? value, bool isCache) {
    departRankList.clear();
    for (Map element in value?.data) {
      departRankList.add(TaskDepartRankModel.fromJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    dropRankValue = rankMenus.first;
    dropdownValue = {'name': '各部门', 'id': null};
    _getAllData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '任务榜单'),
      body: Column(
        children: [
          SizedBox(height: 8.5.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 22.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TopTabItem(
                    onTap: () {
                      setState(() {
                        topTabIndex = 1;
                      });
                      _getAllData();
                    },
                    isSelect: topTabIndex == 1,
                    svgPath: topTabIndex == 1
                        ? 'assets/png/study/person_check.png'
                        : 'assets/png/study/person_uncheck.png'),
                TopTabItem(
                  onTap: () {
                    setState(() {
                      topTabIndex = 2;
                    });
                    _getAllData();
                  },
                  isSelect: topTabIndex == 2,
                  svgPath: topTabIndex == 2
                      ? 'assets/png/study/depart_check.png'
                      : 'assets/png/study/depart_uncheck.png',
                ),
              ],
            ),
          ),
          SizedBox(height: 9.h),
          if (topTabIndex == 1) _dropViews(),
          SizedBox(height: 17.h),
          Expanded(
              child: MediaQuery.removePadding(
                  context: context,
                  removeTop: true,
                  child: EasyRefresh.builder(
                    controller: _refreshController,
                    onRefresh: () => _getAllData(),
                    childBuilder: (_, pyhic) => (topTabIndex == 1 &&
                                courseRankMap == null) ||
                            (topTabIndex == 2 && departRankList.isEmpty)
                        ? NoDataPage(physics: pyhic)
                        : ListView.builder(
                            physics: pyhic,
                            itemCount: topTabIndex == 1
                                ? courseRankMap!['list'].length + 1
                                : departRankList.length + 1,
                            itemBuilder: (_, index) {
                              if (topTabIndex == 1) {
                                if (index == 0) {
                                  return TaskPersonListMeItem(
                                      item: courseRankMap?['me']);
                                } else {
                                  return TaskPersonListItem(
                                      item: courseRankMap?['list'][index - 1],
                                      index: index);
                                }
                              } else {
                                if (index == 0) {
                                  return const TaskDepartmentListFirstItem();
                                } else {
                                  TaskDepartRankModel departmentItem =
                                      departRankList[index - 1];
                                  return TaskDepartmentListItem(
                                      index: index, item: departmentItem);
                                }
                              }
                            }),
                  ))),
        ],
      ),
    );
  }

  Widget _dropViews() {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(horizontal: 22.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MenuAnchor(
            style: MenuStyle(
              visualDensity: VisualDensity.compact,
              backgroundColor: const WidgetStatePropertyAll(Colors.white),
              shape: WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                  side: const BorderSide(color: Color(0xFFDADADA), width: 0.5),
                ),
              ),
              elevation: const WidgetStatePropertyAll(0),
            ),
            builder: (BuildContext context, MenuController controller,
                Widget? child) {
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  controller.isOpen ? controller.close() : controller.open();
                },
                child: Container(
                  constraints: BoxConstraints(maxWidth: 75.w),
                  child: RichText(
                    text: TextSpan(
                        text: dropdownValue?['name'] ?? '',
                        style: TextStyle(
                                color: const Color(0xFF000000), fontSize: 12.sp)
                            .pfMedium,
                        children: [
                          WidgetSpan(
                            alignment: PlaceholderAlignment.middle,
                            child: Image.asset(
                              dropIsOpen
                                  ? 'assets/png/xiaoxin/drop_up.png'
                                  : 'assets/png/xiaoxin/drop_down.png',
                              width: 15.w,
                              height: 10.h,
                            ),
                          ),
                        ]),
                  ),
                ),
              );
            },
            menuChildren: dropItems,
            onClose: () {
              setState(() {
                dropIsOpen = false;
              });
            },
            onOpen: () {
              setState(() {
                dropIsOpen = true;
              });
            },
          ),
          SizedBox(
            width: 85.w,
            child: MenuAnchor(
              style: MenuStyle(
                visualDensity: VisualDensity.compact,
                backgroundColor: const WidgetStatePropertyAll(Colors.white),
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                    side:
                        const BorderSide(color: Color(0xFFDADADA), width: 0.5),
                  ),
                ),
                elevation: const WidgetStatePropertyAll(0),
              ),
              builder: (BuildContext context, MenuController controller,
                  Widget? child) {
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    controller.isOpen ? controller.close() : controller.open();
                  },
                  child: Container(
                    constraints: BoxConstraints(maxWidth: 75.w),
                    child: RichText(
                      text: TextSpan(
                          text: dropRankValue['name'],
                          style: TextStyle(
                                  color: const Color(0xFF000000),
                                  fontSize: 12.sp)
                              .pfMedium,
                          children: [
                            WidgetSpan(
                              alignment: PlaceholderAlignment.middle,
                              child: Image.asset(
                                dropRankOpen
                                    ? 'assets/png/xiaoxin/drop_up.png'
                                    : 'assets/png/xiaoxin/drop_down.png',
                                width: 15.w,
                                height: 10.h,
                              ),
                            ),
                          ]),
                    ),
                  ),
                );
              },
              menuChildren: dropRankItems,
              onClose: () {
                setState(() {
                  dropRankOpen = false;
                });
              },
              onOpen: () {
                setState(() {
                  dropRankOpen = true;
                });
              },
            ),
          )
        ],
      ),
    );
  }

  MenuItemButton _menuRankItem(Map e) {
    return MenuItemButton(
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 7.5.w, vertical: 8.h),
        constraints: BoxConstraints(maxWidth: 150.w),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color: rankMenus.indexOf(e) == rankMenus.length - 1
                        ? Colors.transparent
                        : const Color(0xFFE6E6E6).withOpacity(0.5)))),
        child: Text(
          e['name'],
          style: TextStyle(
                  color: dropRankValue == e
                      ? AppTheme.colorBlue
                      : const Color(0xFF000000),
                  fontSize: 12.sp)
              .pfMedium,
        ),
      ),
      onPressed: () {
        setState(() {
          dropRankValue = e;
        });
        _getAllData();
      },
    );
  }

  MenuItemButton _menuItem(Map e) {
    return MenuItemButton(
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 7.5.w, vertical: 8.h),
        constraints: BoxConstraints(maxWidth: 150.w),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color: departments.indexOf(e) == departments.length - 1
                        ? Colors.transparent
                        : const Color(0xFFE6E6E6).withOpacity(0.5)))),
        child: Text(
          e['name'],
          style: TextStyle(
                  color: dropdownValue?['id'] == e['id']
                      ? AppTheme.colorBlue
                      : const Color(0xFF000000),
                  fontSize: 12.sp)
              .pfMedium,
        ),
      ),
      onPressed: () {
        setState(() {
          dropdownValue = e;
        });
        _getAllData();
      },
    );
  }
}

// Start of Selection
class TaskDepartmentListItem extends StatelessWidget {
  final int index;
  final TaskDepartRankModel item;
  const TaskDepartmentListItem(
      {super.key, required this.item, required this.index});

  String formatDuration(int duration) {
    double hours = duration / 3600;
    String formattedDuration = hours.toStringAsFixed(2);
    if (formattedDuration.endsWith(".00")) {
      formattedDuration = hours.toStringAsFixed(0);
    }
    return formattedDuration;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
      decoration: const BoxDecoration(
          border: Border(
        bottom:
            BorderSide(color: Color.fromRGBO(230, 230, 230, 0.5), width: 0.5),
      )),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 60.w,
            child: Align(
              alignment: Alignment.center,
              child: Text(
                '$index',
                style: TextStyle(color: _rankingColor(index), fontSize: 16.sp)
                    .phHeavy,
              ),
            ),
          ),
          SizedBox(
            width: 110.w,
            child: Align(
              alignment: Alignment.center,
              child: Text(item.department,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style:
                      TextStyle(color: const Color(0xFF232323), fontSize: 14.sp)
                          .pfRegular),
            ),
          ),
          SizedBox(
            width: 100.w,
            child: Align(
              alignment: Alignment.center,
              child: Text('${item.done}/${item.total}',
                  style:
                      TextStyle(color: const Color(0xFF232323), fontSize: 14.sp)
                          .pfRegular),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.center,
              child: Text('${item.done_percent}%',
                  style:
                      TextStyle(color: const Color(0xFF232323), fontSize: 14.sp)
                          .pfRegular),
            ),
          ),
        ],
      ),
    );
  }

  Color _rankingColor(int rank) {
    if (rank == 1) {
      return const Color(0xFFFF4713);
    } else if (rank == 2) {
      return const Color(0xFFFDA052);
    } else if (rank == 3) {
      return const Color(0xFF6E9DFF);
    } else {
      return const Color(0xFFBABABA);
    }
  }
}

class TaskDepartmentListFirstItem extends StatelessWidget {
  const TaskDepartmentListFirstItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: ScreenUtil().screenWidth,
      height: 40.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
          color: const Color(0xFFE2EEFB),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r), topRight: Radius.circular(16.r))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            width: 60.w,
            child: Align(
              alignment: Alignment.center,
              child: Text('排名', style: _titleStyle()),
            ),
          ),
          SizedBox(
            width: 110.w,
            child: Align(
              alignment: Alignment.center,
              child: Text('部门', style: _titleStyle()),
            ),
          ),
          SizedBox(
            width: 100.w,
            child: Align(
              alignment: Alignment.center,
              child: Text('学习人数', style: _titleStyle()),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.center,
              child: Text('完成率', style: _titleStyle()),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle _titleStyle() {
    return TextStyle(color: const Color(0xFF808080), fontSize: 13.sp).pfRegular;
  }
}

class TaskPersonListItem extends StatelessWidget {
  final Map? item;
  final int index;

  const TaskPersonListItem({
    super.key,
    required this.item,
    required this.index,
  });

  String formatDuration(int duration) {
    double hours = duration / 3600;
    String formattedDuration = hours.toStringAsFixed(2);
    if (formattedDuration.endsWith(".00")) {
      formattedDuration = hours.toStringAsFixed(0);
    }
    return formattedDuration;
  }

  String get departName {
    Map edges = item?['user']['User']['edges'];
    if (edges.isEmpty) {
      return '';
    }
    List roles = edges['department_roles'];
    List dNames = [];
    for (var e in roles) {
      if (!dNames.contains(e['edges']['department']['name'])) {
        dNames.add(e['edges']['department']['name']);
      }
    }
    String departmentName = dNames.join(',');
    return departmentName;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        padding: EdgeInsets.symmetric(vertical: 16.h),
        decoration: const BoxDecoration(
            border: Border(
                bottom: BorderSide(color: Color(0xFFE6E6E6), width: 0.5))),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                if (index == 1)
                  Image.asset('assets/png/study/person_first.png',
                      width: 40, height: 40),
                if (index == 2)
                  Image.asset('assets/png/study/person_second.png',
                      width: 40, height: 40),
                if (index == 3)
                  Image.asset('assets/png/study/person_third.png',
                      width: 40, height: 40),
                if (index >= 4)
                  Container(
                    width: 40,
                    height: 40,
                    alignment: Alignment.center,
                    child: Text(
                      '$index',
                      style: TextStyle(
                              color: const Color(0xFF9E9E9E), fontSize: 20.sp)
                          .phBold,
                    ),
                  ),
                SizedBox(width: 12.w),
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl: ValidatorUtils.testImageUrl, // item.avatar
                    width: 46,
                    height: 46,
                  ),
                ),
                SizedBox(width: 12.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item?['user']['User']['name'],
                      style: TextStyle(
                              color: const Color(0xFF000000), fontSize: 16.sp)
                          .pfMedium,
                    ),
                    SizedBox(
                      width: 100.w,
                      child: Text(
                        departName,
                        style: TextStyle(
                                color: const Color(0xFF464B59),
                                fontSize: 12.sp,
                                overflow: TextOverflow.ellipsis)
                            .pfRegular,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Text(
                      '累计学习',
                      style: TextStyle(
                              color: const Color(0xFF464B59), fontSize: 14.sp)
                          .pfRegular,
                    ),
                    Text(
                      ' ${formatDuration(item?['total_duration'])} ',
                      style: TextStyle(
                              color: const Color(0xFFFE5D30), fontSize: 16.sp)
                          .phBold,
                    ),
                    Text(
                      '小时',
                      style: TextStyle(
                              color: const Color(0xFF7B7F8A), fontSize: 12.sp)
                          .pfMedium,
                    )
                  ],
                ),
                Row(
                  children: [
                    Text(
                      '今日学习',
                      style: TextStyle(
                          color: const Color(0xFF464B59), fontSize: 14.sp),
                    ),
                    Text(
                      ' ${formatDuration(item?['today_duration'])} ',
                      style: TextStyle(
                          color: const Color(0xFF3F88F1),
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600),
                    ),
                    Text(
                      '小时',
                      style: TextStyle(
                          color: const Color(0xFF7B7F8A),
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600),
                    )
                  ],
                ),
              ],
            ),
          ],
        ));
  }
}

class TaskPersonListMeItem extends StatelessWidget {
  final Map? item;
  const TaskPersonListMeItem({super.key, this.item});

  String formatDuration(int duration) {
    double hours = duration / 3600;
    String formattedDuration = hours.toStringAsFixed(2);
    if (formattedDuration.endsWith(".00")) {
      formattedDuration = hours.toStringAsFixed(0);
    }
    return formattedDuration;
  }

  String get departName {
    Map edges = item?['user']['User']['edges'];
    if (edges.isEmpty) {
      return '';
    }
    List roles = edges['department_roles'];
    List dNames = [];
    for (var e in roles) {
      if (!dNames.contains(e['edges']['department']['name'])) {
        dNames.add(e['edges']['department']['name']);
      }
    }
    String departmentName = dNames.join(',');
    return departmentName;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        color: const Color(0xFFE9F3FD),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.r),
                  child: CachedNetworkImage(
                    imageUrl: ValidatorUtils.testImageUrl, // item.avatar
                    width: 46,
                    height: 46,
                  ),
                ),
                SizedBox(width: 12.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item?['user']['User']['name'] ??
                          GlobalPreferences().userInfo?.user.name ??
                          '',
                      style: TextStyle(
                              color: const Color(0xFF000000), fontSize: 16.sp)
                          .pfMedium,
                    ),
                    Row(
                      children: [
                        Container(
                          constraints: BoxConstraints(maxWidth: 80.w),
                          child: Text(
                            departName ??
                                GlobalPreferences().userInfo!.departmentName,
                            style: TextStyle(
                                    color: const Color(0xFF464B59),
                                    fontSize: 12.sp,
                                    overflow: TextOverflow.ellipsis)
                                .pfRegular,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Row(
                          children: [
                            Text(
                              '总排名 ',
                              style: TextStyle(
                                      color: const Color(0xFF464B59),
                                      fontSize: 12.sp)
                                  .pfRegular,
                            ),
                            Text(
                              '${item?['ranking'] ?? '--'}',
                              style: TextStyle(
                                      color: const Color(0xFF464B59),
                                      fontSize: 13.sp)
                                  .pfSemiBold,
                            )
                          ],
                        )
                      ],
                    ),
                  ],
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  children: [
                    Text(
                      '累计学习',
                      style: TextStyle(
                              color: const Color(0xFF464B59), fontSize: 14.sp)
                          .pfRegular,
                    ),
                    Text(
                      ' ${formatDuration(item?['total_duration'])} ',
                      style: TextStyle(
                              color: const Color(0xFFFE5D30), fontSize: 16.sp)
                          .phBold,
                    ),
                    Text(
                      '小时',
                      style: TextStyle(
                              color: const Color(0xFF7B7F8A), fontSize: 12.sp)
                          .pfMedium,
                    )
                  ],
                ),
                Row(
                  children: [
                    Text(
                      '今日学习',
                      style: TextStyle(
                              color: const Color(0xFF464B59), fontSize: 14.sp)
                          .pfRegular,
                    ),
                    Text(
                      ' ${formatDuration(item?['today_duration'])} ',
                      style: TextStyle(
                              color: const Color(0xFF3F88F1), fontSize: 16.sp)
                          .phBold,
                    ),
                    Text(
                      '小时',
                      style: TextStyle(
                              color: const Color(0xFF7B7F8A), fontSize: 12.sp)
                          .pfMedium,
                    )
                  ],
                ),
              ],
            ),
          ],
        ));
  }
}
