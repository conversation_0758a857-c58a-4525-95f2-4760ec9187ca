import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/widget/submit_button.dart';
import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';
import 'package:npemployee/model/knowledge_contest/paper_extend_data_model.dart';
import 'package:npemployee/model/mine/mission_model.dart';
import 'package:npemployee/model/mine/sub_mission_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class ReadyExamPage extends StatefulWidget {
  final MissionListModel model;
  const ReadyExamPage({super.key, required this.model});

  @override
  State<ReadyExamPage> createState() => _ReadyExamPageState();
}

class _ReadyExamPageState extends State<ReadyExamPage> {
  // 倒计时剩余时间
  Duration remainingTime = Duration.zero;

  late Timer timer;

  PaperExtendDataModel? paperExtendDataModel;

  bool get canExam =>
      paperExtendDataModel != null &&
      remainingTime.inSeconds != 0 &&
      (paperExtendDataModel!.remaining_retake ?? 0) > 0;

  void _getPaperExtraData() {
    UserServiceProvider().getPaperExtendData(
        widget.model.mission.paper_id!, widget.model.missionUser.mission_id,
        cacheCallBack: (data) {}, successCallBack: (data) {
      _formatPaperContentData(data);
    }, errorCallBack: (err) {
      EasyLoading.showError(err.msg);
    });
  }

  void _formatPaperContentData(ResultData data) {
    paperExtendDataModel = PaperExtendDataModel.fromJson(data.data);
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeCountdown(widget.model.missionUser.mission_dead_line);
    _getPaperExtraData();
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  void _initializeCountdown(String targetTime) {
    DateTime now = DateTime.now();
    DateTime targetDate = DateTime.parse(targetTime);

    setState(() {
      remainingTime = targetDate.difference(now);
    });

    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainingTime.inSeconds <= 0) {
        timer.cancel();
      } else {
        setState(() {
          remainingTime = remainingTime - const Duration(seconds: 1);
        });
      }
    });
  }

  String _formatTime(Duration duration) {
    final days = duration.inDays;
    final hours = duration.inHours % 24;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    return "$days 天 $hours 时 $minutes 分 $seconds 秒";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        Container(
          decoration: const BoxDecoration(
              gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                Color(0xFF93C1FF),
                Color(0xFFF2F8FF),
                Color(0xFFE3EFFF)
              ])),
        ),
        Positioned(
          top: ScreenUtil().statusBarHeight,
          right: -23.w,
          child: Image.asset('assets/png/mine/task/ready_exam_top.png',
              width: 152.w, height: 180.h),
        ),
        SingleChildScrollView(
          child: Container(
            margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 28.h),
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: Text(
                    "距离考试结束",
                    style: TextStyle(
                            fontSize: 26.sp, color: const Color(0xFF15255B))
                        .phHeavy,
                  ),
                ),
                const SizedBox(height: 10),
                // 倒计时
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: _buildCountdownWidgets(),
                ),
                SizedBox(height: 35.h),

                // 考试任务
                _buildTaskSection(),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: 0,
          child: Container(
            width: ScreenUtil().screenWidth,
            height: 88.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(18.r),
                  topRight: Radius.circular(18.r)),
            ),
            child: SubmitButton(
                onTap: () {
                  NavigatorUtils.push(context, MineRouter.myTaskAnswerPage,
                      arguments: {
                        'model': paperExtendDataModel,
                        'missionModel': widget.model
                      }).then((v) {
                    _getPaperExtraData();
                  });
                },
                name: '开始考试',
                enable: canExam),
          ),
        ),
        const TeamManagerNav(title: ''),
      ],
    ));
  }

  // 倒计时组件
  List<Widget> _buildCountdownWidgets() {
    final days = remainingTime.inDays.toString().padLeft(2, '0');
    final hours = (remainingTime.inHours % 24).toString().padLeft(2, '0');
    final minutes = (remainingTime.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (remainingTime.inSeconds % 60).toString().padLeft(2, '0');

    return [
      _buildTimeUnit(days, "天"),
      _buildTimeUnit(hours, "时"),
      _buildTimeUnit(minutes, "分"),
      _buildTimeUnit(seconds, "秒"),
    ];
  }

  Widget _buildTimeUnit(String value, String unit) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 32.w,
            height: 29.h,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.colorBlue,
                    Color(0xFF018EFD),
                  ]),
              borderRadius: BorderRadius.circular(7.5.r),
            ),
            child: Text(
              value,
              style: TextStyle(
                fontSize: 15.sp,
                color: Colors.white,
              ).phBold,
            ),
          ),
          SizedBox(width: 6.w),
          Text(
            unit,
            style: TextStyle(fontSize: 14.sp, color: const Color(0xFF15255B))
                .phMedium,
          ),
        ],
      ),
    );
  }

  // 考试任务
  Widget _buildTaskSection() {
    return Container(
      padding: EdgeInsets.fromLTRB(17.w, 30.h, 17.w, 40.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            alignment: Alignment.bottomLeft,
            children: [
              Container(
                width: 85.5.w,
                height: 8.5.h,
                decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [
                  const Color(0xFFABECFB),
                  const Color(0xFFFFFFFF).withOpacity(0)
                ])),
              ),
              Text(
                "考试任务",
                style:
                    TextStyle(fontSize: 18.sp, color: const Color(0xFF222222))
                        .phBold,
              ),
            ],
          ),
          SizedBox(height: 20.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 11.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildTaskItem(
                    "试题分数",
                    "${paperExtendDataModel?.max_score ?? '--'}分",
                    'assets/png/mine/task/exam_score.png'),
                _buildTaskItem(
                    "补考机会",
                    "剩余${paperExtendDataModel?.remaining_retake ?? '--'}次",
                    'assets/png/mine/task/resit.png'),
                _buildTaskItem(
                    "及格分数",
                    "${paperExtendDataModel?.pass_score ?? '--'}分",
                    'assets/png/mine/task/pass_score.png'),
              ],
            ),
          ),
          SizedBox(height: 32.h),
          Stack(
            alignment: Alignment.bottomLeft,
            children: [
              Container(
                width: 85.5.w,
                height: 8.5.h,
                decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [
                  const Color(0xFFABECFB),
                  const Color(0xFFFFFFFF).withOpacity(0)
                ])),
              ),
              Text(
                "考试须知",
                style:
                    TextStyle(fontSize: 18.sp, color: const Color(0xFF222222))
                        .phBold,
              ),
            ],
          ),
          SizedBox(height: 18.h),
          _buildNoticeSection('您需在规定时间内通过考试；'),
          SizedBox(height: 18.h),
          _buildNoticeSection('答题过程中每题限时60秒，超时将自动视为答错。', highlight: '60秒'),
          SizedBox(height: 18.h),
          _buildNoticeSection('考试中途退出会继续计时，请及时返回答题；', highlight: '中途退出会继续计时'),
          SizedBox(height: 18.h),
          _buildNoticeSection(
              '首次未通过考试会有${paperExtendDataModel?.max_retake}次补考机会，均未通过则需重新完成学习任务后再次参加考试。',
              highlight: '${paperExtendDataModel?.max_retake}次'),
        ],
      ),
    );
  }

  Widget _buildTaskItem(String title, String value, String imgPath) {
    return Column(
      children: [
        Image.asset(imgPath, width: 42, height: 42),
        SizedBox(height: 7.h),
        Text(title,
            style: TextStyle(fontSize: 12.sp, color: const Color(0xFF222222))
                .pfMedium),
        SizedBox(height: 2.h),
        Text(value,
            style: TextStyle(fontSize: 12.sp, color: const Color(0xFF697593))
                .pfRegular),
      ],
    );
  }

  // 考试须知
  Widget _buildNoticeSection(String content, {String? highlight}) {
    if (highlight != null && highlight.isNotEmpty) {
      final textSpans = <TextSpan>[];
      final parts = content.split(highlight);
      for (int i = 0; i < parts.length; i++) {
        textSpans.add(TextSpan(
          text: parts[i],
          style: TextStyle(color: const Color(0xFF6D7590), fontSize: 14.sp)
              .phMedium,
        ));
        if (i < parts.length - 1) {
          textSpans.add(TextSpan(
            text: highlight,
            style: TextStyle(color: AppTheme.colorBlue, fontSize: 14.sp).phBold,
          ));
        }
      }
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('•  ',
              style: TextStyle(color: const Color(0xFF6D7590), fontSize: 14.sp)
                  .phMedium),
          Expanded(
            child: RichText(
              text: TextSpan(children: textSpans),
            ),
          ),
        ],
      );
    } else {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('•  ',
              style: TextStyle(color: const Color(0xFF6D7590), fontSize: 14.sp)
                  .phMedium),
          Expanded(
              child: Text(content,
                  style:
                      TextStyle(color: const Color(0xFF6D7590), fontSize: 14.sp)
                          .phMedium)),
        ],
      );
    }
  }
}
