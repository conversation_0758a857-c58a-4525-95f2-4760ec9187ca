import 'package:flutter/material.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';

class TaskAnswerAnalysisPage extends StatefulWidget {
  final int? index;
  final List<PaperContentItemModel> questions;
  const TaskAnswerAnalysisPage(
      {super.key, this.index, required this.questions});

  @override
  State<TaskAnswerAnalysisPage> createState() => _TaskAnswerAnalysisPageState();
}

class _TaskAnswerAnalysisPageState extends State<TaskAnswerAnalysisPage> {
  int _index = 0;
  late PageController _pageController;

  List<PaperContentItemModel> questions = [];

  void _pageChange(int index) {
    _index = index;
    _pageController.jumpTo(index * ScreenUtil().screenWidth);
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    if (widget.index != null) {
      _index = widget.index! - 1;
    }
    _pageController = PageController(initialPage: _index);
    questions = widget.questions;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FD),
      appBar: const CommonNav(title: '答案解析'),
      body: SizedBox(
        width: ScreenUtil().screenWidth,
        height: ScreenUtil().screenHeight,
        child: Stack(
          children: [
            Column(
              children: [
                if (questions.isNotEmpty) _topView(),
                Container(height: 14.h, color: Colors.white),
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    onPageChanged: _pageChange,
                    itemCount: questions.length,
                    itemBuilder: _itemBuilder,
                  ),
                )
              ],
            ),
            // 底部导航按钮
            Positioned(
              bottom: 0,
              child: _buildBottomNavigation(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigation(BuildContext c) {
    return Container(
      width: ScreenUtil().screenWidth,
      height: 90.h,
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 53.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFEEEEEE), width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildNavigationButton('上一题', 'assets/png/mine/task/pre_disable.png',
              () {
            if (_index == 0) {
              return;
            }
            _index--;
            _pageChange(_index);
          }, _index != 0),
          _buildNavigationButton('答题卡', 'assets/png/mine/task/<EMAIL>',
              () {
            showDialog(
                context: c,
                builder: (_) {
                  return TaskAnswerCardDialog(
                    questions: questions,
                    onTap: (v) {
                      _index = v - 1;
                      _pageChange(_index);
                    },
                  );
                });
          }, true),
          _buildNavigationButton('下一题', 'assets/png/mine/task/next_enable.png',
              () {
            if (_index == questions.length - 1) {
              return;
            }
            _index++;
            _pageChange(_index);
          }, _index != questions.length - 1),
        ],
      ),
    );
  }

  Widget _buildNavigationButton(
      String text, String imgPath, VoidCallback onTap, bool isEnable) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: Colors.transparent,
        ),
        child: Column(
          children: [
            Image.asset(imgPath,
                width: 25,
                height: 25,
                color: isEnable
                    ? const Color(0xFF333333)
                    : const Color(0xFF999999)),
            Text(text,
                style: TextStyle(
                        color: isEnable
                            ? const Color(0xFF333333)
                            : const Color(0xFF999999),
                        fontSize: 13.sp)
                    .pfRegular),
          ],
        ),
      ),
    );
  }

  // 其他辅助方法...
  Widget _topView() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(right: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                'assets/png/xiaoxin/question_muti_bac.png',
                width: 60.w,
                height: 29.h,
              ),
              Text(
                questions[_index].module_name ?? '未知',
                style: TextStyle(
                  color: const Color(0xFF0054FF),
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Image.asset(
                'assets/png/xiaoxin/question_num.png',
                width: 20,
                height: 20,
              ),
              Text(
                '${_index + 1}',
                style: TextStyle(
                  color: const Color(0xFF0054FF),
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '/${questions.length}',
                style: TextStyle(
                  color: const Color(0xFF464B59),
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _itemBuilder(BuildContext context, int index) {
    PaperContentItemModel question = questions[index];
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildQuestionContent(question),
          SizedBox(height: 12.h),
          _buildAnswerSection(question),
          SizedBox(height: 12.h),
          _buildKeyPointsSection(question),
          SizedBox(height: 12.h),
          _buildAnalysisSection(question),
          SizedBox(height: 90.h),
        ],
      ),
    );
  }

  // 构建问题内容部分
  Widget _buildQuestionContent(PaperContentItemModel question) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Html(data: question.content),
          SizedBox(height: 10.h),
          ...question.options.map((e) => _buildOptionItem(question, e)),
        ],
      ),
    );
  }

  Widget _buildAnswerSection(PaperContentItemModel question) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 4.w,
                height: 16.h,
                decoration: BoxDecoration(
                    color: const Color(0xFF0054FF),
                    borderRadius: BorderRadius.circular(2.r)),
              ),
              SizedBox(width: 7.w),
              Text('答案',
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w500)),
            ],
          ),
          SizedBox(height: 22.h),
          Row(
            children: [
              Text('正确答案',
                  style: TextStyle(
                      color: const Color(0xFF333333), fontSize: 14.sp)),
              SizedBox(width: 13.w),
              Text(
                '${question.answer.join(',')}',
                style: TextStyle(
                    color: const Color(0xFF36D176),
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w500),
              ),
              if (question.selected.isNotEmpty) SizedBox(width: 22.h),
              if (question.selected.isNotEmpty)
                Text('您的答案',
                    style: TextStyle(
                        color: const Color(0xFF333333), fontSize: 14.sp)),
              SizedBox(width: 13.w),
              Text(
                question.selected.join(','),
                style: TextStyle(
                    color: const Color(0xFFFE5A59),
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKeyPointsSection(PaperContentItemModel question) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4.w,
                height: 16.h,
                decoration: BoxDecoration(
                    color: const Color(0xFF0054FF),
                    borderRadius: BorderRadius.circular(2.r)),
              ),
              SizedBox(width: 7.w),
              Text('考点',
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w500)),
            ],
          ),
          SizedBox(height: 16.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              ...question.key_point.map((e) {
                return Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                      color: const Color(0xFFE8F0FF),
                      borderRadius: BorderRadius.circular(10.r)),
                  child: Text(
                    e,
                    style: TextStyle(
                        color: const Color(0xFF0054FF), fontSize: 14.sp),
                  ),
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisSection(PaperContentItemModel question) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r), topRight: Radius.circular(16.r)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 4.w,
                  height: 16.h,
                  decoration: BoxDecoration(
                      color: const Color(0xFF0054FF),
                      borderRadius: BorderRadius.circular(2.r)),
                ),
                SizedBox(width: 7.w),
                Text('解析',
                    style: TextStyle(
                        color: const Color(0xFF333333),
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w500)),
              ],
            ),
            SizedBox(height: 19.h),
            Html(data: question.analysis),
          ],
        ));
  }

  // 构建选项显示
  Widget _buildOptionItem(
      PaperContentItemModel question, Map<String, dynamic> option) {
    bool isCorrect = question.answer.contains(option['option_key']);
    bool wasSelected = question.selected.contains(option['option_key']);

    Color backgroundColor = Colors.transparent;
    Color textColor = const Color(0xFF333333);

    if (wasSelected && isCorrect) {
      backgroundColor = const Color(0xFF36D176);
      textColor = Colors.white;
    } else if (wasSelected && !isCorrect) {
      backgroundColor = const Color(0xFFFE5A59);
      textColor = Colors.white;
    } else if (!wasSelected && isCorrect) {
      if (question.question_category == QuestionCategory.MULTIPLE) {
        backgroundColor = const Color(0xFFFE5A59);
      } else {
        backgroundColor = const Color(0xFF36D176);
      }
      textColor = Colors.white;
    }

    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(vertical: 10.h),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(
                question.question_category == QuestionCategory.MULTIPLE
                    ? 20
                    : 8.r,
              ),
              border: Border.all(color: const Color(0xFFCCCCCC), width: 0.5),
            ),
            child: Text(
              option['option_key'] ?? '',
              style: TextStyle(
                color: textColor,
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 15.w),
          Expanded(child: Html(data: option['option_value'] ?? '')),
        ],
      ),
    );
  }
}

class TaskAnswerCardDialog extends StatelessWidget {
  final List<PaperContentItemModel> questions;
  final Function(int) onTap;
  const TaskAnswerCardDialog(
      {super.key, required this.questions, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Material(
        color: Colors.transparent,
        child: Center(
          child: Container(
              width: 343.w,
              padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 24.w),
              constraints: BoxConstraints(maxHeight: 400.h, minHeight: 200.h),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.r),
                  color: Colors.white),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Text('答题卡',
                          style: TextStyle(
                                  color: const Color(0xFF323640),
                                  fontSize: 18.sp)
                              .pfMedium),
                      const Expanded(child: SizedBox()),
                      _answerCardDesView(const Color(0xFFF3F4F6), '未答'),
                      SizedBox(width: 12.w),
                      _answerCardDesView(const Color(0xFF36D176), '正确'),
                      SizedBox(width: 12.w),
                      _partView(12),
                      SizedBox(width: 3.w),
                      Text('半对',
                          style: TextStyle(
                                  color: const Color(0xFF8B93A6),
                                  fontSize: 14.sp)
                              .pfRegular),
                      SizedBox(width: 12.w),
                      _answerCardDesView(const Color(0xFFFE5A59), '错误'),
                    ],
                  ),
                  SizedBox(height: 28.h),
                  SingleChildScrollView(
                    child: Wrap(
                      spacing: 28.w,
                      runSpacing: 24.h,
                      children: [
                        ...questions.map((e) {
                          return GestureDetector(
                            onTap: () {
                              NavigatorUtils.pop(context);
                              onTap(e.question_number);
                            },
                            child: e.answer_status == AnswerStatus.PART
                                ? _partView(40, num: e.question_number)
                                : Container(
                                    alignment: Alignment.center,
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                        color:
                                            e.answer_status == AnswerStatus.NOT
                                                ? const Color(0xFFF3F4F6)
                                                : (e.answer_status ==
                                                        AnswerStatus.RIGHT
                                                    ? const Color(0xFF36D176)
                                                    : const Color(0xFFFE5A59)),
                                        borderRadius:
                                            BorderRadius.circular(20)),
                                    child: Text('${e.question_number}',
                                        style: TextStyle(
                                                color: e.answer_status ==
                                                        AnswerStatus.NOT
                                                    ? const Color(0xFF333333)
                                                    : Colors.white,
                                                fontSize: 18.sp)
                                            .pfSemiBold),
                                  ),
                          );
                        }),
                      ],
                    ),
                  ),
                ],
              )),
        ));
  }

  Widget _answerCardDesView(Color color, String title) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: color,
          ),
        ),
        SizedBox(width: 3.w),
        Text(title,
            style: TextStyle(color: const Color(0xFF8B93A6), fontSize: 14.sp)
                .pfRegular),
      ],
    );
  }

  Widget _partView(double size, {int? num}) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 半圆绿色覆盖
        ClipOval(
          child: SizedBox(
            width: size,
            height: size,
            child: Transform.rotate(
              angle: 45 * 3.141592653589793 / 180, // 顺时针旋转120度，将角度转换为弧度
              child: ClipOval(
                child: SizedBox(
                  width: size,
                  height: size,
                  child: Stack(
                    children: [
                      // 白色部分
                      Container(
                        color: const Color(0xFFF3F4F6),
                      ),
                      // 左半部分为绿色
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          width: size / 2, // 左半部分宽度
                          color: const Color(0xFF36D176),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        // 圆心数字
        if (num != null)
          Text(
            "$num",
            style: TextStyle(fontSize: 18.sp, color: const Color(0xFF333333))
                .pfSemiBold,
          ),
      ],
    );
  }
}
