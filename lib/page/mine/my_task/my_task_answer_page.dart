import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/widget/submit_button.dart';
import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';
import 'package:npemployee/model/knowledge_contest/paper_data_model.dart';
import 'package:npemployee/model/knowledge_contest/paper_extend_data_model.dart';
import 'package:npemployee/model/mine/mission_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:tap_debouncer/tap_debouncer.dart';

class MyTaskAnswerPage extends StatefulWidget {
  final MissionListModel missionModel;
  final PaperExtendDataModel model;
  const MyTaskAnswerPage(
      {super.key, required this.model, required this.missionModel});

  @override
  State<MyTaskAnswerPage> createState() => _MyTaskAnswerPageState();
}

class _MyTaskAnswerPageState extends State<MyTaskAnswerPage> {
  int _index = 0;
  int _seconds = 0;
  Timer? _timer;
  PageController _pageController = PageController(initialPage: 0);
  PaperContentModel? paperContent;
  List<PaperContentItemModel> questions = [];
  int questionCount = 60; //60秒内没完成答题，算错答
  int currentAnswer = 0;

  //提交答案
  void _submitAnswer() {
    //提交答题数据并刷新页面
    EasyLoading.show();
    UserServiceProvider()
        .submitAnswer(
            paperContent!.answer_record_id,
            questions[_index].question_id,
            questions[_index].paper_id,
            questions[_index].selected,
            _seconds)
        .then((value) {
      EasyLoading.dismiss();
      if (value?.code == 0) {
        for (var element in questions) {
          if (element.question_id == value?.data['question_id']) {
            AnswerStatus answer_status = AnswerStatus.NOT;
            if (value?.data['answer_status'] == 'PART') {
              answer_status = AnswerStatus.PART;
            } else if (value?.data['answer_status'] == 'WRONG') {
              answer_status = AnswerStatus.WRONG;
            } else if (value?.data['answer_status'] == 'RIGHT') {
              answer_status = AnswerStatus.RIGHT;
            }
            element.answer_status = answer_status;
          }
        }
        if (value?.data['answer_status'] == 'RIGHT' &&
            _index != questions.length - 1) {
          _index++;
          _pageChange(_index);
        }
        currentAnswer = _index;
        if (mounted) {
          setState(() {});
        }
      } else {
        EasyLoading.showError(value?.msg ?? '出错了，请联系管理员');
      }
    });
  }

  // 交卷
  void _submit() {
    EasyLoading.show();
    UserServiceProvider().getPaperData(
      widget.missionModel.mission.paper_id!,
      missionId: widget.missionModel.missionUser.id,
      cacheCallBack: (data) {},
      successCallBack: (data) {
        EasyLoading.dismiss();
        PaperDataModel pData = PaperDataModel.fromJson(data.data);
        NavigatorUtils.push(context, MineRouter.taskTranscriptPage, arguments: {
          'paperData': pData,
          'paperContent': paperContent,
          'mission': widget.missionModel
        });
      },
      errorCallBack: (error) {
        EasyLoading.dismiss();
      },
    );
  }

  void _timerStart() {
    int count = 0;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _seconds--;
        count++;
        // 每3秒上传一次数据
        if (count >= 3) {
          count = 0;
          debugPrint(
              '每3秒上传一次数据, _seconds: $_seconds, useTime: ${widget.model.limit_time! - _seconds}');
          UserServiceProvider().submitExamConsumeTime(
              paperContent!.answer_record_id,
              widget.model.limit_time! - _seconds);
        }
        if (questions[_index].answer_status == AnswerStatus.NOT &&
            _index >= currentAnswer) {
          questionCount--;
          if (questionCount == 0) {
            if (_index == questions.length - 1) {
              //最后一题直接交卷
              timer.cancel();
              currentAnswer = 0;
              _submit();
            } else {
              //自动提交进入下一题
              _index++;
              _submitAnswer();
              _pageController.animateToPage(_index,
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.ease);
            }
            debugPrint('60秒内未完成答题');
          }
        }

        if (_seconds == 0) {
          currentAnswer = 0;
          timer.cancel();
          _submit();
        }
      });
    });
  }

  String _formatTime() {
    int minutes = _seconds ~/ 60; // 计算分钟数
    int remainingSeconds = _seconds % 60; // 计算剩余的秒数

    // 格式化成 mm:ss 格式
    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = remainingSeconds.toString().padLeft(2, '0');

    return "$formattedMinutes:$formattedSeconds";
  }

  void _pageChange(int index) {
    _index = index;
    _pageController.jumpTo(index * ScreenUtil().screenWidth);
    setState(() {});
  }

  void _getPaperContent() {
    EasyLoading.show();
    UserServiceProvider().getPaperContent(
      widget.missionModel.mission.paper_id!,
      missionId: widget.missionModel.missionUser.id,
      cacheCallBack: (data) {},
      successCallBack: (data) {
        EasyLoading.dismiss();
        if (data.code == 0) {
          paperContent = PaperContentModel.fromJson(data.data);
          questions = paperContent!.list;
          _timerStart();
          setState(() {});
        }
      },
      errorCallBack: (data) {
        EasyLoading.dismiss();
        EasyLoading.showError(data.msg);
      },
    );
  }

  @override
  void initState() {
    super.initState();
    _getPaperContent();
    _seconds = widget.model.limit_time ?? 0;
  }

  @override
  void dispose() {
    currentAnswer = 0;
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                  Color(0xFF93C1FF),
                  Color(0xFFF2F8FF),
                  Color(0xFFE3EFFF)
                ])),
          ),
          Positioned(
            right: 0,
            child: Image.asset('assets/png/mine/task/answer_top_bac.png',
                width: ScreenUtil().screenWidth, height: 175.h),
          ),
          if (questions.isNotEmpty)
            SizedBox(
              width: ScreenUtil().screenWidth,
              height: ScreenUtil().screenHeight,
              child: Stack(
                children: [
                  Column(
                    children: [
                      _topView(),
                      Expanded(
                          child: PageView.builder(
                              controller: _pageController,
                              onPageChanged: _pageChange,
                              physics: questions[_index].answer_status ==
                                          AnswerStatus.NOT &&
                                      _index >= currentAnswer
                                  ? const NeverScrollableScrollPhysics()
                                  : const AlwaysScrollableScrollPhysics(),
                              itemCount: questions.length,
                              itemBuilder: _itemBuilder))
                    ],
                  ),
                  _optionsView(),
                ],
              ),
            ),
          TeamManagerNav(title: paperContent?.paper_name ?? ''),
        ],
      ),
    );
  }

  Widget _optionsView() {
    if (questions[_index].selected.isNotEmpty &&
        questions[_index].answer_status == AnswerStatus.NOT &&
        _index >= currentAnswer) {
      return Positioned(
          bottom: 0,
          child: Container(
            height: 90.h,
            width: ScreenUtil().screenWidth,
            color: Colors.white,
            child: SubmitButton(
                onTap: () {
                  _submitAnswer();
                },
                name:
                    '确定', //${paperContent?.max_wrong == 0 ? '' : '(剩余${paperContent?.max_wrong}次答错机会)'}
                enable: true),
          ));
    } else if (questions[_index].answer_status != AnswerStatus.NOT ||
        (questions[_index].answer_status == AnswerStatus.NOT &&
            _index < currentAnswer)) {
      return Positioned(
          bottom: 0,
          child: Container(
            height: 90.h,
            width: ScreenUtil().screenWidth,
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    if (_index == 0) {
                      return;
                    }
                    _index--;
                    _pageChange(_index);
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    padding:
                        EdgeInsets.symmetric(vertical: 14.h, horizontal: 46.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        color: Colors.transparent,
                        border: Border.all(
                            color: _index == 0
                                ? const Color(0xFF999999)
                                : const Color(0xFF0054FF))),
                    child: Text('上一题',
                        style: TextStyle(
                            color: _index == 0
                                ? const Color(0xFF999999)
                                : const Color(0xFF0054FF),
                            fontSize: 16.sp)),
                  ),
                ),
                TapDebouncer(onTap: () async {
                  print('点击了下一题');
                  if (_index == questions.length - 1) {
                    if (_timer != null && _timer!.isActive) {
                      _timer!.cancel();
                      _timer = null;
                    }
                    _submit();
                  } else {
                    _index++;
                    _pageChange(_index);
                  }
                }, builder: (_, TapDebouncerFunc? onTap) {
                  return GestureDetector(
                    onTap: onTap,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: EdgeInsets.symmetric(
                          vertical: 14.h, horizontal: 46.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.r),
                          color: const Color(0xFF0054FF)),
                      child: Text(
                          _index == questions.length - 1 &&
                                  (questions[_index].answer_status !=
                                          AnswerStatus.NOT ||
                                      (questions[_index].answer_status ==
                                              AnswerStatus.NOT &&
                                          _index < currentAnswer))
                              ? '提交答案'
                              : (questions[_index].question_category ==
                                          QuestionCategory.SINGLE ||
                                      questions[_index].question_category ==
                                          QuestionCategory.TF)
                                  ? '下一题'
                                  : questions[_index].answer_status ==
                                              AnswerStatus.NOT &&
                                          _index >= currentAnswer
                                      ? '确定'
                                      : '下一题',
                          style: TextStyle(
                              color: const Color(0xFFFFFFFF), fontSize: 16.sp)),
                    ),
                  );
                })
              ],
            ),
          ));
    }
    return Container();
    /* return Positioned(
        bottom: 0,
        child: Container(
          width: ScreenUtil().screenWidth,
          height: 90.h,
          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
          decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(
                  top: BorderSide(color: Color(0xFFEEEEEE), width: 0.5))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _index > 0
                  ? GestureDetector(
                      onTap: () {
                        _index--;
                        _pageChange(_index);
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: EdgeInsets.symmetric(
                            vertical: 14.h, horizontal: 46.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.r),
                          color: Colors.transparent,
                        ),
                        child: Text('上一题',
                            style: TextStyle(
                                color: const Color(0xFF0054FF),
                                fontSize: 16.sp)),
                      ),
                    )
                  : const SizedBox(),
              questions[_index].selected.isNotEmpty
                  ? GestureDetector(
                      onTap: () {
                        if (_index == questions.length - 1 &&
                            questions[_index].answer_status !=
                                AnswerStatus.NOT) {
                          if (_timer != null && _timer!.isActive) {
                            _timer!.cancel();
                            _timer = null;
                          }
                        } else {
                          if (questions[_index].answer_status ==
                              AnswerStatus.NOT) {
                            //提交答题数据并刷新页面
                            UserServiceProvider()
                                .submitAnswer(
                                    paperContent!.answer_record_id,
                                    questions[_index].question_id,
                                    questions[_index].paper_id,
                                    questions[_index].selected,
                                    _seconds)
                                .then((value) {
                              if (value?.code == 0) {
                                _getPaperContent();
                              }
                            });
                          } else {
                            _index++;
                            _pageChange(_index);
                          }
                        }
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: EdgeInsets.symmetric(
                            vertical: 14.h, horizontal: 46.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16.r),
                            color: const Color(0xFF0054FF)),
                        child: Text(
                            _index == questions.length - 1 &&
                                    questions[_index].answer_status !=
                                        AnswerStatus.NOT
                                ? '完成'
                                : (questions[_index].question_category ==
                                            QuestionCategory.SINGLE ||
                                        questions[_index].question_category ==
                                            QuestionCategory.TF)
                                    ? '下一题'
                                    : questions[_index].answer_status ==
                                            AnswerStatus.NOT
                                        ? '确定'
                                        : '下一题',
                            style: TextStyle(
                                color: const Color(0xFFFFFFFF),
                                fontSize: 16.sp)),
                      ),
                    )
                  : const SizedBox()
            ],
          ),
        )); */
  }

  Widget _topView() {
    return Container(
      margin: EdgeInsets.only(top: 88.h),
      padding: EdgeInsets.only(right: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Image.asset('assets/png/xiaoxin/question_muti_bac.png',
                  width: 60.w, height: 29.h),
              Text(questions[_index].module_name ?? '未知',
                  style:
                      TextStyle(color: const Color(0xFF0054FF), fontSize: 12.sp)
                          .pfRegular),
            ],
          ),
          Row(
            children: [
              Image.asset('assets/png/xiaoxin/question_time.png',
                  width: 20, height: 20),
              Text(
                _formatTime(),
                style:
                    TextStyle(color: const Color(0xFF464B59), fontSize: 16.sp)
                        .pfMedium,
              ),
              SizedBox(width: 25.w),
              Image.asset('assets/png/xiaoxin/question_num.png',
                  width: 20, height: 20),
              Text(
                '${_index + 1}',
                style:
                    TextStyle(color: const Color(0xFF0054FF), fontSize: 16.sp)
                        .pfSemiBold,
              ),
              Text(
                '/${questions.length}',
                style:
                    TextStyle(color: const Color(0xFF464B59), fontSize: 16.sp)
                        .pfSemiBold,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _itemBuilder(c, index) {
    PaperContentItemModel question = questions[index];
    bool isError = question.answer_status == AnswerStatus.WRONG ||
        question.answer_status == AnswerStatus.PART;
    return SingleChildScrollView(
      child: Column(children: [
        SizedBox(height: 48.h),
        Container(
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(16.w)),
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Html(data: question.content),
              SizedBox(height: 10.h),
              ...question.options.map((e) {
                return _choiceView(question, e);
              }),
            ],
          ),
        ),
        if (isError) SizedBox(height: 12.h),
        if (isError)
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 4.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                          color: const Color(0xFF0054FF),
                          borderRadius: BorderRadius.circular(2.r)),
                    ),
                    SizedBox(width: 7.w),
                    Text('答案',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 18.sp)
                            .pfMedium),
                  ],
                ),
                SizedBox(height: 22.h),
                Row(
                  children: [
                    Text('正确答案',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 14.sp)
                            .pfRegular),
                    SizedBox(width: 13.w),
                    Text(
                      '${question.answer.join(',')}',
                      style: TextStyle(
                          color: const Color(0xFF36D176),
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w500),
                    ),
                    SizedBox(width: 22.h),
                    Text('您的答案',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 14.sp)
                            .pfRegular),
                    SizedBox(width: 13.w),
                    Text(
                      question.selected.join(','),
                      style: TextStyle(
                          color: const Color(0xFFFE5A59),
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ],
            ),
          ),
        if (isError) SizedBox(height: 12.h),
        if (isError)
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 4.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                          color: const Color(0xFF0054FF),
                          borderRadius: BorderRadius.circular(2.r)),
                    ),
                    SizedBox(width: 7.w),
                    Text('考点',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 18.sp)
                            .pfMedium),
                  ],
                ),
                SizedBox(height: 16.h),
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children: [
                    ...question.key_point.map((e) {
                      return Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                            color: const Color(0xFFE8F0FF),
                            borderRadius: BorderRadius.circular(10.r)),
                        child: Text(
                          e,
                          style: TextStyle(
                                  color: const Color(0xFF0054FF),
                                  fontSize: 14.sp)
                              .pfRegular,
                        ),
                      );
                    }),
                  ],
                ),
              ],
            ),
          ),
        if (isError) SizedBox(height: 12.h),
        if (isError)
          Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 4.w,
                        height: 16.h,
                        decoration: BoxDecoration(
                            color: const Color(0xFF0054FF),
                            borderRadius: BorderRadius.circular(2.r)),
                      ),
                      SizedBox(width: 7.w),
                      Text('解析',
                          style: TextStyle(
                                  color: const Color(0xFF333333),
                                  fontSize: 18.sp)
                              .pfMedium),
                    ],
                  ),
                  SizedBox(height: 19.h),
                  Html(data: question.analysis),
                ],
              )),
        SizedBox(height: ScreenUtil().bottomBarHeight + 90.h),
      ]),
    );
  }

  Widget _choiceView(PaperContentItemModel question, Map choice) {
    bool showResult = question.answer_status != AnswerStatus.NOT;
    bool isSelected = question.selected.contains(choice['option_key']);
    bool isCorrectAnswer = question.answer.contains(choice['option_key']);

    Color backgroundColor = Colors.transparent;
    Color textColor = const Color(0xFF333333);
    Color? borderColor;

    Color rightColor = const Color(0xFF36D176);
    Color wrongColor = const Color(0xFFFE5A59);

    if (showResult) {
      if (question.question_category == QuestionCategory.SINGLE ||
          question.question_category == QuestionCategory.TF) {
        if (isCorrectAnswer) {
          backgroundColor = rightColor;
          textColor = Colors.white;
        } else if (isSelected) {
          backgroundColor = wrongColor;
          textColor = Colors.white;
        }
      } else if (question.question_category == QuestionCategory.MULTIPLE) {
        if (isSelected && isCorrectAnswer) {
          // 选中且正确，绿色背景
          backgroundColor = rightColor;
          textColor = Colors.white;
        } else if (isSelected && !isCorrectAnswer) {
          // 选中且错误，红色背景
          backgroundColor = wrongColor;
          textColor = Colors.white;
        } else if (!isSelected && isCorrectAnswer) {
          // 漏选且是正确答案，边框绿色，文字绿色,白色背景
          textColor = rightColor;
          borderColor = rightColor;
        }
        //漏选且不是正确答案，保持不变
      }
    } else if (isSelected) {
      backgroundColor = const Color(0xFF0054FF);
      textColor = Colors.white;
    }

    return GestureDetector(
      onTap: () {
        if (showResult) return; // 已显示结果时不允许再选择

        if (question.question_category == QuestionCategory.MULTIPLE) {
          if (question.selected.contains(choice['option_key'])) {
            question.selected.remove(choice['option_key']);
          } else {
            question.selected.add(choice['option_key']);
          }
        } else {
          question.selected.clear();
          question.selected.add(choice['option_key']);
        }
        setState(() {});
      },
      child: Container(
        width: ScreenUtil().screenWidth,
        padding: EdgeInsets.symmetric(vertical: 10.h),
        child: Row(
          children: [
            Container(
              alignment: Alignment.center,
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(
                      question.question_category == QuestionCategory.SINGLE ||
                              question.question_category == QuestionCategory.TF
                          ? 20
                          : 8.r),
                  border: Border.all(
                      color: borderColor ?? const Color(0xFFCCCCCC),
                      width: borderColor != null ? 1.5 : 0.5)),
              child: Text(
                choice['option_key'] ?? '',
                style: TextStyle(color: textColor, fontSize: 18.sp).pfMedium,
              ),
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Html(data: choice['option_value'] ?? ''),
              /* Text(
              choice['option_value'] ?? '',
              style: TextStyle(color: textColor, fontSize: 16.sp),
            ) */
            ),
          ],
        ),
      ),
    );
  }
}
