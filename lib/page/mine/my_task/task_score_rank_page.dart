import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/model/knowledge_contest/paper_data_model.dart';
import 'package:npemployee/model/mine/mission_model.dart';
import 'package:npemployee/model/study/depart_rank_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/top_tab_item.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class TaskScoreRankPage extends StatefulWidget {
  final PaperDataModel model;
  final MissionListModel mission;
  const TaskScoreRankPage(
      {super.key, required this.model, required this.mission});

  @override
  State<TaskScoreRankPage> createState() => _TaskScoreRankPageState();
}

class _TaskScoreRankPageState extends State<TaskScoreRankPage> {
  int topTabIndex = 1; //1-个人榜 2-部门榜
  List departments = []; //部门列表
  List<MenuItemButton> get dropItems =>
      departments.map((e) => _menuItem(e)).toList();
  Map? selectedValue;
  bool dropIsOpen = false;

  Map? courseRankMap;
  List _tabs = ['排名', '姓名', '部门', '得分'];

  late int paperId;
  late int missionId;

  List<TaskDepartRankModel> departRankList = [];
  List<PaperRankModel> personsRank = [];
  List<PaperAllRank> allRank = [];

  final EasyRefreshController _refreshController = EasyRefreshController(
      controlFinishRefresh: true, controlFinishLoad: true);
  int page = 1;

//获取部门列表
  void _getDepartmentData() async {
    UserServiceProvider().getMissionDepartments(
        widget.mission.missionUser.mission_id, cacheCallBack: (data) {
      // _formatDepartmentData(data, true);
    }, successCallBack: (data) {
      if (data.code != 0) {
        EasyLoading.showError(data.msg);
        return;
      }
      _formatDepartmentData(data, false);
    }, errorCallBack: (err) {
      EasyLoading.showError(err.msg);
    });
  }

  void _formatDepartmentData(ResultData data, bool isCache) {
    departments.clear();
    List datas = data.data;
    datas.insert(0, {'name': '各部门', 'id': null});
    departments = datas;
    if (!isCache) {
      if (mounted) {
        setState(() {});
        _getRank(departId: selectedValue?['id'], isRefresh: true);
      }
    }
  }

  void _getRank({int? departId, required bool isRefresh}) async {
    page = isRefresh ? 1 : page + 1;
    ResultData? data = await UserServiceProvider().getPaperRank(paperId,
        department_id: departId, page: page, mission_id: missionId);
    if (isRefresh) {
      _refreshController.finishRefresh();
      _refreshController.resetFooter();
    } else {
      bool noMore = data?.data['list'].length < 10;
      _refreshController.finishLoad(
          noMore ? IndicatorResult.noMore : IndicatorResult.success);
    }
    if (data?.code == 0) {
      if (isRefresh) {
        allRank.clear();
      }
      for (var element in data?.data['list'] ?? []) {
        allRank.add(PaperAllRank.fromJson(element));
      }
      setState(() {});
    } else {
      EasyLoading.showError('未知错误，请联系管理员');
    }
  }

  @override
  void initState() {
    super.initState();
    paperId = widget.mission.mission.paper_id!;
    missionId = widget.mission.missionUser.mission_id;
    selectedValue = {'name': '各部门', 'id': null};
    _getDepartmentData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '成绩排名'),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 8.5.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 22.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TopTabItem(
                    onTap: () {
                      _tabs = ['排名', '姓名', '部门', '得分'];
                      setState(() {
                        topTabIndex = 1;
                      });
                      _getDepartmentData();
                    },
                    isSelect: topTabIndex == 1,
                    svgPath: topTabIndex == 1
                        ? 'assets/png/study/person_check.png'
                        : 'assets/png/study/person_uncheck.png'),
                TopTabItem(
                  onTap: () {
                    _tabs = ['排名', '部门', '学习人数', '通过率'];
                    setState(() {
                      topTabIndex = 2;
                    });
                    _getDepartmentData();
                  },
                  isSelect: topTabIndex == 2,
                  svgPath: topTabIndex == 2
                      ? 'assets/png/study/depart_check.png'
                      : 'assets/png/study/depart_uncheck.png',
                ),
              ],
            ),
          ),
          SizedBox(height: 9.h),
          if (topTabIndex == 1) _dropView(),
          SizedBox(height: 17.h),
          Expanded(
              child: Container(
            decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF),
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.r),
                    topRight: Radius.circular(16.r))),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(vertical: 11.h),
                  decoration: BoxDecoration(
                      color: const Color(0xFFE2EEFB),
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16.r),
                          topRight: Radius.circular(16.r))),
                  child: Row(
                    children: [
                      ..._tabs.map((e) {
                        return Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            child: Text(
                              e,
                              style: TextStyle(
                                      color: const Color(0xFF808080),
                                      fontSize: 13.sp)
                                  .pfRegular,
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                ),
                Expanded(
                    child: MediaQuery.removePadding(
                  removeTop: true,
                  context: context,
                  child: EasyRefresh.builder(
                      controller: _refreshController,
                      onRefresh: () {
                        if (topTabIndex == 1) {
                          _getRank(
                              departId: selectedValue?['id'], isRefresh: true);
                        }
                      },
                      onLoad: () {
                        if (topTabIndex == 1) {
                          _getRank(
                              departId: selectedValue?['id'], isRefresh: false);
                        }
                      },
                      childBuilder: (_, p) {
                        return ListView.builder(
                            physics: p,
                            itemCount: topTabIndex == 1
                                ? (allRank.length)
                                : (widget
                                    .model.all_rank_list_for_mission.length),
                            itemBuilder: (c, index) {
                              List<String> itemText = [];
                              if (topTabIndex == 1) {
                                PaperAllRank item = allRank[index];
                                itemText = [
                                  '${item.rank_no}',
                                  item.name ?? '',
                                  item.departNameSort,
                                  // DateTimeUtils.formatTime(item.use_time),
                                  item.score ?? '--'
                                ];
                              } else {
                                PaperRankMissionModel item = widget
                                    .model.all_rank_list_for_mission[index];
                                itemText = [
                                  '${index + 1}',
                                  item.department_name,
                                  '${item.answer_number}',
                                  item.pass_rate,
                                ];
                              }
                              return Container(
                                decoration: const BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            color: AppTheme.colorDivider,
                                            width: 0.5))),
                                child: Row(
                                  children: [
                                    ...itemText.asMap().entries.map((entry) {
                                      // 使用 asMap().entries 获取索引和值
                                      int idx = entry.key;
                                      String e = entry.value;
                                      Color textColor = const Color(0xFF232323);
                                      TextStyle textStyle = TextStyle(
                                              color: const Color(0xFF232323),
                                              fontSize: 14.sp)
                                          .pfRegular;
                                      if (idx == 0) {
                                        // 只有排名需要判断前三名
                                        if (e == '1' &&
                                            itemText.indexOf(e) == 0) {
                                          textColor = const Color(0xFFFF4713);
                                          textStyle = textStyle
                                              .copyWith(color: textColor)
                                              .phHeavy; // 金牌
                                        } else if (e == '2' &&
                                            itemText.indexOf(e) == 0) {
                                          textColor =
                                              const Color(0xFFFDA052); // 银牌
                                          textStyle = textStyle
                                              .copyWith(color: textColor)
                                              .phHeavy;
                                        } else if (e == '3' &&
                                            itemText.indexOf(e) == 0) {
                                          textColor =
                                              const Color(0xFF6E9DFF); // 铜牌
                                          textStyle = textStyle
                                              .copyWith(color: textColor)
                                              .phHeavy;
                                        }
                                      } else if (idx == itemText.length - 1) {
                                        // 最后一项是分数
                                        if (topTabIndex == 1) {
                                          textColor = int.parse(e) <
                                                  int.parse(
                                                      widget.model.score_pass)
                                              ? const Color(0xFFE02020)
                                              : const Color(0xFF232323);
                                          textStyle = textStyle
                                              .copyWith(color: textColor)
                                              .phRegular;
                                        }
                                      }
                                      int flex = 1;
                                      if (topTabIndex == 1) {
                                        int idxx = itemText.indexOf(e);
                                        if (idxx == 1 || idxx == 2) {
                                          flex = 2;
                                        } else {
                                          flex = 1;
                                        }
                                      }
                                      return Expanded(
                                        flex: flex,
                                        child: Container(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 5.h),
                                            alignment: Alignment.center,
                                            child: Text(e, style: textStyle)),
                                      );
                                    }),
                                  ],
                                ),
                              );
                            });
                      }),
                ))
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _dropView() {
    return MenuAnchor(
      style: MenuStyle(
        visualDensity: VisualDensity.compact,
        backgroundColor: const WidgetStatePropertyAll(Colors.white),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(color: Color(0xFFDADADA), width: 0.5),
          ),
        ),
        elevation: const WidgetStatePropertyAll(0),
      ),
      builder:
          (BuildContext context, MenuController controller, Widget? child) {
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            controller.isOpen ? controller.close() : controller.open();
          },
          child: Container(
            margin: EdgeInsets.only(left: 16.w),
            child: RichText(
              text: TextSpan(
                  text: selectedValue?['name'] ?? departments.first['name'],
                  style:
                      TextStyle(color: const Color(0xFF000000), fontSize: 12.sp)
                          .pfMedium,
                  children: [
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Image.asset(
                        dropIsOpen
                            ? 'assets/png/xiaoxin/drop_up.png'
                            : 'assets/png/xiaoxin/drop_down.png',
                        width: 15.w,
                        height: 10.h,
                      ),
                    ),
                  ]),
            )
            /* Row(
              children: [
                Text(
                  selectedValue?.name ?? departmentModels.first.name,
                  style:
                      TextStyle(color: const Color(0xFF000000), fontSize: 12.sp)
                          .pfMedium,
                ),
                Image.asset(
                  dropIsOpen
                      ? 'assets/png/xiaoxin/drop_up.png'
                      : 'assets/png/xiaoxin/drop_down.png',
                  width: 15.w,
                  height: 10.h,
                ),
              ],
            ) */
            ,
          ),
        );
      },
      menuChildren: dropItems,
      onClose: () {
        setState(() {
          dropIsOpen = false;
        });
      },
      onOpen: () {
        setState(() {
          dropIsOpen = true;
        });
      },
    );
  }

  MenuItemButton _menuItem(Map e) {
    return MenuItemButton(
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 7.5.w, vertical: 8.h),
        constraints: BoxConstraints(maxWidth: 150.w),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color: departments.indexOf(e) == departments.length - 1
                        ? Colors.transparent
                        : const Color(0xFFE6E6E6).withOpacity(0.5)))),
        child: Text(
          e['name'],
          style: TextStyle(
                  color: selectedValue == e
                      ? AppTheme.colorBlue
                      : const Color(0xFF000000),
                  fontSize: 12.sp)
              .pfMedium,
        ),
      ),
      onPressed: () {
        setState(() {
          selectedValue = e;
        });
        _getRank(departId: e['id'], isRefresh: true);
      },
    );
  }
}
