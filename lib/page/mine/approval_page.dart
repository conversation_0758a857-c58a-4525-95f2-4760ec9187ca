import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import '../../widget/mine/approval/approval_cell.dart';
import '../../widget/mine/approval/reusable_tab.dart';

class ApprovalPage extends StatefulWidget {
  @override
  _ApprovalPageState createState() => _ApprovalPageState();
}

class _ApprovalPageState extends State<ApprovalPage> {
  String searchQuery = '';
  int selectedIndex = 0; // To track "待审批" (0) or "已完成" (1)
  int segmentIndex = 0; // To track "通过" (0) or "驳回" (1)

  final EasyRefreshController _refreshController = EasyRefreshController();
  int page = 1;
  int size = 10;
  bool hasMore = false;

  // Sample data
  List pendingItems = [];

  List approvedItems = [];

  List rejectedItems = [];

  // Filtered items based on searchQuery
  List get filteredItems {
    List items = [];
    if (selectedIndex == 0) {
      items = pendingItems;
    } else if (segmentIndex == 0) {
      items = approvedItems;
    } else if (segmentIndex == 1) {
      items = rejectedItems;
    }
    if (searchQuery.isEmpty) {
      return items;
    } else {
      return items
          .where((item) =>
              item['name']!.contains(searchQuery) ||
              item['phone']!.contains(searchQuery) ||
              item['idCard']!.contains(searchQuery))
          .toList();
    }
  }

  List<String> _getRolesString(List roles) {
    List<String> roleStrings = [];
    roles.forEach((e) {
      String role = '';
      String d = e['department']['name'];
      List r = [];
      e['roles'].forEach((e1) {
        r.add(e1['role']['name']);
      });
      role = '$d | ${r.join(',')}';
      roleStrings.add(role);
    });
    return roleStrings;
  }

  List _formatApprovalData(List data) {
    List datas = [];
    final formatter = DateFormat('yyyy.MM.dd HH:mm:ss');
    for (var e in data) {
      Map<String, dynamic> map = {};
      map['id'] = e['approval']['id'];
      map['avatar'] = e['user']['avatar'];
      map['name'] = e['user']['name'];
      map['role'] = _getRolesString(e['roles']);
      map['phone'] = e['user']['mobile'];
      map['idCard'] = e['user']['id_no'];
      map['time'] = formatter
          .format(DateTime.parse(e['approval']['created_at']).toLocal());

      if (selectedIndex == 0) {
        map['remarks'] = null;
      } else if (segmentIndex == 1) {
        map['remarks'] = e['user']['note'];
      }
      datas.add(map);
    }
    return datas;
  }

  void _dataHasMore(List? datas) {
    if (datas == null) {
      hasMore = false;
    } else {
      if (datas.length >= size) {
        hasMore = true;
      } else {
        hasMore = false;
      }
    }
  }

  void _onRefresh() {
    EasyLoading.show(status: '请稍后');
    page = 1;
    if (selectedIndex == 0) {
      UserServiceProvider()
          .getApprovalList('REVIEWING', page: page, size: size)
          .then((value) {
        EasyLoading.dismiss();
        if (value?.code != 0) {
          ToastUtils.show(value?.msg ?? '');
          if (value?.code == 1) {
            NavigatorUtils.pop(context);
          }
          return;
        }
        List datas = value?.data ?? [];
        _dataHasMore(datas);
        pendingItems = _formatApprovalData(datas);
        setState(() {});
      });
    } else {
      if (segmentIndex == 0) {
        UserServiceProvider()
            .getApprovalList('REVIEW_PASS', page: page, size: size)
            .then((value) {
          EasyLoading.dismiss();
          List datas = value?.data ?? [];
          _dataHasMore(datas);
          approvedItems = _formatApprovalData(datas);
          setState(() {});
        });
      } else {
        UserServiceProvider()
            .getApprovalList('REVIEW_REJECT', page: page, size: size)
            .then((value) {
          EasyLoading.dismiss();
          List datas = value?.data ?? [];
          _dataHasMore(datas);
          rejectedItems = _formatApprovalData(datas);
          setState(() {});
        });
      }
    }
  }

  void _onLoading() {
    page++;
    if (selectedIndex == 0) {
      UserServiceProvider()
          .getApprovalList('REVIEWING', page: page, size: size)
          .then((value) {
        List? datas = value?.data;
        _dataHasMore(datas);
        if (datas != null) {
          pendingItems.add(_formatApprovalData(datas));
          setState(() {});
        }
      });
    } else {
      if (segmentIndex == 0) {
        UserServiceProvider()
            .getApprovalList('REVIEW_PASS', page: page, size: size)
            .then((value) {
          List? datas = value?.data;
          _dataHasMore(datas);
          if (datas != null) {
            approvedItems.add(_formatApprovalData(datas));
            setState(() {});
          }
        });
      } else {
        UserServiceProvider()
            .getApprovalList('REVIEW_REJECT', page: page, size: size)
            .then((value) {
          List? datas = value?.data;
          _dataHasMore(datas);
          if (datas != null) {
            rejectedItems.add(_formatApprovalData(datas));
            setState(() {});
          }
        });
      }
    }
  }

  // bool _hasData() {
  //   bool hasData = false;
  //
  //   if (selectedIndex == 0) {
  //     hasData = pendingItems.isNotEmpty;
  //   } else {
  //     if (segmentIndex == 0) {
  //       hasData = approvedItems.isNotEmpty;
  //     } else {
  //       hasData = rejectedItems.isNotEmpty;
  //     }
  //   }
  //   return hasData;
  // }

  @override
  void initState() {
    super.initState();

    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FD),
      appBar: const CommonNav(title: '我的审批'),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // White background containing the search bar and tabs
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 23),
            child: Column(
              children: [
                // Search bar
                TextField(
                  onChanged: (value) {
                    setState(() {
                      searchQuery = value;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: '请输入搜索内容',
                    hintStyle: const TextStyle(
                      color: Color(0xFFCCCCCC),
                      fontSize: 15,
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF333333),
                        width: 1.5,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF333333),
                        width: 1.5,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: Color(0xFF333333),
                        width: 1.5,
                      ),
                    ),
                    suffixIcon: const Icon(Icons.search),
                  ),
                  style: const TextStyle(fontSize: 15),
                ),
                const SizedBox(height: 22),
                // Reusable Tabs section
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ReusableTab(
                      svgPath: 'assets/svg/mine/pending.svg',
                      title: '待审批',
                      isSelected: selectedIndex == 0,
                      onTap: () {
                        setState(() {
                          selectedIndex = 0;
                        });
                        _onRefresh();
                      },
                    ),
                    const SizedBox(width: 15), // Spacing between tabs
                    ReusableTab(
                      svgPath: 'assets/svg/mine/completed.svg',
                      title: '已完成',
                      isSelected: selectedIndex == 1,
                      onTap: () {
                        setState(() {
                          selectedIndex = 1;
                        });
                        _onRefresh();
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (selectedIndex ==
              1) // Show segment control only if "已完成" is selected
            Padding(
              padding: const EdgeInsets.only(
                  left: 20, right: 16, top: 10), // Adjusted for spacing
              child: Row(
                mainAxisAlignment:
                    MainAxisAlignment.start, // Align items to the left
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        segmentIndex = 0; // "通过" selected
                      });
                      _onRefresh();
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment
                          .center, // Center the text and indicator
                      children: [
                        Text(
                          '通过',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: segmentIndex == 0
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: segmentIndex == 0
                                ? AppTheme.colorPrimaryBlack
                                : const Color(0xFF909090),
                          ),
                        ),
                        const SizedBox(
                            height: 8), // Space between text and indicator

                        Container(
                          width: 15, // Adjusted width
                          height: 4, // Adjusted height
                          decoration: BoxDecoration(
                            color: (segmentIndex == 0)
                                ? AppTheme.colorBlue
                                : Colors.transparent,
                            borderRadius:
                                BorderRadius.circular(2), // Rounded corners
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20), // Spacing between "通过" and "驳回"
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        segmentIndex = 1; // "驳回" selected
                      });
                      _onRefresh();
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment
                          .center, // Center the text and indicator
                      children: [
                        Text(
                          '驳回',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: segmentIndex == 1
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: segmentIndex == 1
                                ? AppTheme.colorPrimaryBlack
                                : const Color(0xFF909090),
                          ),
                        ),
                        const SizedBox(
                            height: 8), // Space between text and indicator

                        Container(
                          width: 15, // Adjusted width
                          height: 4, // Adjusted height
                          decoration: BoxDecoration(
                            color: (segmentIndex == 1)
                                ? AppTheme.colorBlue
                                : Colors.transparent,
                            borderRadius:
                                BorderRadius.circular(2), // Rounded corners
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          // List section
          Expanded(
              child: filteredItems.isEmpty
                  ? _noDataWidget()
                  : EasyRefresh.builder(
                      onRefresh: () => _onRefresh(),
                      onLoad: () => _onLoading(),
                      controller: _refreshController,
                      childBuilder: (_, physic) => ListView.builder(
                        physics: physic,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 16),
                        itemCount: filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = filteredItems[index];
                          return ApprovalCell(
                            avatar: item['avatar'],
                            name: item['name']!,
                            role: item['role']!,
                            phone: item['phone']!,
                            idCard: item['idCard']!,
                            time: item['time']!,
                            showButtons: selectedIndex ==
                                0, // Only show buttons for pending
                            remarks:
                                item['remarks'], // Remarks for rejected cases
                            id: item['id'],
                            callback: () {
                              _onRefresh();
                            },
                          );
                        },
                      ),
                    )),
        ],
      ),
    );
  }

  Widget _noDataWidget() {
    return SizedBox(
      width: ScreenUtil().screenWidth,
      child: Column(
        children: [
          SizedBox(height: 91.h),
          Image.asset('assets/png/mine/approval_no_data.png',
              width: 126.w, height: 133.h),
          SizedBox(height: 14.5.h),
          Text('暂无数据，这里空空如也～',
              style: TextStyle(color: Color(0xFFB0B5BF), fontSize: 14.sp)),
        ],
      ),
    );
  }
}
