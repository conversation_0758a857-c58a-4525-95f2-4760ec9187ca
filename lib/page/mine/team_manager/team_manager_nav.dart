import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class TeamManagerNav extends StatelessWidget {
  final String title;
  final Color? iconColor;
  final Color? titleColor;
  final Widget? leftWidget;
  const TeamManagerNav(
      {super.key,
      required this.title,
      this.iconColor,
      this.titleColor,
      this.leftWidget});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
      child: Container(
        width: ScreenUtil().screenWidth,
        height: 88.h - ScreenUtil().statusBarHeight,
        color: Colors.transparent,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Text(
              title,
              style: TextStyle(
                      color: titleColor ?? const Color(0xFF000000),
                      fontSize: 18.sp)
                  .pfSemiBold,
            ),
            Positioned(
                left: 3.w,
                child: leftWidget ??
                    IconButton(
                      onPressed: () {
                        NavigatorUtils.pop(context);
                      },
                      icon: Icon(Icons.arrow_back_ios,
                          color: iconColor ?? const Color(0xFF000000)),
                      iconSize: 18,
                    ))
          ],
        ),
      ),
    );
  }
}
