import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/upgrade_dialog.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:package_info/package_info.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutUsPage extends StatefulWidget {
  @override
  _AboutUsPageState createState() => _AboutUsPageState();
}

class _AboutUsPageState extends State<AboutUsPage> {
  String? _currentVersion;
  String? _buildNumber;
  String _serverVersion = '1.1.0';
  Map? upgradeInfo;
  Map? storeInfo;

  @override
  void initState() {
    super.initState();
    _initPackageInfo();
    _getUpgradeInfo();
  }

  void _getUpgradeInfo() async {
    ResultData? data = await UserServiceProvider().getVersionInfo();
    if (data?.code == '200') {
      upgradeInfo = data?.data;
      if (upgradeInfo?['need_update'] == true) {
        _serverVersion = data?.data['version'];
      }
      setState(() {});
    }
  }

  String getManufacturerName(String brand) {
    String paramBrand = 'XIAOMI';
    switch (brand.toUpperCase()) {
      case 'HUAWEI':
        paramBrand = "华为";
      case 'HONOR':
        paramBrand = "荣耀";
      case 'XIAOMI':
      case 'REDMI':
      case 'MI':
        paramBrand = "XIAOMI";
      case 'ONEPLUS':
      case 'OPPO':
        paramBrand = "OPPO";
      case 'VIVO':
        paramBrand = "VIVO";
      default:
        paramBrand = "XIAOMI";
    }
    return paramBrand;
  }

  void _upgradeApp() {
    showDialog(
        context: context,
        builder: (_) {
          return UpgradeDialog(
            data: upgradeInfo,
            storeData: storeInfo,
          );
        });
  }

  Future<void> _initPackageInfo() async {
    final PackageInfo info = await PackageInfo.fromPlatform();
    setState(() {
      _currentVersion = info.version;
      _buildNumber = info.buildNumber;
    });
  }

  void _updateApp() async {
    String storeName = '未知';
    if (Platform.isIOS) {
      storeName = 'AppStore';
    } else if (Platform.isAndroid) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      storeName = getManufacturerName(androidInfo.manufacturer);
    }
    ResultData? storeData =
        await UserServiceProvider().getStoreInfo(_serverVersion, storeName);
    storeInfo = storeData?.data;
    if (storeData?.code == '200') {
      if (Platform.isIOS) {
        if (storeData?.data['approve'] == "1") {
          _upgradeApp();
        }
      } else if (Platform.isAndroid) {
        _upgradeApp();
      }
    }
  }

  void _openICP() async {
    const icpUrl = 'https://beian.miit.gov.cn/';
    if (await canLaunchUrl(Uri.parse(icpUrl))) {
      await launchUrl(Uri.parse(icpUrl));
    } else {
      print('Could not launch ICP URL');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonNav(title: '关于我们'),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            children: [
              const SizedBox(height: 77),
              Center(
                child: Image.asset(
                  'assets/png/playstore-icon.png',
                  width: 60,
                  height: 60,
                ),
              ),
              const SizedBox(height: 28),
              Text(
                '新途径人',
                style: AppTheme.getTextStyle(
                    baseSize: 18,
                    color: AppTheme.colorPrimaryBlack,
                    fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Text(
                'Version $_currentVersion',
                style: AppTheme.getTextStyle(
                  baseSize: 13,
                  color: AppTheme.colorPrimaryBlack,
                ).pfMedium,
              ),
              const SizedBox(height: 8),
              (upgradeInfo?['need_update'] == false)
                  ? Text(
                      '已是最新版本',
                      style: AppTheme.getTextStyle(
                        baseSize: 12,
                        color: AppTheme.colorLightGrey2,
                      ).pfMedium,
                    )
                  : GestureDetector(
                      onTap: _updateApp,
                      child: Text('立即更新',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF0054FF),
                            decoration: TextDecoration.underline,
                            decorationColor:
                                Colors.blue, // Set underline color to blue
                          ).pfMedium),
                    ),
              const SizedBox(height: 100), // Space before the slogan
              Text(
                '成为\n受人尊重的教育品牌！',
                textAlign: TextAlign.center,
                style: AppTheme.getTextStyle(
                  baseSize: 13,
                  color: AppTheme.colorPrimaryBlack,
                ).pfMedium,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 70),
            child: Column(
              children: [
                Text(
                  '北京新途径教育科技有限公司 版权所有',
                  style: AppTheme.getTextStyle(
                    baseSize: 11,
                    color: AppTheme.colorLightGrey2,
                  ).pfMedium,
                ),
                SizedBox(
                  height: 6,
                ),
                GestureDetector(
                  onTap: _openICP,
                  child: Text(
                    '京ICP备18044390号-41A',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF0054FF),
                      decoration: TextDecoration.underline,
                      decorationColor:
                          Colors.blue, // Set underline color to blue
                    ).pfMedium,
                  ),
                ),
                SizedBox(
                  height: 6,
                ),
                Text(
                  'Copyright © 2012-${DateTime.now().year} All Rights Reserved.',
                  style: AppTheme.getTextStyle(
                    baseSize: 11,
                    color: AppTheme.colorLightGrey2,
                  ).pfMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
