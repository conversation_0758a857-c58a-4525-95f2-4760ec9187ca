import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';
import 'package:npemployee/model/knowledge_contest/paper_data_model.dart';
import 'package:npemployee/model/mine/team_manager_person_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/mine/employee_info_section.dart';
import 'package:npemployee/widget/study/all_moth_week_day_item.dart';

class TranscriptPage extends StatefulWidget {
  final int paperId;
  const TranscriptPage({super.key, required this.paperId});

  @override
  State<TranscriptPage> createState() => _TranscriptPageState();
}

class _TranscriptPageState extends State<TranscriptPage> {
  int _tabIndex = 1;
  List _tabs = [];

  PaperDataModel? _paperData;

  List<DepartmentModel> departmentModels = [];
  List<MenuItemButton> get dropItems =>
      departmentModels.map((e) => _menuItem(e)).toList();
  DepartmentModel? selectedValue;
  bool dropIsOpen = false;

  List departRank = [];
  List<PaperAllRank> allRank = [];

  final EasyRefreshController _refreshController = EasyRefreshController(
      controlFinishRefresh: true, controlFinishLoad: true);
  int page = 1;

  void _getPaperData() {
    EasyLoading.show();
    UserServiceProvider().getPaperData(
      widget.paperId,
      cacheCallBack: (data) {},
      successCallBack: (data) {
        EasyLoading.dismiss();
        _formatterPaperData(data);
      },
      errorCallBack: (error) {
        EasyLoading.dismiss();
      },
    );
  }

  void _formatterPaperData(ResultData data) {
    _paperData = PaperDataModel.fromJson(data.data);
    setState(() {});
  }

  void _getDepartmentModelsAction() {
    departmentModels.clear();
    List<RolesModel> roles = GlobalPreferences().userInfo?.roles ?? [];
    roles.forEach((role) {
      String d = role.department.name;
      int id = role.department.id;
      List<RolesRolesModel> r = role.roles;
      List<String> myRoles = [];
      r.forEach((e) {
        myRoles.add(e.role.name);
      });
      departmentModels
          .add(DepartmentModel(id: id, name: d, positions: myRoles));
    });
    departmentModels.sort((a, b) => a.id.compareTo(b.id));
    setState(() {
      selectedValue = departmentModels.first;
    });
    _getRank(departId: selectedValue?.id, isRefresh: true);
  }

  void _getRank({int? departId, required bool isRefresh}) async {
    page = isRefresh ? 1 : page + 1;
    ResultData? data = await UserServiceProvider()
        .getPaperRank(widget.paperId, department_id: departId, page: page);
    if (isRefresh) {
      _refreshController.finishRefresh();
      _refreshController.resetFooter();
    } else {
      bool noMore = data?.data['list'].length < 10;
      _refreshController.finishLoad(
          noMore ? IndicatorResult.noMore : IndicatorResult.success);
    }
    if (data?.code == 0) {
      if (isRefresh) {
        allRank.clear();
      }
      for (var element in data?.data['list'] ?? []) {
        allRank.add(PaperAllRank.fromJson(element));
      }
      setState(() {});
    } else {
      EasyLoading.showError('未知错误，请联系管理员');
    }
  }

  @override
  void initState() {
    super.initState();
    _getDepartmentModelsAction();
    _tabs = ['排名', '姓名', '用时', '得分'];
    _getPaperData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          NavigatorUtils.popUntil(context, StudyRouter.knowledgeContest);
          return false; // 返回 false 阻止默认返回行为
        },
        child: Scaffold(
          backgroundColor: const Color(0xFFF7F8FD),
          body: Column(
            children: [
              Stack(
                children: [
                  Image.asset('assets/png/xiaoxin/transcript_top_bac.png',
                      width: ScreenUtil().screenWidth),
                  Positioned(
                      // left: 10.w,
                      top: ScreenUtil().statusBarHeight + 13.h,
                      child: IconButton(
                          onPressed: () {
                            NavigatorUtils.popUntil(
                                context, StudyRouter.knowledgeContest);
                          },
                          icon: const Icon(Icons.arrow_back_ios,
                              color: Colors.white, size: 18))),
                  Positioned(
                      left: 35.w,
                      bottom: 35.h,
                      child: Text(
                        _paperData?.paper_name ?? '',
                        style: TextStyle(
                                color: const Color(0xFF222222), fontSize: 18.sp)
                            .phMedium,
                      ))
                ],
              ),
              SizedBox(height: 16.h),
              Expanded(
                  child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      padding: EdgeInsets.symmetric(
                          vertical: 18.h, horizontal: 24.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  Text(_paperData?.my_score ?? '',
                                      style: TextStyle(
                                              color: const Color(0xFF222222),
                                              fontSize: 30.sp)
                                          .phBold),
                                  Text('/${_paperData?.score_total ?? ''}',
                                      style: TextStyle(
                                              color: const Color(0xFF898989),
                                              fontSize: 12.sp)
                                          .phBold)
                                ],
                              ),
                              SizedBox(height: 12.h),
                              Text('分数',
                                  style: TextStyle(
                                          color: const Color(0xFF999999),
                                          fontSize: 13.sp)
                                      .pfRegular),
                            ],
                          ),
                          Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  Text('${_paperData?.right_total ?? ''}',
                                      style: TextStyle(
                                              color: const Color(0xFF222222),
                                              fontSize: 30.sp)
                                          .phBold),
                                  Text('/${_paperData?.question_total ?? ''}',
                                      style: TextStyle(
                                              color: const Color(0xFF898989),
                                              fontSize: 12.sp)
                                          .phBold)
                                ],
                              ),
                              SizedBox(height: 12.h),
                              Text('答对题数量',
                                  style: TextStyle(
                                          color: const Color(0xFF999999),
                                          fontSize: 13.sp)
                                      .pfRegular),
                            ],
                          ),
                          Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  Text('${_paperData?.rank_with_all ?? ''}',
                                      style: TextStyle(
                                              color: const Color(0xFF222222),
                                              fontSize: 30.sp)
                                          .phBold),
                                  Text(
                                      '/${_paperData?.answer_total_with_all ?? ''}',
                                      style: TextStyle(
                                              color: const Color(0xFF898989),
                                              fontSize: 12.sp)
                                          .phBold)
                                ],
                              ),
                              SizedBox(height: 12.h),
                              Text('成绩排名',
                                  style: TextStyle(
                                          color: const Color(0xFF999999),
                                          fontSize: 13.sp)
                                      .pfRegular),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      padding: EdgeInsets.symmetric(
                          vertical: 18.h, horizontal: 24.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text('答题卡',
                                  style: TextStyle(
                                          color: const Color(0xFF323640),
                                          fontSize: 18.sp)
                                      .pfMedium),
                              const Expanded(child: SizedBox()),
                              _answerCardDesView(const Color(0xFFF3F4F6), '未答'),
                              SizedBox(width: 12.w),
                              _answerCardDesView(const Color(0xFF36D176), '正确'),
                              SizedBox(width: 12.w),
                              _partView(12),
                              SizedBox(width: 3.w),
                              Text('半对',
                                  style: TextStyle(
                                          color: const Color(0xFF8B93A6),
                                          fontSize: 14.sp)
                                      .pfRegular),
                              SizedBox(width: 12.w),
                              _answerCardDesView(const Color(0xFFFE5A59), '错误'),
                            ],
                          ),
                          SizedBox(height: 28.h),
                          // Text('常识判断',
                          //     style: TextStyle(
                          //         color: const Color(0xFF9BA0AD), fontSize: 13.sp)),
                          // SizedBox(height: 16.h),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                ...(_paperData?.answer_sheet ?? []).map((e) {
                                  return GestureDetector(
                                    onTap: () {
                                      NavigatorUtils.push(
                                          context, StudyRouter.answerAnalysis,
                                          arguments: {
                                            'questions':
                                                _paperData?.answer_sheet ?? [],
                                            'index': e.question_number
                                          });
                                    },
                                    child: e.answer_status == AnswerStatus.PART
                                        ? Container(
                                            margin: EdgeInsets.only(
                                                left: e.question_number == 1
                                                    ? 0
                                                    : 28.w),
                                            child: _partView(40,
                                                num: e.question_number),
                                          )
                                        : Container(
                                            alignment: Alignment.center,
                                            margin: EdgeInsets.only(
                                                left: e.question_number == 1
                                                    ? 0
                                                    : 28.w),
                                            width: 40,
                                            height: 40,
                                            decoration: BoxDecoration(
                                                color: e.answer_status ==
                                                        AnswerStatus.NOT
                                                    ? const Color(0xFFF3F4F6)
                                                    : (e.answer_status ==
                                                            AnswerStatus.RIGHT
                                                        ? const Color(
                                                            0xFF36D176)
                                                        : const Color(
                                                            0xFFFE5A59)),
                                                borderRadius:
                                                    BorderRadius.circular(20)),
                                            child: Text('${e.question_number}',
                                                style: TextStyle(
                                                        color:
                                                            e.answer_status ==
                                                                    AnswerStatus
                                                                        .NOT
                                                                ? const Color(
                                                                    0xFF333333)
                                                                : Colors.white,
                                                        fontSize: 18.sp)
                                                    .pfSemiBold),
                                          ),
                                  );
                                }),
                              ],
                            ),
                          ),
                          SizedBox(height: 16.h),
                          GestureDetector(
                            onTap: () {
                              NavigatorUtils.push(
                                  context, StudyRouter.answerAnalysis,
                                  arguments: {
                                    'questions': _paperData?.answer_sheet ?? []
                                  });
                            },
                            child: SizedBox(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text('查看解析',
                                      style: TextStyle(
                                              color: const Color(0xFF9BA0AD),
                                              fontSize: 13.sp)
                                          .pfRegular),
                                  const Icon(
                                    Icons.arrow_forward_ios,
                                    color: Color(0xFF9BA0AD),
                                    size: 13,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Container(
                      height: 500.h,
                      decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(16.r),
                              topRight: Radius.circular(16.r))),
                      child: Column(
                        children: [
                          Padding(
                              padding: EdgeInsets.fromLTRB(16.w, 25.h, 16.w, 0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Stack(
                                    children: [
                                      Positioned(
                                          bottom: 0,
                                          child: Image.asset(
                                              'assets/png/xiaoxin/sort_bac.png',
                                              width: 80.w)),
                                      Text(
                                        '成绩排名', //${DateTimeUtils.formatDataToMMd(DateTime.now())}
                                        style: TextStyle(
                                                color: const Color(0xFF222222),
                                                fontSize: 16.sp)
                                            .pfSemiBold,
                                      )
                                    ],
                                  ),
                                  if (_tabIndex == 1 &&
                                      departmentModels.isNotEmpty)
                                    SizedBox(width: 8.w),
                                  if (_tabIndex == 1 &&
                                      departmentModels.isNotEmpty)
                                    _dropView(),
                                  if (_tabIndex == 1 &&
                                      departmentModels.isNotEmpty)
                                    SizedBox(width: 8.w),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 2.w, vertical: 2.h),
                                    decoration: BoxDecoration(
                                        color: const Color(0xFFF7F8FD),
                                        borderRadius:
                                            BorderRadius.circular(17.5.r)),
                                    child: Row(
                                      children: [
                                        AllMonthWeekDayItem(
                                            onTap: () {
                                              _tabs = ['排名', '姓名', '用时', '得分'];
                                              setState(() {
                                                _tabIndex = 1;
                                              });
                                              _getRank(
                                                  departId: selectedValue?.id,
                                                  isRefresh: true);
                                            },
                                            title: '部门内排名',
                                            itemWith: 88.w,
                                            isSelect: _tabIndex == 1),
                                        AllMonthWeekDayItem(
                                            onTap: () {
                                              _tabs = ['排名', '姓名', '部门', '得分'];
                                              setState(() {
                                                _tabIndex = 2;
                                              });
                                              _getRank(isRefresh: true);
                                            },
                                            title: '总排名',
                                            itemWith: 88.w,
                                            isSelect: _tabIndex == 2),
                                      ],
                                    ),
                                  ),
                                ],
                              )),
                          SizedBox(height: 12.h),
                          Container(
                            padding: EdgeInsets.symmetric(vertical: 11.h),
                            decoration: BoxDecoration(
                                color: const Color(0xFFE2EEFB),
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(16.r),
                                    topRight: Radius.circular(16.r))),
                            child: Row(
                              children: [
                                ..._tabs.map((e) {
                                  int flex = 1;
                                  if (_tabIndex == 2) {
                                    if (e == '部门' || e == '姓名') {
                                      flex = 2;
                                    } else {
                                      flex = 1;
                                    }
                                  }
                                  return Expanded(
                                    flex: flex,
                                    child: Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        e,
                                        style: TextStyle(
                                                color: const Color(0xFF808080),
                                                fontSize: 13.sp)
                                            .pfRegular,
                                      ),
                                    ),
                                  );
                                }),
                              ],
                            ),
                          ),
                          Expanded(
                              child: MediaQuery.removePadding(
                            removeTop: true,
                            context: context,
                            child: EasyRefresh.builder(
                                controller: _refreshController,
                                onRefresh: () {
                                  if (_tabIndex == 1) {
                                    _getRank(
                                        departId: selectedValue?.id,
                                        isRefresh: true);
                                  } else {
                                    _getRank(isRefresh: true);
                                  }
                                },
                                onLoad: () {
                                  if (_tabIndex == 1) {
                                    _getRank(
                                        departId: selectedValue?.id,
                                        isRefresh: false);
                                  } else {
                                    _getRank(isRefresh: false);
                                  }
                                },
                                childBuilder: (_, p) {
                                  return ListView.builder(
                                      physics: p,
                                      itemCount: _tabIndex == 1
                                          ? (_paperData?.department_rank_list
                                                  .length ??
                                              0)
                                          : allRank.length,
                                      itemBuilder: (c, index) {
                                        List<String> itemText = [];
                                        LinearGradient? linearGradient;
                                        if (index == 0) {
                                          linearGradient = const LinearGradient(
                                              begin: Alignment.topCenter,
                                              end: Alignment.bottomCenter,
                                              colors: [
                                                Color(0xFFFFEDE8),
                                                Color(0xFFFFFFFF)
                                              ]);
                                        } else if (index == 1) {
                                          linearGradient = const LinearGradient(
                                              begin: Alignment.topCenter,
                                              end: Alignment.bottomCenter,
                                              colors: [
                                                Color(0xFFFDF4ED),
                                                Color(0xFFFFFFFF)
                                              ]);
                                        } else if (index == 2) {
                                          linearGradient = const LinearGradient(
                                              begin: Alignment.topCenter,
                                              end: Alignment.bottomCenter,
                                              colors: [
                                                Color(0xFFEFF4FF),
                                                Color(0xFFFFFFFF)
                                              ]);
                                        }
                                        if (_tabIndex == 1) {
                                          PaperRankModel? item;
                                          item = _paperData
                                              ?.department_rank_list[index];
                                          itemText = [
                                            '${item?.rank_no}',
                                            item!.name,
                                            DateTimeUtils.formatTime(
                                                item.use_time),
                                            item.score
                                          ];
                                        } else {
                                          PaperAllRank? item;
                                          item = allRank[index];
                                          itemText = [
                                            '${item.rank_no}',
                                            item.name ?? '',
                                            item.departNameSort,
                                            item.score ?? '--',
                                          ];
                                        }
                                        return Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 16.h),
                                          decoration: BoxDecoration(
                                              gradient: linearGradient,
                                              border: Border(
                                                  bottom: BorderSide(
                                                      color: const Color(
                                                              0xFFE6E6E6)
                                                          .withOpacity(0.5),
                                                      width: 0.5))),
                                          child: Row(
                                            children: [
                                              ...itemText.map((e) {
                                                Color textColor =
                                                    const Color(0xFF232323);

                                                TextStyle textStyle = TextStyle(
                                                        color: const Color(
                                                            0xFF232323),
                                                        fontSize: 14.sp)
                                                    .pfRegular;

                                                if (e == '1' &&
                                                    itemText.indexOf(e) == 0) {
                                                  textColor =
                                                      const Color(0xFFFF8A00);
                                                  textStyle = textStyle
                                                      .copyWith(
                                                          color: textColor)
                                                      .pfSemiBold; // 金牌
                                                } else if (e == '2' &&
                                                    itemText.indexOf(e) == 0) {
                                                  textColor = const Color(
                                                      0xFF808080); // 银牌
                                                  textStyle = textStyle
                                                      .copyWith(
                                                          color: textColor)
                                                      .pfSemiBold;
                                                } else if (e == '3' &&
                                                    itemText.indexOf(e) == 0) {
                                                  textColor = const Color(
                                                      0xFFB87A56); // 铜牌
                                                  textStyle = textStyle
                                                      .copyWith(
                                                          color: textColor)
                                                      .pfSemiBold;
                                                }
                                                int flex = 1;
                                                if (_tabIndex == 2) {
                                                  int idx = itemText.indexOf(e);
                                                  if (idx == 1 || idx == 2) {
                                                    flex = 2;
                                                  } else {
                                                    flex = 1;
                                                  }
                                                }
                                                return Expanded(
                                                  flex: flex,
                                                  child: Container(
                                                      // color: Colors.red,
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical: 5.h),
                                                      // decoration: BoxDecoration(
                                                      //     gradient: linearGradient),
                                                      alignment:
                                                          Alignment.center,
                                                      child: Text(e,
                                                          style: textStyle)),
                                                );
                                              }),
                                            ],
                                          ),
                                        );
                                      });
                                }),
                          ))
                        ],
                      ),
                    ),
                  ],
                ),
              ))
            ],
          ),
        ));
  }

  Widget _answerCardDesView(Color color, String title) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6),
            color: color,
          ),
        ),
        SizedBox(width: 3.w),
        Text(title,
            style: TextStyle(color: const Color(0xFF8B93A6), fontSize: 14.sp)
                .pfRegular),
      ],
    );
  }

  Widget _partView(double size, {int? num}) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 半圆绿色覆盖
        ClipOval(
          child: SizedBox(
            width: size,
            height: size,
            child: Transform.rotate(
              angle: 45 * 3.141592653589793 / 180, // 顺时针旋转120度，将角度转换为弧度
              child: ClipOval(
                child: SizedBox(
                  width: size,
                  height: size,
                  child: Stack(
                    children: [
                      // 白色部分
                      Container(
                        color: const Color(0xFFF3F4F6),
                      ),
                      // 左半部分为绿色
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          width: size / 2, // 左半部分宽度
                          color: const Color(0xFF36D176),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        // 圆心数字
        if (num != null)
          Text(
            "$num",
            style: TextStyle(fontSize: 18.sp, color: const Color(0xFF333333))
                .pfSemiBold,
          ),
      ],
    );
  }

  Widget _dropView() {
    return MenuAnchor(
      style: MenuStyle(
        visualDensity: VisualDensity.compact,
        backgroundColor: const WidgetStatePropertyAll(Colors.white),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(color: Color(0xFFDADADA), width: 0.5),
          ),
        ),
        elevation: const WidgetStatePropertyAll(0),
      ),
      builder:
          (BuildContext context, MenuController controller, Widget? child) {
        return GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            controller.isOpen ? controller.close() : controller.open();
          },
          child: Container(
            constraints: BoxConstraints(maxWidth: 75.w),
            child: RichText(
              text: TextSpan(
                  text: selectedValue?.name ?? departmentModels.first.name,
                  style:
                      TextStyle(color: const Color(0xFF000000), fontSize: 12.sp)
                          .pfMedium,
                  children: [
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Image.asset(
                        dropIsOpen
                            ? 'assets/png/xiaoxin/drop_up.png'
                            : 'assets/png/xiaoxin/drop_down.png',
                        width: 15.w,
                        height: 10.h,
                      ),
                    ),
                  ]),
            )
            /* Row(
              children: [
                Text(
                  selectedValue?.name ?? departmentModels.first.name,
                  style:
                      TextStyle(color: const Color(0xFF000000), fontSize: 12.sp)
                          .pfMedium,
                ),
                Image.asset(
                  dropIsOpen
                      ? 'assets/png/xiaoxin/drop_up.png'
                      : 'assets/png/xiaoxin/drop_down.png',
                  width: 15.w,
                  height: 10.h,
                ),
              ],
            ) */
            ,
          ),
        );
      },
      menuChildren: dropItems,
      onClose: () {
        setState(() {
          dropIsOpen = false;
        });
      },
      onOpen: () {
        setState(() {
          dropIsOpen = true;
        });
      },
    );
  }

  MenuItemButton _menuItem(DepartmentModel e) {
    return MenuItemButton(
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(horizontal: 7.5.w, vertical: 8.h),
        constraints: BoxConstraints(maxWidth: 150.w),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color: departmentModels.indexOf(e) ==
                            departmentModels.length - 1
                        ? Colors.transparent
                        : const Color(0xFFE6E6E6).withOpacity(0.5)))),
        child: Text(
          e.name,
          style: TextStyle(
                  color: selectedValue == e
                      ? AppTheme.colorBlue
                      : const Color(0xFF000000),
                  fontSize: 12.sp)
              .pfMedium,
        ),
      ),
      onPressed: () {
        setState(() {
          selectedValue = e;
        });
        _getRank(departId: e.id, isRefresh: true);
      },
    );
  }
}
