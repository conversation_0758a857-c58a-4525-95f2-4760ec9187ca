import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';
import 'package:npemployee/widget/common_nav.dart';

class AnswerAnalysisPage extends StatefulWidget {
  final int? index;
  final List<PaperContentItemModel> questions;
  const AnswerAnalysisPage({super.key, required this.questions, this.index});

  @override
  State<AnswerAnalysisPage> createState() => _AnswerAnalysisPageState();
}

class _AnswerAnalysisPageState extends State<AnswerAnalysisPage> {
  int _index = 0;
  late PageController _pageController;

  List<PaperContentItemModel> questions = [];

  @override
  void initState() {
    super.initState();
    if (widget.index != null) {
      _index = widget.index! - 1;
    }
    _pageController = PageController(initialPage: _index);
    questions = widget.questions;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FD),
      appBar: const CommonNav(title: '答案解析'),
      body: SizedBox(
        width: ScreenUtil().screenWidth,
        height: ScreenUtil().screenHeight,
        child: Stack(
          children: [
            Column(
              children: [
                if (questions.isNotEmpty) _topView(),
                Container(height: 14.h, color: Colors.white),
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    onPageChanged: (i) {
                      setState(() {
                        _index = i;
                      });
                    },
                    itemCount: questions.length,
                    itemBuilder: _itemBuilder,
                  ),
                )
              ],
            ),
            // 底部导航按钮
            if (_index != questions.length - 1)
              Positioned(
                bottom: ScreenUtil().bottomBarHeight,
                child: _buildBottomNavigation(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      width: ScreenUtil().screenWidth,
      height: 90.h,
      padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 46.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Color(0xFFEEEEEE), width: 0.5),
        ),
      ),
      child: _buildNavigationButton('下一题', false, () {
        _pageController.nextPage(
            duration: const Duration(milliseconds: 300), curve: Curves.ease);
      }),
    );
  }

  Widget _buildNavigationButton(
      String text, bool isPrevious, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: isPrevious ? Colors.transparent : const Color(0xFF0054FF),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isPrevious ? const Color(0xFF0054FF) : Colors.white,
            fontSize: 16.sp,
          ),
        ),
      ),
    );
  }

  // 其他辅助方法...
  Widget _topView() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(right: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                'assets/png/xiaoxin/question_muti_bac.png',
                width: 60.w,
                height: 29.h,
              ),
              Text(
                questions[_index].module_name ?? '未知',
                style: TextStyle(
                  color: const Color(0xFF0054FF),
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Image.asset(
                'assets/png/xiaoxin/question_num.png',
                width: 20,
                height: 20,
              ),
              Text(
                '${_index + 1}',
                style: TextStyle(
                  color: const Color(0xFF0054FF),
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '/${questions.length}',
                style: TextStyle(
                  color: const Color(0xFF464B59),
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _itemBuilder(BuildContext context, int index) {
    PaperContentItemModel question = questions[index];
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildQuestionContent(question),
          SizedBox(height: 12.h),
          _buildAnswerSection(question),
          SizedBox(height: 12.h),
          _buildKeyPointsSection(question),
          SizedBox(height: 12.h),
          _buildAnalysisSection(question),
          SizedBox(height: ScreenUtil().bottomBarHeight + 90.h),
        ],
      ),
    );
  }

  // 构建问题内容部分
  Widget _buildQuestionContent(PaperContentItemModel question) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Html(data: question.content),
          SizedBox(height: 10.h),
          ...question.options.map((e) => _buildOptionItem(question, e)),
        ],
      ),
    );
  }

  Widget _buildAnswerSection(PaperContentItemModel question) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 4.w,
                height: 16.h,
                decoration: BoxDecoration(
                    color: const Color(0xFF0054FF),
                    borderRadius: BorderRadius.circular(2.r)),
              ),
              SizedBox(width: 7.w),
              Text('答案',
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 18.sp)
                          .pfMedium),
            ],
          ),
          SizedBox(height: 22.h),
          Row(
            children: [
              Text('正确答案',
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 14.sp)
                          .pfMedium),
              SizedBox(width: 13.w),
              Text(
                '${question.answer.join(',')}',
                style:
                    TextStyle(color: const Color(0xFF36D176), fontSize: 18.sp)
                        .pfMedium,
              ),
              if (question.selected.isNotEmpty) SizedBox(width: 22.h),
              if (question.selected.isNotEmpty)
                Text('您的答案',
                    style: TextStyle(
                            color: const Color(0xFF333333), fontSize: 14.sp)
                        .pfMedium),
              SizedBox(width: 13.w),
              Text(
                question.selected.join(','),
                style:
                    TextStyle(color: const Color(0xFFFE5A59), fontSize: 18.sp)
                        .pfMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKeyPointsSection(PaperContentItemModel question) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4.w,
                height: 16.h,
                decoration: BoxDecoration(
                    color: const Color(0xFF0054FF),
                    borderRadius: BorderRadius.circular(2.r)),
              ),
              SizedBox(width: 7.w),
              Text('考点',
                  style:
                      TextStyle(color: const Color(0xFF333333), fontSize: 18.sp)
                          .pfMedium),
            ],
          ),
          SizedBox(height: 16.h),
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: [
              ...question.key_point.map((e) {
                return Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                      color: const Color(0xFFE8F0FF),
                      borderRadius: BorderRadius.circular(10.r)),
                  child: Text(
                    e,
                    style: TextStyle(
                            color: const Color(0xFF0054FF), fontSize: 14.sp)
                        .pfMedium,
                  ),
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisSection(PaperContentItemModel question) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 4.w,
                  height: 16.h,
                  decoration: BoxDecoration(
                      color: const Color(0xFF0054FF),
                      borderRadius: BorderRadius.circular(2.r)),
                ),
                SizedBox(width: 7.w),
                Text('解析',
                    style: TextStyle(
                            color: const Color(0xFF333333), fontSize: 18.sp)
                        .pfMedium),
              ],
            ),
            SizedBox(height: 19.h),
            Html(data: question.analysis),
          ],
        ));
  }

  // 构建选项显示
  Widget _buildOptionItem(
      PaperContentItemModel question, Map<String, dynamic> option) {
    bool showResult = question.answer_status != AnswerStatus.NOT;
    bool isSelected = question.selected.contains(option['option_key']);
    bool isCorrectAnswer = question.answer.contains(option['option_key']);

    Color backgroundColor = Colors.transparent;
    Color textColor = const Color(0xFF333333);
    Color? borderColor;

    Color rightColor = const Color(0xFF36D176);
    Color wrongColor = const Color(0xFFFE5A59);

    if (showResult) {
      if (question.question_category == QuestionCategory.SINGLE ||
          question.question_category == QuestionCategory.TF) {
        if (isCorrectAnswer) {
          backgroundColor = rightColor;
          textColor = Colors.white;
        } else if (isSelected) {
          backgroundColor = wrongColor;
          textColor = Colors.white;
        }
      } else if (question.question_category == QuestionCategory.MULTIPLE) {
        if (isSelected && isCorrectAnswer) {
          // 选中且正确，绿色背景
          backgroundColor = rightColor;
          textColor = Colors.white;
        } else if (isSelected && !isCorrectAnswer) {
          // 选中且错误，红色背景
          backgroundColor = wrongColor;
          textColor = Colors.white;
        } else if (!isSelected && isCorrectAnswer) {
          // 漏选且是正确答案，边框绿色，文字绿色,白色背景
          textColor = rightColor;
          borderColor = rightColor;
        }
        //漏选且不是正确答案，保持不变
      }
    } else if (isSelected) {
      backgroundColor = const Color(0xFF0054FF);
      textColor = Colors.white;
    }

    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(vertical: 10.h),
      child: Row(
        children: [
          Container(
            alignment: Alignment.center,
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(
                question.question_category == QuestionCategory.SINGLE ||
                        question.question_category == QuestionCategory.TF
                    ? 20
                    : 8.r,
              ),
              border: Border.all(
                  color: borderColor ?? const Color(0xFFCCCCCC),
                  width: borderColor != null ? 1.5 : 0.5),
            ),
            child: Text(
              option['option_key'] ?? '',
              style: TextStyle(color: textColor, fontSize: 18.sp).pfMedium,
            ),
          ),
          SizedBox(width: 15.w),
          Expanded(child: Html(data: option['option_value'] ?? '')),
        ],
      ),
    );
  }
}
