import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/common_nav.dart';

class AnswerQuestionPage extends StatefulWidget {
  final int paperId;
  final String paperStatus;
  final int limitTime;
  const AnswerQuestionPage(
      {super.key,
      required this.paperId,
      required this.paperStatus,
      required this.limitTime});

  @override
  State<AnswerQuestionPage> createState() => _AnswerQuestionPageState();
}

class _AnswerQuestionPageState extends State<AnswerQuestionPage> {
  int _index = 0;
  int _seconds = 0;
  Timer? _timer;
  late PageController _pageController;
  double _startPosition = 0.0; // 记录手势起始位置

  PaperContentModel? paperContent;
  List<PaperContentItemModel> questions = [];

  bool get needTimer => widget.limitTime > 0;

  void _timerStart() {
    int count = 0;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _seconds--;
        count++;
        // 每3秒上传一次数据
        if (count >= 3) {
          count = 0;
          debugPrint(
              '每3秒上传一次数据, _seconds: $_seconds, useTime: ${widget.limitTime - _seconds}');
          UserServiceProvider().submitExamConsumeTime(
              paperContent!.answer_record_id, widget.limitTime - _seconds);
        }

        if (_seconds == 0) {
          timer.cancel();
          NavigatorUtils.push(context, StudyRouter.transcript,
              arguments: {'paperId': widget.paperId});
        }
      });
    });
  }

  String _formatTime() {
    int minutes = _seconds ~/ 60; // 计算分钟数
    int remainingSeconds = _seconds % 60; // 计算剩余的秒数

    // 格式化成 mm:ss 格式
    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = remainingSeconds.toString().padLeft(2, '0');

    return "$formattedMinutes:$formattedSeconds";
  }

  void _getPaperContent(bool isInit) {
    EasyLoading.show();
    UserServiceProvider().getPaperContent(widget.paperId,
        cacheCallBack: (result) {
      // _formatPaterContentData(result, true);
    }, successCallBack: (result) {
      EasyLoading.dismiss();
      _formatPaterContentData(result, false, isInit);
    }, errorCallBack: (err) {
      EasyLoading.dismiss();
      _formatPaterContentData(err, false, isInit);
    });
  }

  void _formatPaterContentData(ResultData result, bool isCache, bool isInit) {
    questions.clear();
    /* Map json = {
      "code": 0,
      "data": {
        "question_total": 5,
        "max_wrong": 0,
        "answer_record_id": 2,
        "paper_name": "前后端对接测试",
        "score_total": '10',
        "score_pass": '8',
        'limit_time': 600,
        'max_retake': 3,
        "list": [
          {"paper_id": 5, "item_tag": "MODULE", "module_name": "单选题"},
          {
            "paper_content_id": 3,
            "paper_id": 5,
            "question_id": 2,
            "question_number": 1,
            "question_limit_time": 0,
            "module_name": "单选题",
            "question_score": "2",
            "question_category": "SINGLE",
            "content": "<p>第一题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>A选项</p>"},
              {"option_key": "B", "option_value": "<p>B选项</p>"},
              {"option_key": "C", "option_value": "<p>C选项</p>"},
              {"option_key": "D", "option_value": "<p>D选项</p>"}
            ],
            "answer": ["A"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "RIGHT",
            "selected": ["A"]
          },
          {
            "paper_content_id": 3,
            "paper_id": 5,
            "question_id": 2,
            "question_number": 1,
            "question_limit_time": 0,
            "module_name": "单选题",
            "question_score": "2",
            "question_category": "SINGLE",
            "content": "<p>第一1题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>A选项</p>"},
              {"option_key": "B", "option_value": "<p>B选项</p>"},
              {"option_key": "C", "option_value": "<p>C选项</p>"},
              {"option_key": "D", "option_value": "<p>D选项</p>"}
            ],
            "answer": ["A"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "WRONG",
            "selected": ["B"]
          },
          {"paper_id": 5, "item_tag": "MODULE", "module_name": "多选题"},
          {
            "paper_content_id": 4,
            "paper_id": 5,
            "question_id": 3,
            "question_number": 2,
            "question_limit_time": 0,
            "module_name": "多选题",
            "question_score": "2",
            "question_category": "MULTIPLE",
            "content": "<p>第二题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>A选项</p>"},
              {"option_key": "B", "option_value": "<p>B选项</p>"},
              {"option_key": "C", "option_value": "<p>C选项</p>"},
              {"option_key": "D", "option_value": "<p>D选项</p>"},
              {"option_key": "E", "option_value": "<p>E选项</p>"}
            ],
            "answer": ["A", "B", "C"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "WRONG",
            "selected": ["D"]
          },
          {
            "paper_content_id": 4,
            "paper_id": 5,
            "question_id": 3,
            "question_number": 2,
            "question_limit_time": 0,
            "module_name": "多选题",
            "question_score": "2",
            "question_category": "MULTIPLE",
            "content": "<p>第二1题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>A选项</p>"},
              {"option_key": "B", "option_value": "<p>B选项</p>"},
              {"option_key": "C", "option_value": "<p>C选项</p>"},
              {"option_key": "D", "option_value": "<p>D选项</p>"},
              {"option_key": "E", "option_value": "<p>E选项</p>"}
            ],
            "answer": ["A", "B", "C"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "WRONG",
            "selected": ["A", "D"]
          },
          {
            "paper_content_id": 4,
            "paper_id": 5,
            "question_id": 3,
            "question_number": 2,
            "question_limit_time": 0,
            "module_name": "多选题",
            "question_score": "2",
            "question_category": "MULTIPLE",
            "content": "<p>第二2题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>A选项</p>"},
              {"option_key": "B", "option_value": "<p>B选项</p>"},
              {"option_key": "C", "option_value": "<p>C选项</p>"},
              {"option_key": "D", "option_value": "<p>D选项</p>"},
              {"option_key": "E", "option_value": "<p>E选项</p>"}
            ],
            "answer": ["A", "B", "C"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "RIGHT",
            "selected": ["A", "B", "C"]
          },
          {
            "paper_content_id": 4,
            "paper_id": 5,
            "question_id": 3,
            "question_number": 2,
            "question_limit_time": 0,
            "module_name": "多选题",
            "question_score": "2",
            "question_category": "MULTIPLE",
            "content": "<p>第二2题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>A选项</p>"},
              {"option_key": "B", "option_value": "<p>B选项</p>"},
              {"option_key": "C", "option_value": "<p>C选项</p>"},
              {"option_key": "D", "option_value": "<p>D选项</p>"},
              {"option_key": "E", "option_value": "<p>E选项</p>"}
            ],
            "answer": ["A", "B", "C"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "PART",
            "selected": ["A", "B"]
          },
          {
            "paper_content_id": 5,
            "paper_id": 5,
            "question_id": 4,
            "question_number": 3,
            "question_limit_time": 0,
            "module_name": "多选题",
            "question_score": "2",
            "question_category": "MULTIPLE",
            "content": "<p>第三题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>A选项</p>"},
              {"option_key": "B", "option_value": "<p>B选项</p>"},
              {"option_key": "C", "option_value": "<p>C选项</p>"},
              {"option_key": "D", "option_value": "<p>D选项</p>"},
              {"option_key": "E", "option_value": "<p>E选项</p>"}
            ],
            "answer": ["A", "B", "C"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "WRONG",
            "selected": ["A", "B", "D", "E"]
          },
          {
            "paper_content_id": 6,
            "paper_id": 5,
            "question_id": 5,
            "question_number": 4,
            "question_limit_time": 0,
            "module_name": "多选题",
            "question_score": "2",
            "question_category": "MULTIPLE",
            "content": "<p>第四题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>A选项</p>"},
              {"option_key": "B", "option_value": "<p>B选项</p>"},
              {"option_key": "C", "option_value": "<p>C选项</p>"},
              {"option_key": "D", "option_value": "<p>D选项</p>"},
              {"option_key": "E", "option_value": "<p>E选项</p>"}
            ],
            "answer": ["A", "B", "C"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "NOT",
            "selected": []
          },
          {"paper_id": 5, "item_tag": "MODULE", "module_name": "判断题"},
          {
            "paper_content_id": 7,
            "paper_id": 5,
            "question_id": 6,
            "question_number": 5,
            "question_limit_time": 0,
            "module_name": "判断题",
            "question_score": "2",
            "question_category": "TF",
            "content": "<p>第五题</p>",
            "options": [
              {"option_key": "A", "option_value": "<p>正确</p>"},
              {"option_key": "B", "option_value": "<p>错误</p>"}
            ],
            "answer": ["A"],
            "key_point": ["考点1", "考点2"],
            "analysis": "<p>这是解析</p>",
            "answer_status": "NOT",
            "selected": []
          }
        ]
      },
      "msg": "success"
    }; */
    // paperContent = PaperContentModel.fromJson(json['data']);
    paperContent = PaperContentModel.fromJson(result.data);
    questions = paperContent!.list;
    if (widget.paperStatus == 'CONTINUE') {
      if (isInit) {
        _index = questions.indexWhere((e) => e.selected.isEmpty);
        if (_index == -1) {
          _index = 0;
          _pageController = PageController(initialPage: 0);
        } else {
          _pageController = PageController(initialPage: _index);
        }
      }
    } else {
      if (isInit) {
        _index = 0;
        _pageController = PageController(initialPage: 0);
      }
    }

    if (isInit) {
      if (needTimer) {
        _timerStart();
      }
    }

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _nextAction() {
    if (questions[_index].answer_status == AnswerStatus.NOT) {
      //提交答题数据并刷新页面
      EasyLoading.show();
      UserServiceProvider()
          .submitAnswer(
              paperContent!.answer_record_id,
              questions[_index].question_id,
              questions[_index].paper_id,
              questions[_index].selected,
              _seconds)
          .then((value) {
        EasyLoading.dismiss();
        if (value?.code == 0) {
          for (var element in questions) {
            if (element.question_id == value?.data['question_id']) {
              AnswerStatus answer_status = AnswerStatus.NOT;
              if (value?.data['answer_status'] == 'PART') {
                answer_status = AnswerStatus.PART;
              } else if (value?.data['answer_status'] == 'WRONG') {
                answer_status = AnswerStatus.WRONG;
              } else if (value?.data['answer_status'] == 'RIGHT') {
                answer_status = AnswerStatus.RIGHT;
              }
              element.answer_status = answer_status;
            }
          }
          if (value?.data['answer_status'] == 'RIGHT' &&
              _index != questions.length - 1) {
            _pageController.nextPage(
                duration: const Duration(milliseconds: 300),
                curve: Curves.ease);
          }
          setState(() {});
        } else {
          EasyLoading.showError(value?.msg ?? '出错了，请联系管理员');
        }
      });
    } else {
      if (_index == questions.length - 1) {
        if (_timer != null && _timer!.isActive) {
          _timer!.cancel();
          _timer = null;
        }
        NavigatorUtils.push(context, StudyRouter.transcript,
            arguments: {'paperId': widget.paperId});
      } else {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.ease,
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getPaperContent(true);
    _seconds = widget.limitTime;
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: paperContent == null
            ? Colors.white
            : (questions[_index].answer_status == AnswerStatus.WRONG ||
                    questions[_index].answer_status == AnswerStatus.PART)
                ? const Color(0xFFF7F8FD)
                : Colors.white,
        appBar: CommonNav(title: paperContent?.paper_name ?? ''),
        body: paperContent == null
            ? Container()
            : SizedBox(
                width: ScreenUtil().screenWidth,
                height: ScreenUtil().screenHeight,
                child: Stack(
                  children: [
                    Column(
                      children: [
                        if (questions.isNotEmpty) _topView(),
                        Container(height: 14.h, color: Colors.white),
                        Expanded(
                          child: GestureDetector(
                            onHorizontalDragStart: (details) {
                              _startPosition = details.globalPosition.dx;
                            },
                            onHorizontalDragEnd: (details) {
                              double delta =
                                  details.globalPosition.dx - _startPosition;

                              if (delta < -30) {
                                if (questions[_index].answer_status ==
                                    AnswerStatus.NOT) {
                                  return;
                                } else {
                                  if (_index < questions.length - 1) {
                                    _pageController.nextPage(
                                      duration:
                                          const Duration(milliseconds: 300),
                                      curve: Curves.ease,
                                    );
                                  }
                                }
                              } else if (delta > 30) {
                                if (_index > 0) {
                                  _pageController.previousPage(
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.ease,
                                  );
                                }
                              }
                            },
                            child: PageView.builder(
                                controller: _pageController,
                                onPageChanged: (i) {
                                  setState(() {
                                    _index = i;
                                  });
                                },
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: questions.length,
                                itemBuilder: _itemBuilder),
                          ),
                        )
                      ],
                    ),
                    if (questions[_index].selected.isNotEmpty)
                      Positioned(
                          bottom: ScreenUtil().bottomBarHeight,
                          child: Container(
                            width: ScreenUtil().screenWidth,
                            height: 90.h,
                            padding: EdgeInsets.symmetric(
                                vertical: 16.h, horizontal: 46.w),
                            decoration: const BoxDecoration(
                                color: Colors.white,
                                border: Border(
                                    top: BorderSide(
                                        color: Color(0xFFEEEEEE), width: 0.5))),
                            child: GestureDetector(
                              onTap: () => _nextAction(),
                              child: AnimatedContainer(
                                alignment: Alignment.center,
                                duration: const Duration(milliseconds: 200),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(16.r),
                                    color: const Color(0xFF0054FF)),
                                child: Text(
                                    _index == questions.length - 1 &&
                                            questions[_index].answer_status !=
                                                AnswerStatus.NOT
                                        ? '完成'
                                        : (questions[_index]
                                                        .question_category ==
                                                    QuestionCategory.SINGLE ||
                                                questions[_index]
                                                        .question_category ==
                                                    QuestionCategory.TF)
                                            ? '下一题'
                                            : questions[_index].answer_status ==
                                                    AnswerStatus.NOT
                                                ? '确定'
                                                : '下一题',
                                    style: TextStyle(
                                            color: const Color(0xFFFFFFFF),
                                            fontSize: 16.sp)
                                        .pfMedium),
                              ),
                            ),
                          )),
                  ],
                ),
              ));
  }

  Widget _topView() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(right: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Image.asset('assets/png/xiaoxin/question_muti_bac.png',
                  width: 60.w, height: 29.h),
              Text(questions[_index].module_name ?? '未知',
                  style:
                      TextStyle(color: const Color(0xFF0054FF), fontSize: 12.sp)
                          .pfRegular),
            ],
          ),
          Row(
            children: [
              Image.asset('assets/png/xiaoxin/question_time.png',
                  width: 20, height: 20),
              Text(
                _formatTime(),
                style:
                    TextStyle(color: const Color(0xFF464B59), fontSize: 16.sp)
                        .pfMedium,
              ),
              SizedBox(width: 25.w),
              Image.asset('assets/png/xiaoxin/question_num.png',
                  width: 20, height: 20),
              Text(
                '${_index + 1}',
                style:
                    TextStyle(color: const Color(0xFF0054FF), fontSize: 16.sp)
                        .pfMedium,
              ),
              Text(
                '/${questions.length}',
                style:
                    TextStyle(color: const Color(0xFF464B59), fontSize: 16.sp)
                        .pfMedium,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _itemBuilder(c, index) {
    PaperContentItemModel question = questions[index];
    bool isError = question.answer_status == AnswerStatus.WRONG ||
        question.answer_status == AnswerStatus.PART;
    return SingleChildScrollView(
      child: Column(children: [
        Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(isError ? 16.r : 0),
                  bottomRight: Radius.circular(isError ? 16.r : 0))),
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Html(data: question.content),
              SizedBox(height: 10.h),
              ...question.options.map((e) {
                return _choiceView(question, e);
              }),
            ],
          ),
        ),
        if (isError) SizedBox(height: 12.h),
        if (isError)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 4.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                          color: const Color(0xFF0054FF),
                          borderRadius: BorderRadius.circular(2.r)),
                    ),
                    SizedBox(width: 7.w),
                    Text('答案',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 18.sp)
                            .pfMedium),
                  ],
                ),
                SizedBox(height: 22.h),
                Row(
                  children: [
                    Text('正确答案',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 14.sp)
                            .pfMedium),
                    SizedBox(width: 13.w),
                    Text(
                      '${question.answer.join(',')}',
                      style: TextStyle(
                              color: const Color(0xFF36D176), fontSize: 18.sp)
                          .pfMedium,
                    ),
                    SizedBox(width: 22.h),
                    Text('您的答案',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 14.sp)
                            .pfMedium),
                    SizedBox(width: 13.w),
                    Text(
                      question.selected.join(','),
                      style: TextStyle(
                              color: const Color(0xFFFE5A59), fontSize: 18.sp)
                          .pfMedium,
                    ),
                  ],
                ),
              ],
            ),
          ),
        if (isError) SizedBox(height: 12.h),
        if (isError)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 4.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                          color: const Color(0xFF0054FF),
                          borderRadius: BorderRadius.circular(2.r)),
                    ),
                    SizedBox(width: 7.w),
                    Text('考点',
                        style: TextStyle(
                                color: const Color(0xFF333333), fontSize: 18.sp)
                            .pfMedium),
                  ],
                ),
                SizedBox(height: 16.h),
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children: [
                    ...question.key_point.map((e) {
                      return Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                            color: const Color(0xFFE8F0FF),
                            borderRadius: BorderRadius.circular(10.r)),
                        child: Text(
                          e,
                          style: TextStyle(
                                  color: const Color(0xFF0054FF),
                                  fontSize: 14.sp)
                              .pfMedium,
                        ),
                      );
                    }),
                  ],
                ),
              ],
            ),
          ),
        if (isError) SizedBox(height: 12.h),
        if (isError)
          Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 4.w,
                        height: 16.h,
                        decoration: BoxDecoration(
                            color: const Color(0xFF0054FF),
                            borderRadius: BorderRadius.circular(2.r)),
                      ),
                      SizedBox(width: 7.w),
                      Text('解析',
                          style: TextStyle(
                                  color: const Color(0xFF333333),
                                  fontSize: 18.sp)
                              .pfMedium),
                    ],
                  ),
                  SizedBox(height: 19.h),
                  Html(data: question.analysis),
                ],
              )),
        SizedBox(height: ScreenUtil().bottomBarHeight + 90.h),
      ]),
    );
  }

  Widget _choiceView(PaperContentItemModel question, Map choice) {
    bool showResult = question.answer_status != AnswerStatus.NOT;
    bool isSelected = question.selected.contains(choice['option_key']);
    bool isCorrectAnswer = question.answer.contains(choice['option_key']);

    Color backgroundColor = Colors.transparent;
    Color textColor = const Color(0xFF333333);
    Color? borderColor;

    Color rightColor = const Color(0xFF36D176);
    Color wrongColor = const Color(0xFFFE5A59);

    if (showResult) {
      if (question.question_category == QuestionCategory.SINGLE ||
          question.question_category == QuestionCategory.TF) {
        if (isCorrectAnswer) {
          backgroundColor = rightColor;
          textColor = Colors.white;
        } else if (isSelected) {
          backgroundColor = wrongColor;
          textColor = Colors.white;
        }
      } else if (question.question_category == QuestionCategory.MULTIPLE) {
        if (isSelected && isCorrectAnswer) {
          // 选中且正确，绿色背景
          backgroundColor = rightColor;
          textColor = Colors.white;
        } else if (isSelected && !isCorrectAnswer) {
          // 选中且错误，红色背景
          backgroundColor = wrongColor;
          textColor = Colors.white;
        } else if (!isSelected && isCorrectAnswer) {
          // 漏选且是正确答案，边框绿色，文字绿色,白色背景
          textColor = rightColor;
          borderColor = rightColor;
        }
        //漏选且不是正确答案，保持不变
      }
    } else if (isSelected) {
      backgroundColor = const Color(0xFF0054FF);
      textColor = Colors.white;
    }

    return GestureDetector(
      onTap: () {
        if (showResult) return; // 已显示结果时不允许再选择

        if (question.question_category == QuestionCategory.MULTIPLE) {
          if (question.selected.contains(choice['option_key'])) {
            question.selected.remove(choice['option_key']);
          } else {
            question.selected.add(choice['option_key']);
          }
        } else {
          question.selected.clear();
          question.selected.add(choice['option_key']);
        }
        setState(() {});
      },
      child: Container(
        width: ScreenUtil().screenWidth,
        padding: EdgeInsets.symmetric(vertical: 10.h),
        child: Row(
          children: [
            Container(
              alignment: Alignment.center,
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(
                      question.question_category == QuestionCategory.SINGLE ||
                              question.question_category == QuestionCategory.TF
                          ? 20
                          : 8.r),
                  border: Border.all(
                      color: borderColor ?? const Color(0xFFCCCCCC),
                      width: borderColor != null ? 1.5 : 0.5)),
              child: Text(
                choice['option_key'] ?? '',
                style: TextStyle(color: textColor, fontSize: 18.sp).pfMedium,
              ),
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Html(data: choice['option_value'] ?? ''),
              /* Text(
              choice['option_value'] ?? '',
              style: TextStyle(color: textColor, fontSize: 16.sp),
            ) */
            ),
          ],
        ),
      ),
    );
  }
}
