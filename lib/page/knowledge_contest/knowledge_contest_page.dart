import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/knowledge_contest/paper_category_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/widget/study/knowledge_contest_item.dart';
import 'package:npemployee/widget/study/video_tab_item.dart';

class KnowledgeContestPage extends StatefulWidget {
  final String title;
  const KnowledgeContestPage({super.key, required this.title});

  @override
  State<KnowledgeContestPage> createState() => _KnowledgeContestPageState();
}

class _KnowledgeContestPageState extends State<KnowledgeContestPage> {
  int _tabIndex = 0;

  List<PaperCategoryModel> paperCategoryList = [];
  List<PaperModel> paperList = [];

  PageController _pageController = PageController();

  final EasyRefreshController _refreshController = EasyRefreshController();

  void _getPaperCategoryList() {
    UserServiceProvider().getPaperCategoryList(
        cacheCallBack: (result) {
          _formatPaperCategoryData(result, true);
        },
        successCallBack: (result) {
          _formatPaperCategoryData(result, false);
        },
        errorCallBack: (err) {});
  }

  void _getPaperList({int? paper_category_id}) {
    UserServiceProvider().getPaperList(
        paper_category_id: paper_category_id,
        cacheCallBack: (result) {
          _formatPaperData(result, true);
        },
        successCallBack: (result) {
          _formatPaperData(result, false);
        },
        errorCallBack: (err) {});
  }

  void _formatPaperCategoryData(ResultData result, bool isCache) {
    paperCategoryList.clear();
    paperCategoryList
        .add(PaperCategoryModel(category_id: null, category_name: '全部'));
    for (var element in result.data['list']) {
      paperCategoryList.add(PaperCategoryModel.fromJson(element));
    }

    if (!isCache) {
      _getPaperList(paper_category_id: paperCategoryList.first.category_id);
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _formatPaperData(ResultData result, bool isCache) {
    paperList.clear();
    for (var element in result.data['list']) {
      paperList.add(PaperModel.fromJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getPaperCategoryList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FD),
      appBar: CommonNav(title: widget.title),
      body: Column(
        children: [
          Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              color: Colors.white,
              child: SingleChildScrollView(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    ...paperCategoryList.map((item) {
                      return VideoTabItem(
                          title: item.category_name,
                          isSelect:
                              paperCategoryList[_tabIndex].category_name ==
                                  item.category_name,
                          onTap: () {
                            _tabIndex = paperCategoryList.indexWhere(
                                (e) => e.category_name == item.category_name);
                            setState(() {});
                            _pageController.animateToPage(_tabIndex,
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.ease);
                            _getPaperList(
                                paper_category_id:
                                    paperCategoryList[_tabIndex].category_id);
                          });
                    }),
                  ],
                ),
              )),
          Expanded(
            child: PageView.builder(
                controller: _pageController,
                itemCount: paperCategoryList.length,
                onPageChanged: (value) {
                  setState(() {
                    _tabIndex = value;
                  });
                  _getPaperList(
                      paper_category_id:
                          paperCategoryList[_tabIndex].category_id);
                },
                itemBuilder: (pC, pI) {
                  return EasyRefresh.builder(
                    controller: _refreshController,
                    onRefresh: () => _getPaperList(
                        paper_category_id:
                            paperCategoryList[_tabIndex].category_id),
                    childBuilder: (_, physic) => paperList.isEmpty
                        ? NoDataPage(physics: physic)
                        : ListView.builder(
                            physics: physic,
                            itemCount: paperList.length,
                            itemBuilder: (c, index) {
                              PaperModel item = paperList[index];
                              return KnowledgeContestItem(
                                item: item,
                                refresh: (value) {
                                  _getPaperList(
                                      paper_category_id:
                                          paperCategoryList[_tabIndex]
                                              .category_id);
                                },
                              );
                            }),
                  );
                }),
          )
        ],
      ),
    );
  }
}
