import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';

class FrequentlyMattersPage extends StatefulWidget {
  const FrequentlyMattersPage({super.key});

  @override
  State<FrequentlyMattersPage> createState() => _FrequentlyMattersPageState();
}

class _FrequentlyMattersPageState extends State<FrequentlyMattersPage> {
  int _currentIndex = 0;

  final List<Map<String, String>> _topMenuItems = [
    {"icon": "campus_operations", "text": "校区运营"},
    {"icon": "ground_based_classes", "text": "地面开班"},
    {"icon": "campus_reconciliation", "text": "校区对账"},
    {"icon": "constant_data", "text": "增长数据"},
    {"icon": "operational_tools", "text": "运营工具"},
  ];

  final List<Map<String, dynamic>> _services = [
    {
      "icon": 'publish_exam_information',
      "title": "发布考情信息",
      "subtitle": "粘贴链接 一键发布考情咨询"
    },
    {"icon": 'job_screening', "title": "创建职位筛选", "subtitle": "为学员提供智能选岗服务"},
    {"icon": 'id_photo', "title": "创建证件照片", "subtitle": "创建各类考试证件照片"},
    {
      "icon": 'written_test_score',
      "title": "创建笔试估分",
      "subtitle": "考前预约 笔试后快速估分"
    },
    {"icon": 'score_reservation', "title": "成绩预约", "subtitle": "成绩发布预约提醒"},
    {"icon": 'check_rankings', "title": "晒分查排名", "subtitle": "考后晒分 系统排名看分差"},
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: ['#FFFFFF'.toColor(), '#F7F8FD'.toColor()])),
      child: Column(
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => _onTap(),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16.r),
              child: SizedBox(
                width: 343.w,
                height: 107.h,
                child: Image.asset(
                  'assets/png/im/test_banner.png',
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          Container(
            width: ScreenUtil().screenWidth,
            padding: EdgeInsets.fromLTRB(26.w, 23.h, 26.w, 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ..._topMenuItems.map((e) {
                  return GestureDetector(
                    onTap: () => _onTap(),
                    child: Column(
                      children: [
                        Image.asset('assets/png/im/${e['icon']}.png',
                            width: 35, height: 35),
                        SizedBox(height: 4.h),
                        Text(
                          e['text']!,
                          style: TextStyle(
                                  color: '#333333'.toColor(), fontSize: 12.sp)
                              .phMedium,
                        ),
                      ],
                    ),
                  );
                })
              ],
            ),
          ),
          Expanded(
            child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15.r),
                    boxShadow: [
                      BoxShadow(
                          color: '#000000'.toColor().withOpacity(0.03),
                          offset: Offset(0, 6.5),
                          blurRadius: 16,
                          spreadRadius: 0)
                    ]),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 17.h),
                    Row(
                      children: [
                        SizedBox(width: 16.w),
                        Text('备考服务',
                            style: TextStyle(
                                    fontSize: 18.sp, color: '#333333'.toColor())
                                .pfSemiBold),
                      ],
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _services.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            onTap: () => _onTap(),
                            leading: Image.asset(
                                'assets/png/im/${_services[index]['icon']}.png',
                                width: 40,
                                height: 40),
                            title: Text(
                              _services[index]['title'],
                              style: TextStyle(
                                      color: '#333333'.toColor(),
                                      fontSize: 15.sp)
                                  .pfSemiBold,
                            ),
                            subtitle: Text(
                              _services[index]['subtitle'],
                              style: TextStyle(
                                      color: '#A5A5A5'.toColor(),
                                      fontSize: 12.sp)
                                  .pfRegular,
                            ),
                            trailing: Image.asset(
                              'assets/png/im/arrow_right_has_bac.png',
                              width: 24,
                              height: 24,
                            ),
                          );
                        },
                      ),
                    )
                  ],
                )),
          ),
          SizedBox(height: ScreenUtil().bottomBarHeight),
        ],
      ),
    );
  }

  void _onTap() {
    EasyLoading.showInfo('开发中，敬请期待!');
    return;
  }
}
