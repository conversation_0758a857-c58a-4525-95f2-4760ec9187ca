import 'package:flutter/material.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_search.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_search_msg_detail.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class MessageSearchPage extends StatefulWidget {
  const MessageSearchPage({
    super.key,
    this.conversation,
    this.onBack,
    this.initKeyword,
    this.isAutoFocus,
    required this.contacts,
  });

  final V2TimConversation? conversation;

  final VoidCallback? onBack;

  final String? initKeyword;

  final bool? isAutoFocus;

  final List<ContactModel> contacts;

  @override
  State<MessageSearchPage> createState() => _MessageSearchPageState();
}

class _MessageSearchPageState extends State<MessageSearchPage> {
  late bool isConversation;
  @override
  void initState() {
    super.initState();
    isConversation = (widget.conversation != null);
  }

  @override
  Widget build(BuildContext context) {
    isConversation = (widget.conversation != null);
    return Scaffold(
      appBar: CommonNav(title: isConversation ? '相关聊天记录' : '全局搜索'),
      body: isConversation
          ? ITimUikitSearchMsgDetail(
              contacts: widget.contacts,
              currentConversation: widget.conversation!,
              keyword: widget.initKeyword ?? "",
              onTapConversation: (con, msg) {
                NavigatorUtils.push(context, MessageRouter.messageDetailPage,
                    arguments: {'con': con, 'msg': msg});
              },
            )
          : ITimUikitSearch(
              contacts: widget.contacts,
              searchContent: {
                "contact": Image.asset(
                  'assets/png/im/contacts.png',
                  width: 40,
                  height: 40,
                ),
                'group': Image.asset(
                  'assets/png/im/group.png',
                  width: 40,
                  height: 40,
                ),
                'history': Image.asset(
                  'assets/png/im/im_history.png',
                  width: 40,
                  height: 40,
                ),
                'check': Image.asset(
                  'assets/png/im/im_search_check.png',
                  width: 15,
                  height: 15,
                ),
              },
              onBack: () {},
              onEnterSearchInConversation: (c, keyword) {
                Navigator.of(context).push(MaterialPageRoute(
                    builder: (_) => MessageSearchPage(
                          contacts: widget.contacts,
                          conversation: c,
                          initKeyword: keyword,
                        )));
              },
              onTapConversation: (con, message) {
                NavigatorUtils.push(context, MessageRouter.messageDetailPage,
                    arguments: {'con': con, 'msg': message});
              }),
    );
  }
}
