import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/model/mine/team_manager_department_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:url_launcher/url_launcher.dart';

class UserProfilePage extends StatefulWidget {
  final ContactModel contact;
  final ContactUser user;
  const UserProfilePage({super.key, required this.contact, required this.user});

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage> {
  // List<TeamManagerDepartmentModel> teamDepartmentList = [];
  String? phoneNumber;
  bool phoneNumberStatus = false;

  /* void _getTeamManagerDetartments() {
    UserServiceProvider().getTeamManagerDetartments(cacheCallBack: (data) {
      // _formatTeamDepartmentData(data, true);
    }, successCallBack: (data) {
      _formatTeamDepartmentData(data, false);
    }, errorCallBack: (err) {
      teamDepartmentList.clear();
      setState(() {});
    });
  }

  _formatTeamDepartmentData(ResultData data, bool isCache) {
    teamDepartmentList.clear();
    for (var e in data.data) {
      teamDepartmentList.add(TeamManagerDepartmentModel.fromJson(e));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  } */

  void _getPhoneNumber() {
    EasyLoading.show();
    UserServiceProvider().getPhoneNumber(widget.user.guid).then((data) {
      EasyLoading.dismiss();
      if (data?.code == 0) {
        phoneNumber = data?.data;
        phoneNumberStatus = !phoneNumberStatus;
        setState(() {});
      }
    });
  }

  void _callPhoneNumber() async {
    String number = 'tel:$phoneNumber';
    Uri diaUri = Uri.parse(number);
    if (await canLaunchUrl(diaUri)) {
      await launchUrl(diaUri);
    } else {
      EasyLoading.showError('拨号失败');
    }
  }

  /* bool get hasManagerPermission {
    bool status = false;
    status = teamDepartmentList.any(
        (element) => element.department.id == widget.contact.department.id);
    return status;
  } */

  @override
  void initState() {
    super.initState();
    phoneNumber = widget.user.mobile;
    // _getTeamManagerDetartments();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        body: Stack(
          children: [
            Container(
              width: ScreenUtil().screenWidth,
              height: 211.h,
              decoration: const BoxDecoration(
                  gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFFE9F0FE), Color(0xFFF7F8FD)])),
            ),
            Container(
              width: ScreenUtil().screenWidth,
              margin: EdgeInsets.only(top: 16.h),
              height: 211.h,
              child: Image.asset(
                'assets/png/im/im_profile_bac.png',
                fit: BoxFit.fitWidth,
              ),
            ),
            Column(
              children: [
                const TeamManagerNav(title: ''),
                SizedBox(height: 36.5.h),
                _topView(),
                SizedBox(height: 16.h),
                _middleView(),
                SizedBox(height: 16.h),
                _bottomView(),
                SizedBox(height: ScreenUtil().bottomBarHeight)
              ],
            ),
          ],
        ));
  }

  Widget _topView() {
    String avatarStr = widget.user.avatar.isEmpty
        ? ValidatorUtils.testImageUrl
        : widget.user.avatar;
    return Container(
      width: 343.w,
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 25.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15.r),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child:
                CachedNetworkImage(imageUrl: avatarStr, width: 60, height: 60),
          ),
          SizedBox(width: 16.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.user.name,
                style:
                    TextStyle(color: const Color(0xFF333333), fontSize: 18.sp)
                        .pfSemiBold,
              ),
              SizedBox(height: 5.5.h),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  if (phoneNumberStatus) {
                    _callPhoneNumber();
                  } else {
                    _getPhoneNumber();
                  }
                },
                child: Row(
                  children: [
                    Text(
                      phoneNumber ?? '',
                      style: TextStyle(
                        color: phoneNumberStatus
                            ? AppTheme.colorBlue
                            : const Color(0xFF808080),
                        fontSize: 12.sp,
                        decoration:
                            phoneNumberStatus ? TextDecoration.underline : null,
                        decorationColor:
                            phoneNumberStatus ? AppTheme.colorBlue : null,
                      ).pfRegular,
                    ),
                    SizedBox(width: 6.w),
                    Image.asset(
                      phoneNumberStatus
                          ? 'assets/png/im/phone_open.png'
                          : 'assets/png/im/phone_close.png',
                      width: 13,
                      height: 13,
                    ),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _middleView() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(15.r)),
      child: Column(
        children: [
          if (widget.contact.department.name.isNotEmpty)
            _middleViewItem('部门', widget.contact.department.name, () {},
                isManager: true,
                /* enabled:
                    widget.user.id == GlobalPreferences().userInfo?.user.id &&
                        hasManagerPermission, */
                hasDivider: false),
          if (widget.contact.schools(widget.user.id).isNotEmpty)
            _middleViewItem(
                '校区', widget.contact.schools(widget.user.id).join(','), () {
              NavigatorUtils.pop(context);
            }, hasDivider: false),
          // _middleViewItem('文案', '文案文案', hasDivider: false),
        ],
      ),
    );
  }

  Widget _middleViewItem(String title, String content, void Function() onTap,
      {bool isManager = false, bool hasDivider = true /* , enabled = true */}) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Row(
          children: [
            Text(title,
                style:
                    TextStyle(color: const Color(0xFF333333), fontSize: 15.sp)
                        .pfRegular),
            SizedBox(width: 27.5.w),
            Expanded(
                child: Container(
                    padding: EdgeInsets.symmetric(vertical: 25.h),
                    decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(
                                color: hasDivider
                                    ? AppTheme.colorDivider
                                    : Colors.transparent,
                                width: 0.5.h))),
                    child: Row(
                      children: [
                        Text(content,
                            style: TextStyle(
                                    color: const Color(0xFF808080),
                                    fontSize: 15.sp)
                                .pfRegular),
                        SizedBox(width: 13.w),
                        if (isManager)
                          Expanded(
                            child: Wrap(
                              spacing: 5.w,
                              runSpacing: 5.h,
                              children: [
                                ...widget.user.roles.map(
                                  (e) => Container(
                                    decoration: BoxDecoration(
                                        color: const Color((0xFF3377FF)),
                                        borderRadius:
                                            BorderRadius.circular(3.r)),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 4.w, vertical: 1.h),
                                    child: Text(e,
                                        style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 10.sp)
                                            .pfRegular),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        // if (enabled) const Spacer(),
                        // if (enabled)
                        //   const Icon(Icons.keyboard_arrow_right,
                        //       color: Color(0xFFBABABA)),
                      ],
                    ))),
          ],
        ),
      ),
    );
  }

  Widget _bottomView() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(15.r)),
      child: Column(
        children: [
          _bottomViewItem('发信息', 'assets/png/im/im_profile_message.png', () {
            NavigatorUtils.push(context, MessageRouter.messageDetailPage,
                arguments: {
                  'con': V2TimConversation(
                      conversationID: 'c2c_${widget.user.id}',
                      userID: widget.user.id.toString(),
                      showName: widget.user.name,
                      contactUserInfo: {
                        "guid": widget.user.guid,
                        "mobile": widget.user.mobile,
                        "list": [
                          {
                            "department": widget.contact.department.name,
                            "roles": widget.user.roles.join(','),
                          }
                        ],
                      },
                      type: 1)
                });
          }),
          _bottomViewItem('语音通话', 'assets/png/im/im_profile_audio.png', () {
            TUICallKit.instance
                .call('${widget.user.id}', TUICallMediaType.audio);
          }, hasDivider: false),
        ],
      ),
    );
  }

  Widget _bottomViewItem(String title, String img, void Function() onTap,
      {bool hasDivider = true}) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        padding: EdgeInsets.symmetric(vertical: 15.h),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color:
                        hasDivider ? AppTheme.colorDivider : Colors.transparent,
                    width: 0.5.h))),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(img, width: 12, height: 12),
            SizedBox(width: 5.w),
            Text(title,
                style: TextStyle(color: AppTheme.colorBlue, fontSize: 15.sp)
                    .pfMedium),
          ],
        ),
      ),
    );
  }
}
