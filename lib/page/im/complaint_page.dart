import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';

class ComplaintPage extends StatefulWidget {
  const ComplaintPage({super.key});

  @override
  State<ComplaintPage> createState() => _ComplaintPageState();
}

class _ComplaintPageState extends State<ComplaintPage> {
  List<String> list = ['发布不适当内容对我造成骚扰', '存在侵权行为', '此账号可能被盗用了', '政治谣言', '其他违规'];

  @override
  Widget build(BuildContext context) {
    return Material(
      color: const Color(0xFFF7F8FD),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: TeamManagerNav(
              title: '投诉',
              leftWidget: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  NavigatorUtils.pop(context);
                },
                child: Image.asset(
                  'assets/png/close.png',
                  width: 18,
                  height: 18,
                ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              '请选择投诉该账号的原因:',
              style: TextStyle(
                      color: const Color(0xFF6F7581),
                      fontSize: 12.sp,
                      height: 2)
                  .pfMedium,
            ),
          ),
          Container(
              padding: EdgeInsets.fromLTRB(21.w, 0, 21.w, 36.5.h),
              color: Colors.white,
              child: Column(
                children: [
                  ...list.map((e) => _itemBuilder(e, () {
                        WeChatService().launchWeChatWork();
                      }))
                ],
              )),
        ],
      ),
    );
  }

  Widget _itemBuilder(String title, void Function()? onTap) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.h),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    width: 1,
                    color: const Color(0xFFEBECF0).withOpacity(0.73)))),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                      color: const Color(0xFF181818),
                      fontSize: 14.sp,
                      height: 2)
                  .pfMedium,
            ),
            Image.asset('assets/png/arrow_right.png', width: 12, height: 12),
          ],
        ),
      ),
    );
  }
}
