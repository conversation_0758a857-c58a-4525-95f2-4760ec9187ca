import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:url_launcher/url_launcher.dart';

class UserProfilePage2 extends StatefulWidget {
  final V2TimConversation con;
  const UserProfilePage2({super.key, required this.con});

  @override
  State<UserProfilePage2> createState() => _UserProfilePage2State();
}

class _UserProfilePage2State extends State<UserProfilePage2> {
  String? phoneNumber;
  bool phoneNumberStatus = false;
  late Map info;

  void _getPhoneNumber() {
    EasyLoading.show();
    UserServiceProvider().getPhoneNumber(info['guid']).then((data) {
      EasyLoading.dismiss();
      if (data?.code == 0) {
        phoneNumber = data?.data;
        phoneNumberStatus = !phoneNumberStatus;
        setState(() {});
      }
    });
  }

  void _callPhoneNumber() async {
    String number = 'tel:$phoneNumber';
    Uri diaUri = Uri.parse(number);
    if (await canLaunchUrl(diaUri)) {
      await launchUrl(diaUri);
    } else {
      EasyLoading.showError('拨号失败');
    }
  }

  @override
  void initState() {
    super.initState();
    info = widget.con.contactUserInfo ?? {};
    phoneNumber = info['mobile'];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        body: Stack(
          children: [
            Container(
              width: ScreenUtil().screenWidth,
              height: 211.h,
              decoration: const BoxDecoration(
                  gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFFE9F0FE), Color(0xFFF7F8FD)])),
            ),
            Container(
              width: ScreenUtil().screenWidth,
              margin: EdgeInsets.only(top: 16.h),
              height: 211.h,
              child: Image.asset(
                'assets/png/im/im_profile_bac.png',
                fit: BoxFit.fitWidth,
              ),
            ),
            Column(
              children: [
                const TeamManagerNav(title: ''),
                SizedBox(height: 36.5.h),
                _topView(),
                SizedBox(height: 16.h),
                _middleView(),
                SizedBox(height: 16.h),
                _bottomView(),
                SizedBox(height: ScreenUtil().bottomBarHeight)
              ],
            ),
          ],
        ));
  }

  Widget _topView() {
    String avatarStr = (widget.con.faceUrl ?? '').isEmpty
        ? ValidatorUtils.testImageUrl
        : widget.con.faceUrl!;
    return Container(
      width: 343.w,
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 25.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15.r),
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child:
                CachedNetworkImage(imageUrl: avatarStr, width: 60, height: 60),
          ),
          SizedBox(width: 16.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.con.showName ?? '',
                style:
                    TextStyle(color: const Color(0xFF333333), fontSize: 18.sp)
                        .pfSemiBold,
              ),
              SizedBox(height: 5.5.h),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  if (phoneNumberStatus) {
                    _callPhoneNumber();
                  } else {
                    _getPhoneNumber();
                  }
                },
                child: Row(
                  children: [
                    Text(
                      phoneNumber ?? '',
                      style: TextStyle(
                        color: phoneNumberStatus
                            ? AppTheme.colorBlue
                            : const Color(0xFF808080),
                        fontSize: 12.sp,
                        decoration:
                            phoneNumberStatus ? TextDecoration.underline : null,
                        decorationColor:
                            phoneNumberStatus ? AppTheme.colorBlue : null,
                      ).pfRegular,
                    ),
                    SizedBox(width: 6.w),
                    Image.asset(
                      phoneNumberStatus
                          ? 'assets/png/im/phone_open.png'
                          : 'assets/png/im/phone_close.png',
                      width: 13,
                      height: 13,
                    ),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _middleView() {
    List departInfos = widget.con.contactUserInfo?['list'] ?? [];
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(15.r)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '部门',
            style: TextStyle(
                    fontSize: 15.sp,
                    color: const Color(0xFF333333),
                    height: 17.85.sp / 15.sp)
                .pfMedium,
          ),
          SizedBox(width: 27.5.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ...departInfos.map((e) {
                List roles = e['roles'].toString().split(', ');
                return Container(
                  margin: EdgeInsets.only(
                      bottom: departInfos.indexOf(e) == departInfos.length - 1
                          ? 0
                          : 8.h),
                  child: Row(
                    children: [
                      Text(
                        e['department'],
                        style: TextStyle(
                                fontSize: 15.sp,
                                color: const Color(0xFF808080),
                                height: 17.85.sp / 15.sp)
                            .pfMedium,
                      ),
                      SizedBox(width: 13.w),
                      SizedBox(
                        width: 150.w,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              ...roles.map(
                                (e) => Container(
                                  margin: EdgeInsets.only(left: 5.w),
                                  decoration: BoxDecoration(
                                      color: const Color((0xFF3377FF)),
                                      borderRadius: BorderRadius.circular(3.r)),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 4.w, vertical: 1.h),
                                  child: Text(e,
                                      style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 10.sp)
                                          .pfRegular),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          )
        ],
      ),
    );
  }

  Widget _bottomView() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(15.r)),
      child: Column(
        children: [
          _bottomViewItem('发信息', 'assets/png/im/im_profile_message.png', () {
            NavigatorUtils.pop(context);
          }),
          _bottomViewItem('语音通话', 'assets/png/im/im_profile_audio.png', () {
            TUICallKit.instance
                .call('${widget.con.userID}', TUICallMediaType.audio);
          }, hasDivider: false),
        ],
      ),
    );
  }

  Widget _bottomViewItem(String title, String img, void Function() onTap,
      {bool hasDivider = true}) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        padding: EdgeInsets.symmetric(vertical: 15.h),
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    color:
                        hasDivider ? AppTheme.colorDivider : Colors.transparent,
                    width: 0.5.h))),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(img, width: 12, height: 12),
            SizedBox(width: 5.w),
            Text(title,
                style: TextStyle(color: AppTheme.colorBlue, fontSize: 15.sp)
                    .pfMedium),
          ],
        ),
      ),
    );
  }
}
