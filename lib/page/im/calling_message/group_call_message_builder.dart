import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/im_utils.dart';

import 'calling_message_data_provider.dart';

class GroupCallMessageItem extends StatefulWidget {
  final CallingMessageDataProvider callingMessageDataProvider;

  const GroupCallMessageItem({
    Key? key,
    required this.callingMessageDataProvider,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => _GroupCallMessageItemState();
}

class _GroupCallMessageItemState extends State<GroupCallMessageItem> {
  @override
  void initState() {
    super.initState();
  }

  Widget wrapMessageTips(Widget child) {
    return Container(
        alignment: Alignment.center,
        margin: const EdgeInsets.symmetric(vertical: 10),
        child: child);
  }

  @override
  Widget build(BuildContext context) {
    return !widget.callingMessageDataProvider.excludeFromHistory
        ? wrapMessageTips(Text(
            widget.callingMessageDataProvider.content,
            style: ImUtils.messageHistoryListTextStyle(),
            textAlign: TextAlign.center,
            softWrap: true,
          ))
        : const SizedBox();
  }
}
