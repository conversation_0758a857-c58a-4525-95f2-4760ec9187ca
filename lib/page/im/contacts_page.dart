import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/manager/local_cache_manager.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/im/frequently_matters_page.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:npemployee/model/mine/team_manager_department_model.dart';

class ContactsPage extends StatefulWidget {
  const ContactsPage({super.key});

  @override
  State<ContactsPage> createState() => _ContactsPageState();
}

class _ContactsPageState extends State<ContactsPage> {
  final List<ContactModel> _contacts = [];
  int _tabIndex = 0;
  final List<String> _tabs = ['部门/校区', '常办事项'];
  final PageController _pageController = PageController();

  String searchQuery = '';
  FocusNode searchFocusNode = FocusNode();

  List<ContactUser> get filteredItems {
    if (searchQuery.isEmpty) return [];

    return _contacts.expand((contact) {
      List<ContactUser> matchingUsers = [];
      matchingUsers.addAll(_searchContant(contact, searchQuery) ?? []);
      return matchingUsers;
    }).toList();
  }

  List<TextSpan> _highlightOccurrences(String text, String query) {
    if (query.isEmpty) return [TextSpan(text: text)];

    List<TextSpan> spans = [];
    int start = 0;
    int indexOfHighlight;
    while ((indexOfHighlight =
            text.toLowerCase().indexOf(query.toLowerCase(), start)) !=
        -1) {
      if (indexOfHighlight > start) {
        spans.add(TextSpan(text: text.substring(start, indexOfHighlight)));
      }
      spans.add(TextSpan(
        text: text.substring(indexOfHighlight, indexOfHighlight + query.length),
        style: const TextStyle(color: Colors.blue),
      ));
      start = indexOfHighlight + query.length;
    }
    if (start < text.length) {
      spans.add(TextSpan(text: text.substring(start)));
    }
    return spans;
  }

  List<ContactUser>? _searchContant(ContactModel contact, String query) {
    var filteredItems = [];
    _searchRecursively(contact, query, filteredItems);
    return filteredItems.cast();
  }

  ContactModel? findContactByDepartmentName(
      List<ContactModel> contacts, String departmentName) {
    for (var contact in contacts) {
      if (contact.department.name == departmentName) {
        return contact;
      }
      if (contact.children != null && contact.children!.isNotEmpty) {
        var result =
            findContactByDepartmentName(contact.children!, departmentName);
        if (result != null) {
          return result;
        }
      }
    }
    return null;
  }

  void _searchRecursively(
      ContactModel contact, String query, List filteredItems) {
    // Check if department name matches
    if (contact.department.name.contains(query)) {
      // Add all users from this department
      if (contact.userList.isNotEmpty) {
        filteredItems.addAll(contact.userList);
      } else {
        for (var child in contact.children ?? []) {
          if (child.userList.isNotEmpty) {
            filteredItems.addAll(child.userList);
          }
        }
      }
    } else {
      // If department doesn't match, check individual users
      for (var user in contact.userList) {
        if (user.name.contains(query)) {
          filteredItems.add(user);
        }
      }
    }
    // Recursively search through children
    for (var child in contact.children ?? []) {
      _searchRecursively(child, query, filteredItems);
    }
  }

  void _getContacts() {
    Map<String, dynamic>? res =
        LocalCacheManager.shared.getMapWithKey(LocalCachekeys.contactsKey);
    if (res != null) {
      ResultData? data = ResultData.fromJson(res);
      _formatContactsData(data, isCache: false);
    } else {
      EasyLoading.show();
      UserServiceProvider().getContacts(cacheCallBack: (data) {
        _formatContactsData(data, isCache: true);
      }, successCallBack: (data) {
        EasyLoading.dismiss();
        _formatContactsData(data);
      }, errorCallBack: (err) {
        EasyLoading.dismiss();
      });
    }
  }

  void _formatContactsData(ResultData? data, {bool isCache = false}) {
    _contacts.clear();
    for (var element in data?.data ?? []) {
      if (element['user_count'] != 0) {
        _contacts.add(ContactModel.fromJson(element));
      }
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getContacts();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (searchFocusNode.hasFocus) {
          searchFocusNode.unfocus();
        }
      },
      child: Scaffold(
        appBar: const CommonNav(title: '通讯录'),
        body: Column(
          children: [
            _tabView(),
            if (_tabIndex == 0)
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: _searchView()),
            _tabIndex == 0 ? SizedBox(height: 27.h) : const SizedBox(height: 0),
            Expanded(child: _tabContent())
          ],
        ),
      ),
    );
  }

  Widget _searchView() {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.5.r),
          color: const Color(0xFFF7F8FD)),
      child: TextField(
        onChanged: (value) {
          setState(() {
            searchQuery = value;
          });
        },
        focusNode: searchFocusNode,
        decoration: InputDecoration(
          hintText: '请输入搜索内容 (姓名/校区/部门)',
          hintStyle: TextStyle(
            color: const Color(0xFFBABABA),
            fontSize: 15.sp,
          ).pfRegular,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 0,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 0,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 0,
            ),
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: Color(0xFFBABABA),
          ),
        ),
        style: const TextStyle(fontSize: 15),
      ),
    );
  }

  Widget _tabView() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      child: Row(
        children: [
          ..._tabs.map((e) => _tabViewItem(e, _tabs.indexOf(e), (i) {
                setState(() {
                  _tabIndex = i;
                });
                if (_tabIndex == 0) _getContacts();
                _pageController.animateToPage(_tabIndex,
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.ease);
              }))
        ],
      ),
    );
  }

  Widget _tabViewItem(String title, int index, Function(int) onTap) {
    bool isSelected = index == _tabIndex;
    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 12.w),
        child: Text(title,
            style: !isSelected
                ? TextStyle(color: const Color(0xFF6A7282), fontSize: 15.sp)
                    .pfRegular
                : TextStyle(color: const Color(0xFF000000), fontSize: 16.sp)
                    .pfSemiBold),
      ),
    );
  }

  Widget _tabContent() {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (value) {
        setState(() {
          _tabIndex = value;
        });
        if (_tabIndex == 0) _getContacts();
      },
      itemCount: _tabs.length,
      itemBuilder: (_, index) => _tabIndex == 0
          ?
          //如果搜索内容为空 展示正常的通讯录层级
          //如果含有搜索内容 展示搜索结果 -> 有结果就展示结果 无结果展示无数据页面
          (searchQuery.isNotEmpty
              ? (filteredItems.isEmpty
                  ? const NoDataPage()
                  : ListView.builder(
                      // 这个是搜索后的结果
                      itemCount: filteredItems.length,
                      itemBuilder: _filteredItemsDepartmentItem))
              : ListView.builder(
                  // 这个就是正常的通讯录层级
                  itemCount: _contacts.length,
                  itemBuilder: _departmentItem))
          : const FrequentlyMattersPage(),
    );
  }

  Widget _filteredItemsDepartmentItem(BuildContext _, int index) {
    ContactUser model = filteredItems[index];
    return GestureDetector(
      onTap: () {
        var department_name =
            model.department_roles?.first['department']['name'] ?? '';
        var contact = findContactByDepartmentName(_contacts, department_name);
        if (contact != null) {
          NavigatorUtils.push(context, MessageRouter.userProfilePage,
              arguments: {
                'contact': contact,
                'user': model,
              });
        }
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
          margin: EdgeInsets.only(bottom: 25.h),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: CachedNetworkImage(
                      imageUrl: model.avatar,
                      width: 40,
                      height: 40,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichText(
                          text: TextSpan(
                            children:
                                _highlightOccurrences(model.name, searchQuery),
                            style: TextStyle(
                              color: const Color(0xFF181818),
                              fontSize: 16.sp,
                            ).pfRegular,
                          ),
                        ),
                        Text(
                            model.department_roles?.first['roles'].first['role']
                                    ['name'] ??
                                '',
                            style: TextStyle(
                                    color: const Color(0xFF999999),
                                    fontSize: 11.sp)
                                .pfRegular),
                        RichText(
                          text: TextSpan(
                            children: _highlightOccurrences(
                                "部门：${model.department_roles?.first['department']['name'] ?? ''}",
                                searchQuery),
                            style: TextStyle(
                              color: const Color(0xFF999999),
                              fontSize: 11.sp,
                            ).pfRegular,
                          ),
                        ),
                      ]),
                  SizedBox(width: 16.w),
                ],
              ),
              const Icon(Icons.keyboard_arrow_right, color: Color(0xFFBABABA)),
            ],
          )),
    );
  }

  Widget _departmentItem(BuildContext _, int index) {
    ContactModel model = _contacts[index];
    return GestureDetector(
      onTap: () {
        if (searchFocusNode.hasFocus) {
          searchFocusNode.unfocus();
        }
        NavigatorUtils.push(context, MessageRouter.contactDetailPage,
            arguments: {'contacts': model});
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
          margin: EdgeInsets.only(bottom: 25.h),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SizedBox(
                      width: 37,
                      height: 37,
                      child:
                          Image.asset('assets/png/im/im_department_icon.png')),
                  SizedBox(width: 12.w),
                  Text(model.department.name,
                      style: TextStyle(
                              color: const Color(0xFF181818), fontSize: 16.sp)
                          .pfRegular),
                  SizedBox(width: 16.w),
                  Text(
                    '(${model.user_count})',
                    style: TextStyle(
                            color: const Color(0xFF999999), fontSize: 16.sp)
                        .pfRegular,
                  )
                ],
              ),
              const Icon(Icons.keyboard_arrow_right, color: Color(0xFFBABABA)),
            ],
          )),
    );
  }
}
