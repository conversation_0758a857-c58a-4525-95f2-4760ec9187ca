import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/conversation/conversation_services.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ITimUikitGroupSearchMsg extends TIMUIKitStatelessWidget {
  final ConversationService _conversationService =
      serviceLocator<ConversationService>();

  final Function(V2TimConversation?) onJumpToSearch;
  ITimUikitGroupSearchMsg({super.key, required this.onJumpToSearch});

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;

    final model = Provider.of<TUIGroupProfileModel>(context);

    return InkWell(
      onTap: () async {
        V2TimConversation? conversation =
            await _conversationService.getConversation(
                conversationID: "group_${model.groupInfo!.groupID}");
        if (conversation != null) {
          onJumpToSearch(conversation);
        }
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 8.5.w),
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(6.r)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              TIM_t("查找聊天记录"),
              style: TextStyle(fontSize: 14.sp, color: '000000'.toColor())
                  .pfMedium,
            ),
            Image.asset('assets/png/arrow_right.png', width: 10.w),
          ],
        ),
      ),
    );
  }
}
