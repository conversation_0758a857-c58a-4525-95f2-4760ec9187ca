import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

enum SearchType { contact, group, history }

class ITimUikitSearchIndicator extends TIMUIKitStatelessWidget {
  final List<SearchType> typeList;
  final ValueChanged<List<SearchType>> onChange;
  final Map? searchContent;
  ITimUikitSearchIndicator(
      {super.key,
      required this.typeList,
      required this.onChange,
      this.searchContent});

  final titleMap = {
    SearchType.contact: "联系人",
    SearchType.group: "群聊",
    SearchType.history: "聊天记录"
  };

  Widget renderItemBox(
      Widget icon, SearchType item, bool isSelect, TUITheme theme) {
    return InkWell(
      onTap: () {
        if (isSelect) {
          typeList.remove(item);
        } else {
          typeList.add(item);
        }
        onChange(typeList);
      },
      child: Container(
        padding: const EdgeInsets.all(6),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(6),
                  child: icon,
                ),
                if (isSelect)
                  Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                          height: 16,
                          width: 16,
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: theme.primaryColor),
                          child: searchContent!['check'])),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              TIM_t(titleMap[item]!),
              style: TextStyle(color: hexToColor('808080'), fontSize: 12)
                  .pfRegular,
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final theme = value.theme;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(TIM_t("搜索指定内容"),
                    style: TextStyle(color: hexToColor('333333'), fontSize: 13)
                        .pfRegular),
              )
            ],
          ),
          const SizedBox(height: 5),
          // Divider(thickness: 0.8, color: theme.weakDividerColor),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.max,
            children: [
              renderItemBox(searchContent!['contact'], SearchType.contact,
                  typeList.contains(SearchType.contact), theme),
              renderItemBox(searchContent!['group'], SearchType.group,
                  typeList.contains(SearchType.group), theme),
              renderItemBox(searchContent!['history'], SearchType.history,
                  typeList.contains(SearchType.history), theme),
              Container(width: 52),
            ],
          )
        ],
      ),
    );
  }
}
