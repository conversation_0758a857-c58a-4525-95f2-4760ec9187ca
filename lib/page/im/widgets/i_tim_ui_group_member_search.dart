import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/optimize_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart' as tui;
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_input.dart';

class ITimUiGroupMemberSearch extends TIMUIKitStatelessWidget {
  final Function(String text) onTextChange;
  ITimUiGroupMemberSearch({super.key, required this.onTextChange});

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;
    final FocusNode focusNode = FocusNode();

    var debounceFunc = OptimizeUtils.debounce(
        (text) => onTextChange(text), const Duration(milliseconds: 300));

    return Container(
      color: Colors.white,
      child: Column(children: [
        if (!isDesktopScreen)
          Container(
            padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 8.h),
            color: Colors.white,
            child: Container(
              decoration: BoxDecoration(
                  color: 'F7F8FD'.toColor(),
                  borderRadius: BorderRadius.circular(5.5.r),
                  border: Border.all(color: 'E9E9E9'.toColor(), width: 0.5)),
              padding: const EdgeInsets.symmetric(horizontal: 12),
              height: 44.h,
              child: Row(
                children: [
                  Image.asset('assets/png/search.png',
                      width: 17, height: 17, color: 'BABABA'.toColor()),
                  const SizedBox(width: 8),
                  Expanded(
                      child: TextField(
                    onChanged: debounceFunc,
                    decoration: InputDecoration(
                      hintText: TIM_t("请输入搜索内容"),
                      hintStyle: TextStyle(
                        color: 'BABABA'.toColor(),
                        fontSize: 15.sp,
                      ).pfRegular,
                      border: InputBorder.none,
                      isDense: true,
                    ),
                    style: TextStyle(
                      color: '333333'.toColor(),
                      fontSize: 15.sp,
                    ).pfRegular,
                  )),
                ],
              ),
            ),
          ),
        if (isDesktopScreen)
          TIMUIKitSearchInput(
            prefixIcon: Icon(
              Icons.search,
              size: 16,
              color: hexToColor("979797"),
            ),
            onChange: (text) {
              focusNode.requestFocus();
              debounceFunc(text);
            },
            focusNode: focusNode,
          ),
        // Divider(
        //     thickness: 1,
        //     indent: 74,
        //     endIndent: 0,
        //     color: theme.weakBackgroundColor,
        //     height: 0)
      ]),
    );
  }
}
