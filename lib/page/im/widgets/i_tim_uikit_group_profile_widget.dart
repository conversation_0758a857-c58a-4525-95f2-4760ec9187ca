import 'package:flutter/cupertino.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_manage.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_member_title.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_message_disturb.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_name_card.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_notification.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_pin_conversation.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_profile_detail_card.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_search_msg.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/widgets/tim_uikit_group_add_opt.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/widgets/tim_uikit_group_type.dart';

class ITimUikitGroupProfileWidget {
  static Widget detailCard(
      {required V2TimGroupInfo groupInfo,
      bool isHavePermission = false,

      /// You can deal with updating group name manually, or UIKIt do it automatically.
      Function(String updateGroupName)? updateGroupName}) {
    return ITimUikitGroupProfileDetailCard(
      groupInfo: groupInfo,
      isHavePermission: isHavePermission,
      updateGroupName: updateGroupName,
    );
  }

  static Widget memberTile() {
    return ITimUikitGroupMemberTitle();
  }

  static Widget groupNotification({
    bool isHavePermission = false,
  }) {
    return ITimUikitGroupNotification(
      isHavePermission: isHavePermission,
    );
  }

  static Widget groupManage() {
    return const ITimUikitGroupManage();
  }

  static Widget searchMessage(Function(V2TimConversation?) onJumpToSearch) {
    return ITimUikitGroupSearchMsg(onJumpToSearch: onJumpToSearch);
  }

  static Widget operationDivider(TUITheme theme) {
    final isDesktopScreen =
        TUIKitScreenUtils.getFormFactor() == DeviceType.Desktop;
    return Container(
      color: 'F7F8FD'.toColor(), //theme.weakDividerColor
      height: isDesktopScreen ? 1 : 10,
    );
  }

  static Widget groupType() {
    return GroupProfileType();
  }

  static Widget groupAddOpt() {
    return GroupProfileAddOpt();
  }

  static Widget nameCard() {
    return const ITimUikitGroupNameCard();
  }

  static Widget messageDisturb() {
    return ITimUikitGroupMessageDisturb();
  }

  static Widget pinedConversation() {
    return ITimUikitGroupPinConversation();
  }
}
