import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/page/im/widgets/i_text_input_bottom_sheet.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart' as tui;
import 'package:tencent_cloud_chat_uikit/ui/widgets/text_input_bottom_sheet.dart';

class ITimUikitGroupNameCard extends StatefulWidget {
  const ITimUikitGroupNameCard({super.key});

  @override
  State<ITimUikitGroupNameCard> createState() => _ITimUikitGroupNameCardState();
}

class _ITimUikitGroupNameCardState
    extends TIMUIKitState<ITimUikitGroupNameCard> {
  final TextEditingController controller = TextEditingController();
  String? nameCard;
  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;
    final model = Provider.of<TUIGroupProfileModel>(context);
    if (model == null) {
      return Container();
    }
    nameCard = model.getSelfNameCard();
    controller.text = nameCard ?? "";

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8.5.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(6.r)),
      child: Column(
        children: [
          GestureDetector(
            onTap: () async {
              if (!isDesktopScreen) {
                ITextInputBottomSheet.showTextInputBottomSheet(
                    context: context,
                    title: TIM_t("设置我的群昵称"),
                    tips: TIM_t("仅限中文、字母、数字和下划线，2-20个字"),
                    onSubmitted: (String nameCard) async {
                      final text = nameCard.trim();
                      model.setNameCard(text);
                    },
                    theme: theme);
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(right: 10),
                        child: Text(
                          TIM_t("我的群昵称"),
                          style: TextStyle(
                                  fontSize: isDesktopScreen ? 14 : 14.sp,
                                  color: '000000'.toColor())
                              .pfMedium,
                        ),
                      ),
                      if (!isDesktopScreen)
                        Expanded(
                            child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Expanded(
                                child: Text(
                              nameCard ?? "",
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                      fontSize: isDesktopScreen ? 14 : 14.sp,
                                      color: '444444'.toColor())
                                  .pfRegular,
                            )),
                            SizedBox(width: 6.w),
                            Image.asset('assets/png/arrow_right.png',
                                width: 10.w),
                          ],
                        )),
                    ],
                  ),
                  if (isDesktopScreen)
                    Text(
                      TIM_t("仅限中文、字母、数字和下划线，2-20个字"),
                      style:
                          TextStyle(color: theme.weakTextColor, fontSize: 12),
                    ),
                  if (isDesktopScreen)
                    Container(
                      margin: const EdgeInsets.symmetric(vertical: 10),
                      height: 30,
                      child: TextField(
                          minLines: 1,
                          controller: controller,
                          maxLines: 1,
                          onSubmitted: (text) {
                            model.setNameCard(text.trim());
                          },
                          keyboardType: TextInputType.multiline,
                          autofocus: true,
                          textAlignVertical: TextAlignVertical.center,
                          textAlign: TextAlign.start,
                          style: const TextStyle(fontSize: 12),
                          decoration: InputDecoration(
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 10),
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(5.0),
                                  borderSide: BorderSide(
                                    color:
                                        theme.weakDividerColor ?? Colors.grey,
                                  )),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5.0),
                                borderSide: BorderSide(
                                  color: theme.weakDividerColor ?? Colors.grey,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                //选中时外边框颜色
                                borderRadius: BorderRadius.circular(5.0),
                                borderSide: BorderSide(
                                  color: theme.weakTextColor ?? Colors.grey,
                                ),
                              ),
                              hintStyle: const TextStyle(
                                color: Color(0xFFAEA4A3),
                              ),
                              hintText: TIM_t("修改我的群昵称"))),
                    ),
                ],
              ),
            ),
          ),
          Container(color: '#ECEAEA'.toColor().withOpacity(0.5), height: 1),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              NavigatorUtils.push(context, MessageRouter.complaintPage);
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(right: 10),
                        child: Text(
                          TIM_t("投诉"),
                          style: TextStyle(
                                  fontSize: isDesktopScreen ? 14 : 14.sp,
                                  color: '000000'.toColor())
                              .pfMedium,
                        ),
                      ),
                      Image.asset('assets/png/arrow_right.png', width: 10.w),
                    ],
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
