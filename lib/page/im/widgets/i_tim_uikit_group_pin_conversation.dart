import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_operation_item.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ITimUikitGroupPinConversation extends TIMUIKitStatelessWidget {
  ITimUikitGroupPinConversation({super.key});

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final model = Provider.of<TUIGroupProfileModel>(context);
    final isPined = model.conversation?.isPinned ?? false;
    return Column(
      children: [
        ITimUikitOperationItem(
          isEmpty: false,
          operationName: TIM_t("置顶聊天"),
          type: "switch",
          isUseCheckedBoxOnWide: true,
          operationValue: isPined,
          radiusTopLeft: 6.r,
          radiusTopRight: 6.r,
          onSwitchChange: (value) {
            model.pinedConversation(value);
          },
        ),
        Stack(
          children: [
            Container(
              color: Colors.white,
              height: 1,
              margin: EdgeInsets.symmetric(horizontal: 8.5.w),
            ),
            Container(
              color: '#ECEAEA'.toColor().withOpacity(0.5),
              height: 1,
              margin: EdgeInsets.symmetric(horizontal: 16.w + 8.5.w),
            ),
          ],
        ),
      ],
    );
  }
}
