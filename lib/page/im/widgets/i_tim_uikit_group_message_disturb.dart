import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_operation_item.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ITimUikitGroupMessageDisturb extends TIMUIKitStatelessWidget {
  ITimUikitGroupMessageDisturb({super.key});

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final model = Provider.of<TUIGroupProfileModel>(context);
    final isShowDisturb =
        model.groupInfo?.groupType == "Meeting" ? false : true;
    final isDisturb = model.conversation?.recvOpt != 0;
    if (!isShowDisturb) {
      return Container();
    }
    return ITimUikitOperationItem(
      isEmpty: false,
      operationName: TIM_t("消息免打扰"),
      type: "switch",
      isUseCheckedBoxOnWide: true,
      operationValue: isDisturb,
      radiusBottomLeft: 6.r,
      radiusBottomRight: 6.r,
      onSwitchChange: (value) {
        model.setMessageDisturb(value);
      },
    );
  }
}
