import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';

class ITimUikitChatGroupTipsElem extends StatefulWidget {
  final V2TimGroupTipsElem groupTipsElem;
  final List<V2TimGroupMemberFullInfo?> groupMemberList;
  const ITimUikitChatGroupTipsElem(
      {super.key, required this.groupMemberList, required this.groupTipsElem});

  @override
  State<ITimUikitChatGroupTipsElem> createState() =>
      _ITimUikitChatGroupTipsElemState();
}

class _ITimUikitChatGroupTipsElemState
    extends TIMUIKitState<ITimUikitChatGroupTipsElem> {
  String groupTipsAbstractText = "";
  @override
  void initState() {
    super.initState();
    getText();
  }

  void getText() async {
    String text = await MessageUtils.groupTipsMessageAbstract(
        widget.groupTipsElem, widget.groupMemberList);
    if (text.contains('修改群头像为 http')) {
      text = text.split(' ').first;
      text = text.replaceRange(text.length - 1, text.length, '');
      text = text.replaceAll('群头像', '了群头像');
    }
    setState(() {
      groupTipsAbstractText = text;
    });
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      child: Text(
        groupTipsAbstractText,
        softWrap: true,
        textAlign: TextAlign.center,
        // textAlign: TextAlign.center,
        style: TextStyle(color: '9DA1A4'.toColor(), fontSize: 12.sp).pfRegular,
      ),
    );
  }
}
