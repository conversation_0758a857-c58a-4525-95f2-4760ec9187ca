import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/widget/custom_dialog.dart';
import 'package:npemployee/page/im/widgets/i_tim_ui_group_member_search.dart';
import 'package:npemployee/page/im/widgets/i_tui_group_member_list.dart';
import 'package:npemployee/page/im/widgets/i_tui_group_member_list_no_search.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/widgets/tim_ui_group_member_search.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/group_member_list.dart';

GlobalKey<_ITransimitGroupOwnerSelectState> selectNewGroupOwnerKey =
    GlobalKey();

class ITransimitGroupOwnerSelect extends StatefulWidget {
  final String? groupID;
  final TUIGroupProfileModel model;
  final ValueChanged<List<V2TimGroupMemberFullInfo>>? onSelectedMember;
  const ITransimitGroupOwnerSelect({
    super.key,
    this.groupID,
    required this.model,
    this.onSelectedMember,
  });

  @override
  State<ITransimitGroupOwnerSelect> createState() =>
      _ITransimitGroupOwnerSelectState();
}

class _ITransimitGroupOwnerSelectState
    extends TIMUIKitState<ITransimitGroupOwnerSelect> {
  final CoreServicesImpl _coreServicesImpl = serviceLocator<CoreServicesImpl>();
  List<V2TimGroupMemberFullInfo> selectedMember = [];
  List<V2TimGroupMemberFullInfo?>? searchMemberList;
  String? searchText;

  @override
  void dispose() {
    super.dispose();
  }

  bool isSearchTextExist(String? searchText) {
    return searchText != null && searchText != "";
  }

  handleSearchGroupMembers(String searchText, context) async {
    searchText = searchText;
    List<V2TimGroupMemberFullInfo?> currentGroupMember = widget
        .model.groupMemberList
        .where(
            (element) => element?.userID != _coreServicesImpl.loginInfo.userID)
        .toList();
    final res =
        await widget.model.searchGroupMember(V2TimGroupMemberSearchParam(
      keywordList: [searchText],
      groupIDList: [widget.model.groupInfo!.groupID],
      isSearchMemberNameCard: true,
      isSearchMemberUserID: true,
      isSearchMemberNickName: true,
      isSearchMemberRemark: true,
    ));

    if (res.code == 0) {
      List<V2TimGroupMemberFullInfo?> list = [];
      final searchResult = res.data!.groupMemberSearchResultItems!;
      searchResult.forEach((key, value) {
        if (value is List) {
          for (V2TimGroupMemberFullInfo item in value) {
            list.add(item);
          }
        }
      });

      currentGroupMember = list;
    } else {
      currentGroupMember = [];
    }
    setState(() {
      searchMemberList =
          isSearchTextExist(searchText) ? currentGroupMember : null;
    });
  }

  onSubmit() {
    if (widget.onSelectedMember != null) {
      widget.onSelectedMember!(selectedMember);
    }
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;

    Widget memberBody() {
      return ITuiGroupMemberListNoSearch(
        customTopArea: PlatformUtils().isWeb
            ? null
            : ITimUiGroupMemberSearch(
                onTextChange: (text) => handleSearchGroupMembers(text, context),
              ),
        memberList: (searchMemberList ?? widget.model.groupMemberList)
            .where((element) =>
                element?.userID != _coreServicesImpl.loginInfo.userID)
            .toList(),
        canSlideDelete: false,
        canSelectMember: true,
        maxSelectNum: 1,
        onSelectedMemberChange: (member) {
          selectedMember = member;
          setState(() {});
        },
        touchBottomCallBack: () {},
      );
    }

    return TUIKitScreenUtils.getDeviceWidget(
        context: context,
        defaultWidget: Scaffold(
            appBar: CommonNav(
              title: '转让群主',
              rightWidget: [
                TextButton(
                  onPressed: () {
                    showDialog(
                        context: context,
                        builder: (_) {
                          return CustomDialog(
                            title: '提示',
                            content:
                                '确定选择${selectedMember.first.nickName}成为新群主，您将自动放弃群主身份',
                            leftButtonText: '取消',
                            rightButtonText: '确认',
                            leftButtonColor: 'ECECEC'.toColor(),
                            rightButtonColor: AppTheme.colorBlue,
                            leftTextColor: 'B3B3B3'.toColor(),
                            rightTextColor: Colors.white,
                            onLeft: () {
                              Navigator.pop(_);
                            },
                            onRight: () {
                              if (selectedMember.isNotEmpty) {
                                Navigator.pop(_);
                                Navigator.pop(context, selectedMember);
                              }
                            },
                          );
                        });
                  },
                  child: Text(
                    TIM_t("完成"),
                    style: TextStyle(
                      color: '000000'.toColor(),
                      fontSize: 15.sp,
                    ).pfMedium,
                  ),
                )
              ],
            ),
            body: memberBody()),
        desktopWidget: memberBody());
  }
}
