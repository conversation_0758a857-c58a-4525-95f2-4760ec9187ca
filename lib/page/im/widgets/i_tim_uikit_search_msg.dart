// ignore_for_file: must_be_immutable, unused_import

import 'package:flutter/material.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_search_item.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/data_services/conversation/conversation_services.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_search_view_model.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_item.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_folder.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/tim_uikit_search_msg_detail.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_showAll.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';

import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';

class ITimUikitSearchMsg extends TIMUIKitStatelessWidget {
  List<V2TimMessageSearchResultItem?> msgList;
  int totalMsgCount;
  String keyword;
  final Function(V2TimConversation, V2TimMessage?) onTapConversation;
  final model = serviceLocator<TUISearchViewModel>();
  final Function(V2TimConversation, String) onEnterConversation;
  final List<ContactModel> contacts;
  ITimUikitSearchMsg(
      {required this.msgList,
      required this.keyword,
      required this.totalMsgCount,
      Key? key,
      required this.onTapConversation,
      required this.onEnterConversation,
      required this.contacts})
      : super(key: key);

  Widget _renderShowALl(bool isShowMore) {
    return (isShowMore == true)
        ? TIMUIKitSearchShowALl(
            textShow: TIM_t("更多聊天记录"),
            onClick: () => {model.searchMsgByKey(keyword, false)},
          )
        : Container();
  }

  Map<String, dynamic> _getUserDetails(String? userID) {
    if (userID == null) {
      return {};
    }

    Map<String, dynamic> mapA = {};
    List<Map<String, String>> list = [];
    void _findUserInContacts(List<ContactModel> contacts, String userID) {
      for (var contact in contacts) {
        for (var user in contact.userList) {
          if (user.id.toString() == userID) {
            Map<String, String> mapB = {
              'department': contact.department.name,
              'roles': user.roles.join(', ')
            };
            list.add(mapB);
            mapA['guid'] = user.guid;
            mapA['mobile'] = user.mobile;
            mapA['list'] = list;
          }
        }
        if (contact.children != null && contact.children!.isNotEmpty) {
          _findUserInContacts(contact.children!, userID);
        }
      }
    }

    _findUserInContacts(contacts, userID);
    return mapA;
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    List<V2TimConversation?> _conversationList =
        Provider.of<TUISearchViewModel>(context).conversationList;

    if (msgList.isNotEmpty) {
      return TIMUIKitSearchFolder(folderName: TIM_t("聊天记录"), children: [
        ...msgList.map((conv) {
          V2TimConversation? conversation;
          String? departmentNames;
          final index = _conversationList.indexWhere(
              (item) => item!.conversationID == conv?.conversationID);
          if (index > -1) {
            conversation = _conversationList[index]!;
            Map userMap = _getUserDetails(conversation.userID);
            conversation.contactUserInfo = userMap;
            if (userMap.isEmpty) {
              departmentNames = '';
            } else {
              List<Map<String, String>> list =
                  List<Map<String, String>>.from(userMap['list']);
              departmentNames =
                  list.map((item) => item['department']).join(' | ');
            }
          }
          if (conversation == null) {
            return Container();
          }
          final option1 = conv?.messageCount;
          return ITimUikitSearchItem(
            onClick: () async {
              onEnterConversation(conversation!, keyword);
            },
            faceUrl: conversation.faceUrl ?? "",
            showName: conversation.showName ?? "",
            departNames: departmentNames ?? "",
            lineOne: conversation.showName ?? "",
            lineTwo: TIM_t_para("{{option1}}条相关聊天记录", "$option1条相关聊天记录")(
                option1: option1),
          );
        }).toList(),
        _renderShowALl(totalMsgCount > msgList.length)
      ]);
    } else {
      return Container();
    }
  }
}
