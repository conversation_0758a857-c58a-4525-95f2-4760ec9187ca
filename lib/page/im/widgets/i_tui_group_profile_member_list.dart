import 'package:flutter/material.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/page/im/widgets/i_tim_ui_group_member_search.dart';
import 'package:npemployee/page/im/widgets/i_tui_group_member_list.dart';
import 'package:npemployee/page/im/widgets/i_tui_group_member_list_no_search.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart' as tui;
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/widgets/tim_ui_group_member_search.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/group_member_list.dart';

class ITuiGroupProfileMemberList extends StatefulWidget {
  List<V2TimGroupMemberFullInfo?> memberList;
  TUIGroupProfileModel model;
  ITuiGroupProfileMemberList({
    super.key,
    required this.memberList,
    required this.model,
  });

  @override
  State<ITuiGroupProfileMemberList> createState() =>
      _ITuiGroupProfileMemberListState();
}

class _ITuiGroupProfileMemberListState
    extends TIMUIKitState<ITuiGroupProfileMemberList> {
  List<V2TimGroupMemberFullInfo?>? searchMemberList;
  String? searchText;

  _kickedOffMember(String userID) async {
    widget.model.kickOffMember([userID]);
  }

  bool isSearchTextExist(String? searchText) {
    return searchText != null && searchText != "";
  }

  handleSearchGroupMembers(String searchText, context) async {
    searchText = searchText;
    List<V2TimGroupMemberFullInfo?> currentGroupMember =
        Provider.of<TUIGroupProfileModel>(context, listen: false)
            .groupMemberList;

    if (!isSearchTextExist(searchText)) {
      setState(() {
        searchMemberList = null;
      });
      return;
    }

    final res =
        await widget.model.searchGroupMember(V2TimGroupMemberSearchParam(
      keywordList: [searchText],
      groupIDList: [widget.model.groupInfo!.groupID],
    ));

    if (res.code == 0) {
      List<V2TimGroupMemberFullInfo?> list = [];
      final searchResult = res.data!.groupMemberSearchResultItems!;
      searchResult.forEach((key, value) {
        if (value is List) {
          for (V2TimGroupMemberFullInfo item in value) {
            list.add(item);
          }
        }
      });

      currentGroupMember = list;
    } else {
      currentGroupMember = [];
    }
    setState(() {
      searchMemberList = currentGroupMember;
    });
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor() == tui.DeviceType.Desktop;
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: widget.model),
      ],
      builder: (BuildContext context, Widget? w) {
        final TUIGroupProfileModel groupProfileModel =
            Provider.of<TUIGroupProfileModel>(context);
        String option1 = groupProfileModel.groupInfo?.memberCount.toString() ??
            widget.memberList.length.toString();
        if (isDesktopScreen) {
          return GroupProfileMemberList(
            customTopArea: PlatformUtils().isWeb
                ? null
                : GroupMemberSearchTextField(
                    onTextChange: (text) =>
                        handleSearchGroupMembers(text, context),
                  ),
            memberList: searchMemberList ?? groupProfileModel.groupMemberList,
            removeMember: _kickedOffMember,
            touchBottomCallBack: () {},
            onTapMemberItem: (memberInfo, details) {
              if (widget.model.onClickUser != null) {
                widget.model.onClickUser!(memberInfo, details);
              }
            },
          );
        }
        return Scaffold(
            appBar: CommonNav(
                title: TIM_t_para("群成员({{option1}}人)", "群成员($option1人)")(
                    option1: option1)),
            body: ITuiGroupMemberListNoSearch(
              customTopArea: PlatformUtils().isWeb
                  ? null
                  : ITimUiGroupMemberSearch(
                      onTextChange: (text) =>
                          handleSearchGroupMembers(text, context),
                    ),
              memberList: searchMemberList ?? groupProfileModel.groupMemberList,
              // removeMember: _kickedOffMember,
              touchBottomCallBack: () {},
              canSlideDelete: false,
              onTapMemberItem: (memberInfo, details) {
                // if (widget.model.onClickUser != null) {
                //   widget.model.onClickUser!(memberInfo, details);
                // }
                NavigatorUtils.push(context, MessageRouter.userProfilePage3,
                    arguments: {'userId': memberInfo.userID});
              },
            ));
      },
    );
  }
}
