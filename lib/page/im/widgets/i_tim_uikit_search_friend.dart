import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_search_item.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_item.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_folder.dart';

import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_search_view_model.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_showAll.dart';

import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';

class ITimUikitSearchFriend extends StatefulWidget {
  final List<V2TimFriendInfoResult> friendResultList;
  final Function(V2TimConversation, V2TimMessage?) onTapConversation;
  final List<ContactModel> contacts;
  const ITimUikitSearchFriend(
      {super.key,
      required this.friendResultList,
      required this.onTapConversation,
      required this.contacts});

  @override
  State<StatefulWidget> createState() => _ITimUikitSearchFriendState();
}

class _ITimUikitSearchFriendState extends TIMUIKitState<ITimUikitSearchFriend> {
  bool isShowAll = false;
  int defaultShowLines = 3;
  List<ContactModel> _contacts = [];

  Widget _renderShowALl(int currentLines) {
    return (isShowAll == false && currentLines > defaultShowLines)
        ? TIMUIKitSearchShowALl(
            textShow: TIM_t("全部联系人"),
            onClick: () => setState(() {
              isShowAll = true;
            }),
          )
        : Container();
  }

  Map<String, dynamic> _getUserDetails(String? userID) {
    if (userID == null) {
      return {};
    }

    Map<String, dynamic> mapA = {};
    List<Map<String, String>> list = [];
    void _findUserInContacts(List<ContactModel> contacts, String userID) {
      for (var contact in contacts) {
        for (var user in contact.userList) {
          if (user.id.toString() == userID) {
            Map<String, String> mapB = {
              'department': contact.department.name,
              'roles': user.roles.join(', ')
            };
            list.add(mapB);
            mapA['guid'] = user.guid;
            mapA['mobile'] = user.mobile;
            mapA['list'] = list;
          }
        }
        if (contact.children != null && contact.children!.isNotEmpty) {
          _findUserInContacts(contact.children!, userID);
        }
      }
    }

    _findUserInContacts(_contacts, userID);
    return mapA;
  }

  @override
  void initState() {
    super.initState();
    _contacts = widget.contacts;
  }

  @override
  void didUpdateWidget(covariant ITimUikitSearchFriend oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.contacts != oldWidget.contacts) {
      setState(() {
        _contacts = widget.contacts;
      });
    }
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    List<V2TimConversation?> _conversationList =
        Provider.of<TUISearchViewModel>(context).conversationList;

    List<V2TimFriendInfoResult> filteredFriendResultList =
        widget.friendResultList.where((friend) {
      int index = _conversationList
          .indexWhere((conv) => friend.friendInfo?.userID == conv?.userID);
      return index == -1 ? false : true;
    }).toList();

    List<V2TimFriendInfoResult> halfFilteredFriendResultList = isShowAll
        ? filteredFriendResultList
        : filteredFriendResultList.sublist(
            0, min(defaultShowLines, filteredFriendResultList.length));

    if (filteredFriendResultList.isNotEmpty) {
      return TIMUIKitSearchFolder(folderName: TIM_t("联系人"), children: [
        ...halfFilteredFriendResultList.map((conv) {
          int convIndex = _conversationList
              .indexWhere((item) => conv.friendInfo?.userID == item?.userID);
          V2TimConversation conversation = _conversationList[convIndex]!;
          Map userMap = _getUserDetails(conversation.userID);
          conversation.contactUserInfo = userMap;
          List<Map<String, String>> list =
              List<Map<String, String>>.from(userMap['list']);
          String departmentNames =
              list.map((item) => item['department']).join(' | ');
          late String? showNickName;
          if (conv.friendInfo?.friendRemark != null &&
              conv.friendInfo?.friendRemark != "") {
            showNickName = conv.friendInfo?.friendRemark;
          } else if (conv.friendInfo?.userProfile?.nickName != null &&
              conv.friendInfo?.userProfile?.nickName != "") {
            showNickName = conv.friendInfo?.userProfile?.nickName;
          } else {
            showNickName = conv.friendInfo?.userID;
          }

          return ITimUikitSearchItem(
            onClick: () {
              widget.onTapConversation(conversation, null);
            },
            faceUrl: conv.friendInfo?.userProfile?.faceUrl ?? "",
            showName: "",
            departNames: departmentNames,
            lineOne: showNickName ?? '',
            // lineOne: conversation.userID!,
            // lineTwo: showNickName!,
          );
        }).toList(),
        _renderShowALl(filteredFriendResultList.length),
      ]);
    } else {
      return Container();
    }
  }
}
