import 'package:azlistview_all_platforms/azlistview_all_platforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable_plus_plus/flutter_slidable_plus_plus.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/optimize_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart' as tui;
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/az_list_view.dart';

class ITuiGroupMemberList extends StatefulWidget {
  static const String atAllUserId = "__kImSDK_MesssageAtALL__";
  final List<V2TimGroupMemberFullInfo?> memberList;
  final Function(String userID)? removeMember;
  final bool canSlideDelete;
  final bool canSelectMember;
  final bool canAtAll;
  // when the @ need filter some group types
  final String? groupType;
  final Function(List<V2TimGroupMemberFullInfo> selectedMember)?
      onSelectedMemberChange;
  // notice: onTapMemberItem and onSelectedMemberChange use together will triger together
  final Function(
          V2TimGroupMemberFullInfo memberInfo, TapDownDetails? tapDetails)?
      onTapMemberItem;
  // When sliding to the bottom bar callBack
  final Function()? touchBottomCallBack;

  final int? maxSelectNum;

  final Widget? customTopArea;

  final void Function()? onSubmit;

  const ITuiGroupMemberList({
    super.key,
    required this.memberList,
    this.groupType,
    this.removeMember,
    this.canSlideDelete = true,
    this.canSelectMember = false,
    this.canAtAll = false,
    this.onSelectedMemberChange,
    this.onTapMemberItem,
    this.customTopArea,
    this.touchBottomCallBack,
    this.maxSelectNum,
    this.onSubmit,
  });

  @override
  State<ITuiGroupMemberList> createState() => _ITuiGroupMemberListState();
}

class _ITuiGroupMemberListState extends TIMUIKitState<ITuiGroupMemberList> {
  List<V2TimGroupMemberFullInfo> selectedMemberList = [];
  String searchQuery = '';
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();

  @override
  void dispose() {
    searchController.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }

  _getShowName(V2TimGroupMemberFullInfo? item) {
    final friendRemark = item?.friendRemark ?? "";
    final nameCard = item?.nameCard ?? "";
    final nickName = item?.nickName ?? "";
    final userID = item?.userID ?? "";
    return friendRemark.isNotEmpty
        ? friendRemark
        : nameCard.isNotEmpty
            ? nameCard
            : nickName.isNotEmpty
                ? nickName
                : userID;
  }

  // Filter member list based on search query
  List<V2TimGroupMemberFullInfo?> _getFilteredMemberList(
      List<V2TimGroupMemberFullInfo?> memberList) {
    if (searchQuery.isEmpty) {
      return memberList;
    }

    return memberList.where((member) {
      final showName = _getShowName(member);
      return showName.toLowerCase().contains(searchQuery.toLowerCase());
    }).toList();
  }

  List<ISuspensionBeanImpl> _getShowList(
      List<V2TimGroupMemberFullInfo?> memberList) {
    // Apply search filter
    final filteredMemberList = _getFilteredMemberList(memberList);

    final List<ISuspensionBeanImpl> showList = List.empty(growable: true);
    for (var i = 0; i < filteredMemberList.length; i++) {
      final item = filteredMemberList[i];
      final showName = _getShowName(item);
      if (item?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER ||
          item?.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_ADMIN) {
        showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: "@"));
      } else {
        String pinyin = PinyinHelper.getPinyinE(showName);
        String tag = pinyin.substring(0, 1).toUpperCase();
        if (RegExp("[A-Z]").hasMatch(tag)) {
          showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: tag));
        } else {
          showList.add(ISuspensionBeanImpl(memberInfo: item, tagIndex: "#"));
        }
      }
    }

    SuspensionUtil.sortListBySuspensionTag(showList);

    // add @everyone item
    if (widget.canAtAll && searchQuery.isEmpty) {
      final canAtGroupType = ["Work", "Public", "Meeting"];
      if (canAtGroupType.contains(widget.groupType)) {
        showList.insert(
            0,
            ISuspensionBeanImpl(
                memberInfo: V2TimGroupMemberFullInfo(
                    userID: ITuiGroupMemberList.atAllUserId,
                    nickName: TIM_t("所有人")),
                tagIndex: ""));
      }
    }

    return showList;
  }

  // Check if a member is an existing group member
  bool _isExistingGroupMember(V2TimGroupMemberFullInfo memberInfo) {
    // Check if this is the "At All" special user
    if (memberInfo.userID == ITuiGroupMemberList.atAllUserId) {
      return false; // Allow selecting "At All"
    }

    // If existing members are passed to the widget, check against them
    // This logic should be customized based on your actual requirements
    if (widget.memberList.isNotEmpty) {
      // Check if this member is marked as non-selectable
      // For example, if they are already in the group
      return false; // By default, allow selection for all members
    }

    return false;
  }

  Widget _buildListItem(
      BuildContext context, V2TimGroupMemberFullInfo memberInfo) {
    final theme = Provider.of<TUIThemeViewModel>(context).theme;
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor() == tui.DeviceType.Desktop;
    final isGroupMember =
        memberInfo.role == GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_MEMBER;
    final isExistingMember = _isExistingGroupMember(memberInfo);
    final isSelected = selectedMemberList
        .where((element) => element.userID == memberInfo.userID)
        .toList()
        .isNotEmpty;

    return Container(
        color: Colors.white,
        child: Slidable(
            endActionPane: widget.canSlideDelete && isGroupMember
                ? ActionPane(motion: const DrawerMotion(), children: [
                    SlidableAction(
                      onPressed: (_) {
                        if (widget.removeMember != null) {
                          widget.removeMember!(memberInfo.userID);
                        }
                      },
                      flex: 1,
                      backgroundColor:
                          theme.cautionColor ?? CommonColor.cautionColor,
                      autoClose: true,
                      label: TIM_t("删除"),
                    )
                  ])
                : null,
            child: Column(children: [
              ListTile(
                tileColor: Colors.black,
                title: Row(
                  children: [
                    if (widget.canSelectMember)
                      Container(
                        margin: const EdgeInsets.only(right: 10),
                        width: 18,
                        height: 18,
                        child: isExistingMember
                            ? Image.asset(
                                'assets/png/im/group_add_no_selected.png')
                            : isSelected
                                ? Image.asset(
                                    'assets/png/im/group_add_selected.png')
                                : Image.asset(
                                    'assets/png/im/group_add_unselected.png'),
                      ),
                    Container(
                      width: isDesktopScreen ? 30 : 36,
                      height: isDesktopScreen ? 30 : 36,
                      margin: const EdgeInsets.only(right: 10),
                      child: Avatar(
                        faceUrl: memberInfo.faceUrl ?? "",
                        showName: _getShowName(memberInfo),
                        type: 1,
                      ),
                    ),
                    Text(_getShowName(memberInfo),
                        style: TextStyle(
                                fontSize: isDesktopScreen ? 14 : 14.sp,
                                color: '000000'.toColor())
                            .pfMedium),
                    memberInfo.role ==
                            GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER
                        ? Container(
                            margin: const EdgeInsets.only(left: 5),
                            padding: const EdgeInsets.fromLTRB(5, 0, 5, 0),
                            decoration: BoxDecoration(
                              border: Border.all(
                                  color: theme.ownerColor ??
                                      CommonColor.ownerColor,
                                  width: 1),
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(4.0)),
                            ),
                            child: Text(TIM_t("群主"),
                                style: TextStyle(
                                  color: theme.ownerColor,
                                  fontSize: isDesktopScreen ? 10 : 12,
                                )),
                          )
                        : memberInfo.role ==
                                GroupMemberRoleType
                                    .V2TIM_GROUP_MEMBER_ROLE_ADMIN
                            ? Container(
                                margin: const EdgeInsets.only(left: 5),
                                padding: const EdgeInsets.fromLTRB(5, 0, 5, 0),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: theme.adminColor ??
                                          CommonColor.adminColor,
                                      width: 1),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(4.0)),
                                ),
                                child: Text(TIM_t("管理员"),
                                    style: TextStyle(
                                      color: theme.adminColor,
                                      fontSize: 12,
                                    )),
                              )
                            : Container()
                  ],
                ),
                onTap: () {
                  if (widget.onTapMemberItem != null) {
                    widget.onTapMemberItem!(memberInfo, null);
                  }
                  if (widget.canSelectMember && !isExistingMember) {
                    final isChecked = selectedMemberList.contains(memberInfo);
                    if (isChecked) {
                      selectedMemberList.remove(memberInfo);
                    } else {
                      if (widget.maxSelectNum != null &&
                          selectedMemberList.length >= widget.maxSelectNum!) {
                        return;
                      }
                      selectedMemberList.add(memberInfo);
                    }
                    if (widget.onSelectedMemberChange != null) {
                      widget.onSelectedMemberChange!(selectedMemberList);
                    }
                    setState(() {});
                  }
                },
              ),
            ])));
  }

  static Widget getSusItem(BuildContext context, TUITheme theme, String tag,
      {double susHeight = 30}) {
    if (tag == '@') {
      tag = TIM_t("群主");
    }
    return Container(
      height: susHeight,
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.only(left: 16.0),
      color: 'F7F8FD'.toColor(),
      alignment: Alignment.centerLeft,
      child: Text(
        tag,
        softWrap: true,
        style: TextStyle(
          fontSize: 11.sp,
          color: '80848A'.toColor(),
        ).pfMedium,
      ),
    );
  }

  Widget _buildSearchField(TUITheme theme) {
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 8.h),
      color: Colors.white,
      child: Container(
        decoration: BoxDecoration(
            color: 'F7F8FD'.toColor(),
            borderRadius: BorderRadius.circular(5.5.r),
            border: Border.all(color: 'E9E9E9'.toColor(), width: 0.5)),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        height: 44.h,
        child: Row(
          children: [
            Image.asset('assets/png/search.png',
                width: 17, height: 17, color: 'BABABA'.toColor()),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: searchController,
                focusNode: searchFocusNode,
                decoration: InputDecoration(
                  hintText: TIM_t("请输入搜索内容"),
                  hintStyle: TextStyle(
                    color: 'BABABA'.toColor(),
                    fontSize: 15.sp,
                  ).pfRegular,
                  border: InputBorder.none,
                  isDense: true,
                ),
                style: TextStyle(
                  color: '333333'.toColor(),
                  fontSize: 15.sp,
                ).pfRegular,
                onChanged: (value) {
                  setState(() {
                    searchQuery = value;
                  });
                },
              ),
            ),
            if (searchQuery.isNotEmpty)
              GestureDetector(
                onTap: () {
                  setState(() {
                    searchQuery = '';
                    searchController.clear();
                  });
                },
                child: Icon(Icons.cancel, color: theme.weakTextColor, size: 18),
              ),
          ],
        ),
      ),
    );
  }

  // 底部已选择人员面板
  Widget _buildSelectedMembersPanel(BuildContext context, TUITheme theme) {
    if (selectedMemberList.isEmpty) {
      return Container();
    }

    return Container(
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.fromLTRB(16, 12, 16, 12),
      decoration: BoxDecoration(color: Colors.white),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 70,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: selectedMemberList.length,
                itemBuilder: (context, index) {
                  V2TimGroupMemberFullInfo member = selectedMemberList[index];
                  String showName = _getShowName(member);
                  String faceUrl = member.faceUrl ?? "";

                  return Container(
                    alignment: Alignment.centerLeft,
                    width: 50,
                    child: Column(
                      children: [
                        Stack(
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              margin: const EdgeInsets.only(right: 10),
                              child: Avatar(
                                faceUrl: faceUrl,
                                showName: showName,
                                type: 1,
                              ),
                            ),
                            Positioned(
                              right: 5,
                              top: -5,
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    selectedMemberList.removeAt(index);
                                    if (widget.onSelectedMemberChange != null) {
                                      widget.onSelectedMemberChange!(
                                          selectedMemberList);
                                    }
                                  });
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(2),
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.cancel,
                                    color: theme.primaryColor,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 5),
                        Text(
                          showName,
                          style: TextStyle(
                            color: theme.darkTextColor,
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(width: 10),
          if (widget.onSubmit != null)
            GestureDetector(
              onTap: () {
                if (widget.onSubmit != null) {
                  widget.onSubmit!();
                  // If there's a callback to handle the confirmation, you can call it here
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.primaryColor,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  TIM_t('确定(${selectedMemberList.length})'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;

    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor() == tui.DeviceType.Desktop;

    final throteFunction =
        OptimizeUtils.throttle((ScrollNotification notification) {
      final pixels = notification.metrics.pixels;
      // 总像素高度
      final maxScrollExtent = notification.metrics.maxScrollExtent;
      // 滑动百分比
      final progress = pixels / maxScrollExtent;
      if (progress >= 0.9 && widget.touchBottomCallBack != null) {
        widget.touchBottomCallBack!();
      }
    }, 300);
    final showList = _getShowList(widget.memberList);
    return Container(
      color: isDesktopScreen ? null : 'F7F8FD'.toColor(),
      child: Column(
        children: [
          widget.customTopArea != null ? widget.customTopArea! : Container(),
          _buildSearchField(theme),
          Expanded(
              child: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification notification) {
              throteFunction(notification);
              return true;
            },
            child: (showList.isEmpty)
                ? Center(
                    child: Text(
                      searchQuery.isEmpty ? TIM_t("暂无群成员") : TIM_t("未找到相关群成员"),
                      style:
                          TextStyle(fontSize: 14.sp, color: '333333'.toColor())
                              .pfRegular,
                    ),
                  )
                : Container(
                    padding: isDesktopScreen
                        ? const EdgeInsets.symmetric(horizontal: 16)
                        : null,
                    child: AZListViewContainer(
                        memberList: showList,
                        susItemBuilder: (context, index) {
                          final model = showList[index];
                          return getSusItem(
                              context, theme, model.getSuspensionTag());
                        },
                        itemBuilder: (context, index) {
                          final memberInfo = showList[index].memberInfo
                              as V2TimGroupMemberFullInfo;

                          return _buildListItem(context, memberInfo);
                        }),
                  ),
          )),
          // Add selected members panel at the bottom
          if (widget.canSelectMember)
            _buildSelectedMembersPanel(context, theme),
        ],
      ),
    );
  }
}
