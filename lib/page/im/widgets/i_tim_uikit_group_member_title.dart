import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/page/im/group/add_group.dart';
import 'package:npemployee/page/im/widgets/i_tui_delete_group_member.dart';
import 'package:npemployee/page/im/widgets/i_tui_group_profile_member_list.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/tim_uikit_wide_modal_operation_key.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart' as tui;
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/group_member/tui_add_group_member.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/group_member/tui_delete_group_member.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/group_member/tui_group_member_list.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/wide_popup.dart';

class ITimUikitGroupMemberTitle extends TIMUIKitStatelessWidget {
  ITimUikitGroupMemberTitle({super.key});

  List<V2TimGroupMemberFullInfo?> _getMemberList(memberList, int showRange) {
    if (memberList.length > showRange) {
      return memberList.getRange(0, showRange).toList();
    } else {
      return memberList;
    }
  }

  _getShowName(V2TimGroupMemberFullInfo? item) {
    final friendRemark = item?.friendRemark ?? "";
    final nickName = item?.nickName ?? "";
    final nameCard = item?.nameCard ?? "";
    // final userID = item?.userID;
    final showName = nameCard != "" ? nameCard : nickName;
    return friendRemark != "" ? friendRemark : showName;
  }

  List<Widget> _groupMemberListBuilder(List memberList, TUITheme theme,
      TUIGroupProfileModel model, int showRange, BuildContext c) {
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor() == tui.DeviceType.Desktop;
    double itemWidth = (ScreenUtil().screenWidth - 49.w - 20.w) / 5;
    return _getMemberList(memberList, showRange).map((element) {
      final faceUrl = element?.faceUrl ?? "";
      final showName = _getShowName(element);
      return InkWell(
        onTapDown: (details) {
          NavigatorUtils.push(c, MessageRouter.userProfilePage3,
              arguments: {'userId': element!.userID});
          // if (model.onClickUser != null && element?.userID != null) {
          //   model.onClickUser!(element!, details);
          // }
        },
        child: Container(
          width: isDesktopScreen ? 36 : itemWidth,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: isDesktopScreen ? 36 : 40,
                height: isDesktopScreen ? 36 : 40,
                child: Avatar(
                  borderRadius:
                      isDesktopScreen ? BorderRadius.circular(18) : null,
                  faceUrl: faceUrl,
                  showName: showName,
                  type: 1,
                ),
              ),
              if (!isDesktopScreen)
                Text(
                  showName,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                          overflow: TextOverflow.ellipsis,
                          color: '81848B'.toColor(),
                          fontSize: 10.sp,
                          height: 24.sp / 10.sp)
                      .pfRegular,
                )
            ],
          ),
        ),
      );
    }).toList();
  }

  List<Widget> _inviteMemberBuilder(bool isCanInviteMember,
      bool isCanKickOffMember, theme, BuildContext context) {
    return [];
  }

  void navigateToMemberList(BuildContext context, TUIGroupProfileModel model,
      List<V2TimGroupMemberFullInfo?> memberList) {
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;
    if (!isDesktopScreen) {
      Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ITuiGroupProfileMemberList(
                model: model, memberList: memberList),
          ));
    } else {
      final option1 = memberList.length.toString();
      TUIKitWidePopup.showPopupWindow(
          operationKey: TUIKitWideModalOperationKey.groupMembersList,
          context: context,
          width: MediaQuery.of(context).size.width * 0.5,
          height: MediaQuery.of(context).size.height * 0.8,
          title: TIM_t_para("群成员({{option1}}人)", "群成员($option1人)")(
              option1: option1),
          child: (onClose) =>
              GroupProfileMemberListPage(model: model, memberList: memberList));
    }
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;
    final model = Provider.of<TUIGroupProfileModel>(context);
    final memberAmount = model.groupInfo?.memberCount ?? 0;
    final option1 = memberAmount.toString();
    final memberList = model.groupMemberList;
    final isCanInviteMember = model.canInviteMember();
    final isCanKickOffMember = model.canKickOffMember();
    double itemWidth = (ScreenUtil().screenWidth - 49.w - 20.w) / 5;

    int showRange = isDesktopScreen ? 7 : (isCanKickOffMember ? 8 : 9);
    if (isDesktopScreen && isCanInviteMember) {
      showRange--;
    }
    if (isDesktopScreen && isCanKickOffMember) {
      showRange--;
    }

    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 8.5.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(6.r)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /* Container(
            padding: const EdgeInsets.only(bottom: 12),
            decoration: isDesktopScreen
                ? null
                : BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            color: theme.weakDividerColor ??
                                CommonColor.weakDividerColor))),
            child: InkWell(
              onTap: () async {
                navigateToMemberList(context, model, memberList);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(TIM_t("群成员"),
                      style: TextStyle(
                          color: theme.darkTextColor,
                          fontSize: isDesktopScreen ? 14 : 16)),
                  Row(
                    children: [
                      Text(
                        TIM_t_para("{{option1}}人", "$option1人")(
                            option1: option1),
                        style: TextStyle(
                            color: theme.darkTextColor,
                            fontSize: isDesktopScreen ? 14 : 16),
                      ),
                      Icon(
                        Icons.keyboard_arrow_right,
                        color: theme.weakTextColor,
                      ),
                    ],
                  )
                ],
              ),
            ),
          ), */
          if (isDesktopScreen)
            InkWell(
              onTap: () async {
                navigateToMemberList(context, model, memberList);
              },
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                      width: 1,
                      color: theme.weakDividerColor ??
                          CommonColor.weakDividerColor),
                  borderRadius: const BorderRadius.all(Radius.circular(4)),
                ),
                // height: 30,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.search,
                      color: hexToColor("979797"),
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(TIM_t("搜索"),
                        style: TextStyle(
                          color: theme.weakTextColor,
                          fontSize: 12,
                        )),
                  ],
                ),
              ),
            ),
          Container(
            child: Wrap(
              spacing: isDesktopScreen ? 10 : 5.w,
              runSpacing: 8.h,
              alignment: WrapAlignment.start,
              children: [
                ..._groupMemberListBuilder(
                    memberList, theme, model, showRange, context),
                if (isCanInviteMember)
                  GestureDetector(
                    onTap: () {
                      if (isDesktopScreen) {
                        TUIKitWidePopup.showPopupWindow(
                            context: context,
                            operationKey:
                                TUIKitWideModalOperationKey.addGroupMembers,
                            width: 350,
                            title: TIM_t("添加群成员"),
                            height: 460,
                            onSubmit: () {
                              addGroupMemberKey.currentState?.submitAdd();
                            },
                            child: (onClose) => AddGroupMemberPage(
                                  model: model,
                                  onClose: onClose,
                                  key: addGroupMemberKey,
                                ));
                      } else {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ImAddGroup(
                                    groupId: model.groupInfo?.groupID,
                                    members: model.groupMemberList)));
                      }
                    },
                    child: Container(
                      width: itemWidth,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Image.asset('assets/png/im/im_group_add_boarder.png',
                              width: 40, height: 40),
                          Image.asset('assets/png/im/im_group_add.png',
                              width: 13, height: 13),
                        ],
                      ),
                    ),
                  ),
                if (isCanKickOffMember)
                  GestureDetector(
                    onTap: () {
                      if (isDesktopScreen) {
                        TUIKitWidePopup.showPopupWindow(
                          operationKey:
                              TUIKitWideModalOperationKey.kickOffGroupMembers,
                          context: context,
                          width: 350,
                          title: TIM_t("删除群成员"),
                          height: 460,
                          onSubmit: () {
                            deleteGroupMemberKey.currentState?.submitDelete();
                          },
                          child: (onClose) => ITuiDeleteGroupMember(
                            model: model,
                            onClose: onClose,
                            key: deleteGroupMemberKey,
                          ),
                        );
                      } else {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ITuiDeleteGroupMember(
                                model: model,
                              ),
                            ));
                      }
                    },
                    child: Container(
                      width: itemWidth,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Image.asset('assets/png/im/im_group_add_boarder.png',
                              width: 40, height: 40),
                          Image.asset('assets/png/im/im_group_remove.png',
                              width: 13, height: 13),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (memberList.length > showRange)
            InkWell(
              child: Container(
                alignment: Alignment.center,
                margin: EdgeInsets.only(top: isDesktopScreen ? 12 : 16),
                child: Text(
                  TIM_t("查看更多群成员"),
                  style: TextStyle(
                          color: theme.weakTextColor,
                          fontSize: isDesktopScreen ? 12 : 12.sp)
                      .pfRegular,
                ),
              ),
              onTap: () async {
                navigateToMemberList(context, model, memberList);
              },
            ),
        ],
      ),
    );
  }
}
