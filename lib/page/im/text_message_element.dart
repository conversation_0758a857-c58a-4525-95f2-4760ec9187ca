import 'dart:async';
import 'dart:convert';

import 'package:extended_text/extended_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart' as v;
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_chat_separate_view_model.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitMessageItem/TIMUIKitMessageReaction/tim_uikit_message_reaction_show_panel.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitChat/TIMUIKitTextField/special_text/DefaultSpecialTextSpanBuilder.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/link_preview/link_preview_entry.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/link_preview/widgets/link_preview.dart';

class TextMessageElement extends StatefulWidget {
  final V2TimMessage message;
  final bool isFromSelf;
  final bool isShowJump;
  final VoidCallback clearJump;
  final TextStyle? fontStyle;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? textPadding;
  final bool? isShowMessageReaction;
  final List<CustomEmojiFaceData> customEmojiStickerList;

  const TextMessageElement(
      {super.key,
      required this.message,
      required this.isFromSelf,
      required this.isShowJump,
      required this.clearJump,
      this.fontStyle,
      this.borderRadius,
      this.isShowMessageReaction,
      this.backgroundColor,
      this.textPadding,
      this.customEmojiStickerList = const []});

  @override
  State<TextMessageElement> createState() => _TextMessageElementState();
}

class _TextMessageElementState extends State<TextMessageElement> {
  bool isShowJumpState = false;
  bool isShining = false;
  late TUIChatSeparateViewModel model;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // get the link preview info
      _getLinkPreview();
    });
  }

  @override
  void didUpdateWidget(TextMessageElement oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.message.msgID == null && widget.message.msgID != null) {
      _getLinkPreview();
    }
  }

  _showJumpColor() {
    if ((model.jumpMsgID != widget.message.msgID) &&
        (widget.message.msgID?.isNotEmpty ?? true)) {
      return;
    }
    isShining = true;
    int shineAmount = 6;
    setState(() {
      isShowJumpState = true;
    });
    Timer.periodic(const Duration(milliseconds: 300), (timer) {
      if (mounted) {
        setState(() {
          isShowJumpState = shineAmount.isOdd ? true : false;
        });
      }
      if (shineAmount == 0 || !mounted) {
        isShining = false;
        timer.cancel();
      }
      shineAmount--;
    });
    Future.delayed(const Duration(milliseconds: 100), () {
      widget.clearJump();
    });
  }

  // get the link preview info
  _getLinkPreview() {
    if (model.chatConfig.urlPreviewType !=
        UrlPreviewType.previewCardAndHyperlink) {
      return;
    }
    try {
      if (widget.message.localCustomData != null &&
          widget.message.localCustomData!.isNotEmpty) {
        final String localJSON = widget.message.localCustomData!;
        final LocalCustomDataModel? localPreviewInfo =
            LocalCustomDataModel.fromMap(json.decode(localJSON));
        // If [localCustomData] is not empty, check if the link preview info exists
        if (localPreviewInfo == null || localPreviewInfo.isLinkPreviewEmpty()) {
          // If not exists, get it
          _initLinkPreview();
        }
      } else {
        // It [localCustomData] is empty, get the link info
        _initLinkPreview();
      }
    } catch (e) {
      return null;
    }
  }

  _initLinkPreview() async {
    // Get the link preview info from extension, let it update the message UI automatically by providing a [onUpdateMessage].
    // The `onUpdateMessage` can use the `updateMessage()` from the [TIMUIKitChatController] directly.
    LinkPreviewEntry.getFirstLinkPreviewContent(
        message: widget.message,
        onUpdateMessage: (message) {
          model.updateMessageFromController(
              msgID: widget.message.msgID!, message: message);
        });
  }

  Widget? _renderPreviewWidget() {
    // If the link preview info from [localCustomData] is available, use it to render the preview card.
    // Otherwise, it will returns null.
    if (widget.message.localCustomData != null &&
        widget.message.localCustomData!.isNotEmpty) {
      try {
        final String localJSON = widget.message.localCustomData!;
        final LocalCustomDataModel? localPreviewInfo =
            LocalCustomDataModel.fromMap(json.decode(localJSON));
        if (localPreviewInfo != null &&
            !localPreviewInfo.isLinkPreviewEmpty()) {
          return Container(
            margin: const EdgeInsets.only(top: 8),
            child:
                // You can use this default widget [LinkPreviewWidget] to render preview card, or you can use custom widget.
                LinkPreviewWidget(linkPreview: localPreviewInfo),
          );
        } else {
          return null;
        }
      } catch (e) {
        return null;
      }
    } else {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    model = Provider.of<TUIChatSeparateViewModel>(context);
    final textWithLink = LinkPreviewEntry.getHyperlinksText(
        widget.message.textElem?.text ?? "",
        model.chatConfig.isSupportMarkdownForTextMessage,
        onLinkTap: model.chatConfig.onTapLink,
        isUseQQPackage:
            model.chatConfig.stickerPanelConfig?.useQQStickerPackage ?? true,
        isUseTencentCloudChatPackage: model.chatConfig.stickerPanelConfig
                ?.useTencentCloudChatStickerPackage ??
            true,
        isUseTencentCloudChatPackageOldKeys: model.chatConfig.stickerPanelConfig
                ?.useTencentCloudChatStickerPackageOldKeys ??
            false,
        customEmojiStickerList: widget.customEmojiStickerList,
        isEnableTextSelection: model.chatConfig.isEnableTextSelection ?? false);
    final borderRadius = widget.isFromSelf
        ? const BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(2),
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10))
        : const BorderRadius.only(
            topLeft: Radius.circular(2),
            topRight: Radius.circular(10),
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10));
    if ((model.jumpMsgID == widget.message.msgID)) {}
    if (widget.isShowJump) {
      if (!isShining) {
        Future.delayed(Duration.zero, () {
          _showJumpColor();
        });
      } else {
        if ((model.jumpMsgID == widget.message.msgID) &&
            (widget.message.msgID?.isNotEmpty ?? false)) {
          widget.clearJump();
        }
      }
    }

    /* final defaultStyle = widget.isFromSelf
        ? (theme.chatMessageItemFromSelfBgColor ??
            theme.lightPrimaryMaterialColor.shade50)
        : (theme.chatMessageItemFromOthersBgColor); */
    final defaultStyle =
        !widget.isFromSelf ? Colors.white : hexToColor('3377FF');

    final backgroundColor = isShowJumpState
        ? const Color.fromRGBO(245, 166, 35, 1)
        : (defaultStyle ?? widget.backgroundColor);
    final Color textColor = !widget.isFromSelf
        ? '#000000'.toColor().withOpacity(0.9)
        : '#FFFFFF'.toColor();

    return Container(
      padding: widget.textPadding ?? EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: widget.borderRadius ?? borderRadius,
      ),
      constraints:
          BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // If the [elemType] is text message, it will not be null here.
          // You can render the widget from extension directly, with a [TextStyle] optionally.
          model.chatConfig.urlPreviewType != UrlPreviewType.none
              ? textWithLink!(
                  style: widget.fontStyle ??
                      TextStyle(
                              color: textColor,
                              fontSize: 14.sp,
                              textBaseline: TextBaseline.ideographic,
                              height: model.chatConfig.textHeight)
                          .pfRegular)
              : ExtendedText(widget.message.textElem?.text ?? "",
                  softWrap: true,
                  style: widget.fontStyle ??
                      TextStyle(
                              fontSize: 14.sp,
                              height: model.chatConfig.textHeight)
                          .pfRegular,
                  specialTextSpanBuilder: DefaultSpecialTextSpanBuilder(
                    isUseQQPackage: model.chatConfig.stickerPanelConfig
                            ?.useQQStickerPackage ??
                        true,
                    isUseTencentCloudChatPackage: model
                            .chatConfig
                            .stickerPanelConfig
                            ?.useTencentCloudChatStickerPackage ??
                        true,
                    isUseTencentCloudChatPackageOldKeys: model
                            .chatConfig
                            .stickerPanelConfig
                            ?.useTencentCloudChatStickerPackageOldKeys ??
                        false,
                    customEmojiStickerList: widget.customEmojiStickerList,
                    showAtBackground: true,
                    checkHttpLink: true,
                  )),
          // If the link preview info is available, render the preview card.
          if (_renderPreviewWidget() != null &&
              model.chatConfig.urlPreviewType ==
                  UrlPreviewType.previewCardAndHyperlink)
            _renderPreviewWidget()!,
          if (widget.isShowMessageReaction ?? true)
            TIMUIKitMessageReactionShowPanel(message: widget.message)
        ],
      ),
    );
  }
}
