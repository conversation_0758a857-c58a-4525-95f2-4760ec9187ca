import 'dart:async';
import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/im_utils.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/model/im/system_notification_model.dart';
import 'package:npemployee/page/im/calling_message/calling_message_data_provider.dart';
import 'package:npemployee/page/im/calling_message/group_call_message_builder.dart';
import 'package:npemployee/page/im/calling_message/single_call_message_builder.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:tencent_cloud_chat_customer_service_plugin/tencent_cloud_chat_customer_service_plugin.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/link_preview/common/extensions.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/link_preview/common/utils.dart';
import 'package:url_launcher/url_launcher.dart';

class CustomMessageElem extends StatefulWidget {
  final TextStyle? messageFontStyle;
  final BorderRadius? messageBorderRadius;
  final Color? messageBackgroundColor;
  final EdgeInsetsGeometry? textPadding;
  final V2TimMessage message;
  final bool isShowJump;
  final VoidCallback? clearJump;
  final TIMUIKitChatController chatController;

  const CustomMessageElem({
    Key? key,
    required this.message,
    required this.isShowJump,
    required this.chatController,
    this.clearJump,
    this.messageFontStyle,
    this.messageBorderRadius,
    this.messageBackgroundColor,
    this.textPadding,
  }) : super(key: key);

  static Future<void> launchWebURL(BuildContext context, String url) async {
    try {
      await launchUrl(
        Uri.parse(url).withScheme,
        mode: LaunchMode.externalApplication,
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(TIM_t("无法打开URL"))), // Cannot launch the url
      );
    }
  }

  @override
  State<CustomMessageElem> createState() => _CustomMessageElemState();
}

class _CustomMessageElemState extends State<CustomMessageElem> {
  bool isShowJumpState = false;
  bool isShining = false;
  bool isShowBorder = false;

  _showJumpColor() {
    isShining = true;
    int shineAmount = 6;
    setState(() {
      isShowJumpState = true;
      isShowBorder = true;
    });
    Timer.periodic(const Duration(milliseconds: 300), (timer) {
      if (mounted) {
        setState(() {
          isShowJumpState = shineAmount.isOdd ? true : false;
          isShowBorder = shineAmount.isOdd ? true : false;
        });
      }
      if (shineAmount == 0 || !mounted) {
        isShining = false;
        timer.cancel();
      }
      shineAmount--;
    });
    if (widget.clearJump != null) {
      widget.clearJump!();
    }
  }

  Widget _callElemBuilder(BuildContext context, TUITheme theme) {
    final customElem = widget.message.customElem;

    final callingMessageDataProvider =
        CallingMessageDataProvider(widget.message);

    // final linkMessage = getLinkMessage(customElem);
    // final webLinkMessage = getWebLinkMessage(customElem);
    final systemMessage = getSystemNotificationModel(customElem);
    final isCustomerServiceMessage =
        TencentCloudChatCustomerServicePlugin.isCustomerServiceMessage(
            widget.message);

    if (customElem?.data == "group_create") {
      return renderMessageItem(
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(TIM_t(("群聊创建成功！")),
                style: ImUtils.messageHistoryListTextStyle()),
          ],
        ),
        theme,
        false,
      );
    } else if (MessageUtils.getCustomGroupCreatedOrDismissedString(
            widget.message)
        .isNotEmpty) {
      return Container(
          margin: const EdgeInsets.symmetric(vertical: 20),
          alignment: Alignment.center,
          child: Text.rich(TextSpan(children: [
            TextSpan(
              text: MessageUtils.getCustomGroupCreatedOrDismissedString(
                  widget.message),
              style: ImUtils.messageHistoryListTextStyle(),
            ),
          ], style: ImUtils.messageHistoryListTextStyle())));
    } else if (isCustomerServiceMessage) {
      return MessageCustomerService(
        message: widget.message,
        theme: theme,
        isShowJumpState: isShowJumpState,
        sendMessage: widget.chatController.sendMessage,
      );
    } /* else if (linkMessage != null) {
      final String option1 = linkMessage.link ?? "";
      return renderMessageItem(
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(linkMessage.text ?? ""),
            MarkdownBody(
              data: TIM_t_para("[查看详情 >>]({{option1}})", "[查看详情 >>]($option1)")(
                  option1: option1),
              styleSheet: MarkdownStyleSheet.fromTheme(ThemeData(
                      textTheme: const TextTheme(
                          // ignore: deprecated_member_use
                          bodyMedium: TextStyle(fontSize: 16.0))))
                  .copyWith(
                a: TextStyle(color: LinkUtils.hexToColor("015fff")),
              ),
              onTapLink: (
                String link,
                String? href,
                String title,
              ) {
                LinkUtils.launchURL(context, linkMessage.link ?? "");
              },
            )
          ],
        ),
        theme,
        false,
      );
    } else if (webLinkMessage != null) {
      return renderMessageItem(
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text.rich(TextSpan(
                style: const TextStyle(
                  fontSize: 16,
                ),
                children: [
                  TextSpan(text: webLinkMessage.title),
                  TextSpan(
                    text: webLinkMessage.hyperlinks_text?["key"],
                    style: const TextStyle(
                      color: Color.fromRGBO(0, 110, 253, 1),
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        CustomMessageElem.launchWebURL(
                          context,
                          webLinkMessage.hyperlinks_text?["value"],
                        );
                      },
                  )
                ])),
            if (webLinkMessage.description != null &&
                webLinkMessage.description!.isNotEmpty)
              Text(
                webLinkMessage.description!,
                style: const TextStyle(
                  fontSize: 16,
                ),
              )
          ],
        ),
        theme,
        false,
      );
    } */
    else if (!callingMessageDataProvider.excludeFromHistory &&
        callingMessageDataProvider.isCallingSignal) {
      if (callingMessageDataProvider.participantType ==
          CallParticipantType.group) {
        return GroupCallMessageItem(
            callingMessageDataProvider: callingMessageDataProvider);
      } else {
        return renderMessageItem(
          CallMessageItem(
            callingMessageDataProvider: callingMessageDataProvider,
            padding: const EdgeInsets.all(0),
            isFromSelf: widget.message.isSelf ?? true,
          ),
          theme,
          false,
        );
      }
    } else if (systemMessage != null) {
      return renderMessageItem(
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              NavigatorPushUtils.to(
                  context, systemMessage.jump, systemMessage.style, null);
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (systemMessage.type != null)
                  Text(systemMessage.type!,
                      style:
                          TextStyle(color: '#5F7093'.toColor(), fontSize: 14.sp)
                              .pfRegular),
                SizedBox(height: 10.h),
                if (systemMessage.title != null)
                  Text(systemMessage.title!,
                      style:
                          TextStyle(color: '#000000'.toColor(), fontSize: 14.sp)
                              .pfRegular),
                SizedBox(height: 24.h),
                if (systemMessage.img != null)
                  CachedNetworkImage(
                    imageUrl: systemMessage.img!.first['meta'],
                    width: double.parse('${systemMessage.img!.first['width']}'),
                    height:
                        double.parse('${systemMessage.img!.first['height']}'),
                  ),
                SizedBox(height: 24.h),
                if (systemMessage.date != null)
                  Text(
                      DateTimeUtils.formatCustomDate(
                          systemMessage.date!, 'MM月dd日 hh:ss'),
                      style:
                          TextStyle(color: '#AAAAAA'.toColor(), fontSize: 11.sp)
                              .pfRegular),
              ],
            ),
          ),
          theme,
          false);
    } else {
      return renderMessageItem(const Text("[自定义]"), theme, false);
    }
  }

  Widget renderMessageItem(Widget child, TUITheme theme, bool isVoteMessage) {
    final isFromSelf = widget.message.isSelf ?? true;
    final borderRadius = isFromSelf
        ? const BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(2),
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10))
        : const BorderRadius.only(
            topLeft: Radius.circular(2),
            topRight: Radius.circular(10),
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10));

    /* final defaultStyle = isFromSelf
        ? theme.lightPrimaryMaterialColor.shade50
        : theme.weakBackgroundColor; */
    final defaultStyle = isFromSelf ? '#3377FF'.toColor() : Colors.white;
    final backgroundColor =
        isShowJumpState ? const Color.fromRGBO(245, 166, 35, 1) : defaultStyle;

    return Container(
        padding: isVoteMessage
            ? null
            : (widget.textPadding ?? const EdgeInsets.all(10)),
        decoration: isVoteMessage
            ? BoxDecoration(
                border: Border.all(
                    width: 1, color: theme.weakDividerColor ?? Colors.grey))
            : BoxDecoration(
                color: widget.messageBackgroundColor ?? backgroundColor,
                borderRadius: widget.messageBorderRadius ?? borderRadius,
              ),
        constraints: BoxConstraints(
            maxWidth:
                isVoteMessage ? 298 : 240), // vote message width need more
        child: child);
  }

  @override
  Widget build(BuildContext context) {
    const theme = CommonColor.defaultTheme;

    if (widget.isShowJump) {
      if (!isShining) {
        Future.delayed(Duration.zero, () {
          _showJumpColor();
        });
      } else {
        if (widget.clearJump != null) {
          widget.clearJump!();
        }
      }
    }

    return _callElemBuilder(context, theme);
  }
}

SystemNotificationModel? getSystemNotificationModel(
    V2TimCustomElem? customElem) {
  try {
    if (customElem?.data != null) {
      final customMessage = jsonDecode(customElem!.data!);
      return SystemNotificationModel.fromJson(customMessage);
    }
    return null;
  } catch (err) {
    return null;
  }
}

class LinkMessage {
  String? link;
  String? text;
  String? businessID;

  LinkMessage.fromJSON(Map json) {
    link = json["link"];
    text = json["text"];
    businessID = json["businessID"];
  }
}

LinkMessage? getLinkMessage(V2TimCustomElem? customElem) {
  try {
    if (customElem?.data != null) {
      final customMessage = jsonDecode(customElem!.data!);
      return LinkMessage.fromJSON(customMessage);
    }
    return null;
  } catch (err) {
    return null;
  }
}

class WebLinkMessage {
  String? title;
  String? description;
  // ignore: non_constant_identifier_names
  Map<String, dynamic>? hyperlinks_text;

  WebLinkMessage.fromJSON(Map json) {
    title = json["title"];
    description = json["description"];
    hyperlinks_text = json["hyperlinks_text"];
  }
}

WebLinkMessage? getWebLinkMessage(V2TimCustomElem? customElem) {
  try {
    if (customElem?.extension != null) {
      final customMessage = jsonDecode(customElem!.extension!);
      return WebLinkMessage.fromJSON(customMessage);
    }
    return null;
  } catch (err) {
    return null;
  }
}
