import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http/http.dart' as http;
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:path_provider/path_provider.dart';

class AvatarGridGenerator {
  static Future<ui.Image> _generateAvatarGrid(List<String> avatarUrls) async {
    // 限制最多9个头像
    final urls = avatarUrls.take(9).toList();
    final int count = urls.length;

    // 确定网格布局
    final int rows = (count > 4) ? 3 : ((count > 1) ? 2 : 1);
    final int cols = (count > 4) ? 3 : ((count > 1) ? 2 : 1);

    // 设置画布大小（假设最终图片为300x300）
    final double totalSize = 300.0;
    final double padding = 2.0;

    // 计算实际需要的行数（可能小于最大行数）
    final int actualRows = (count / cols).ceil();

    // 计算单个头像的大小
    final double avatarSize = (totalSize - (padding * (cols + 1))) / cols;

    // 计算实际内容的总高度
    final double contentHeight =
        (avatarSize * actualRows) + (padding * (actualRows + 1));

    // 计算垂直居中的起始y坐标
    final double startY = (totalSize - contentHeight) / 2;

    // 创建画布
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // 绘制白色背景
    final paint = Paint()..color = Colors.white;
    canvas.drawRect(Rect.fromLTWH(0, 0, totalSize, totalSize), paint);

    // 下载并绘制每个头像
    int currentIndex = 0;
    for (int i = 0; i < actualRows; i++) {
      for (int j = 0; j < cols; j++) {
        if (currentIndex >= count) break;

        final x = padding + j * (avatarSize + padding);
        final y = startY + padding + i * (avatarSize + padding);

        try {
          final avatarImage = await _loadNetworkImage(urls[currentIndex]);
          canvas.drawImageRect(
            avatarImage,
            Rect.fromLTWH(0, 0, avatarImage.width.toDouble(),
                avatarImage.height.toDouble()),
            Rect.fromLTWH(x, y, avatarSize, avatarSize),
            paint,
          );
        } catch (e) {
          // 如果加载失败，绘制占位符
          canvas.drawRect(
            Rect.fromLTWH(x, y, avatarSize, avatarSize),
            Paint()..color = Colors.grey,
          );
        }

        currentIndex++;
      }
    }

    // 生成最终图片
    final picture = recorder.endRecording();
    return picture.toImage(totalSize.toInt(), totalSize.toInt());
  }

  static Future<ui.Image> _loadNetworkImage(String url) async {
    final response = await http.get(Uri.parse(url));
    final bytes = response.bodyBytes;
    final codec = await ui.instantiateImageCodec(bytes);
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  static Future<String?> postImage(List<String> avatarUrls) async {
    EasyLoading.show();
    final gridImage = await _generateAvatarGrid(avatarUrls);
    final byteData = await gridImage.toByteData(format: ui.ImageByteFormat.png);
    final bytes = byteData!.buffer.asUint8List();

    try {
      // 创建临时文件
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
          '${tempDir.path}/group_avatar_${DateTime.now().millisecondsSinceEpoch}.png');
      await tempFile.writeAsBytes(bytes);

      ResultData? res = await UserServiceProvider().uploadAvatar(tempFile.path);
      EasyLoading.dismiss();
      if (res?.code == 0) {
        // 删除临时文件
        tempFile.delete();
        return res?.data;
      } else {
        return null;
      }
    } catch (e) {
      EasyLoading.dismiss();
      return null;
    }
  }
}
