import 'package:flutter/material.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/manager/local_cache_manager.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/im/message_search_page.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_profile.dart';
import 'package:npemployee/page/im/widgets/i_tim_uikit_group_profile_widget.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/group_profile_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_self_info_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitAddFriend/tim_uikit_send_application.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitGroupProfile/group_profile_widget.dart';

class GroupProfile extends StatefulWidget {
  final String groupID;
  final Function(String updateGroupName)? updateGroupName;
  const GroupProfile({super.key, required this.groupID, this.updateGroupName});

  @override
  State<GroupProfile> createState() => _GroupProfileState();
}

class _GroupProfileState extends State<GroupProfile> {
  final sdkInstance = TIMUIKitCore.getSDKInstance();
  final coreInstance = TIMUIKitCore.getInstance();
  final List<ContactModel> _contacts = [];

  void _getContacts() {
    Map<String, dynamic>? res =
        LocalCacheManager.shared.getMapWithKey(LocalCachekeys.contactsKey);
    if (res != null) {
      ResultData? data = ResultData.fromJson(res);
      _formatContactsData(data, isCache: false);
    } else {
      UserServiceProvider().getContacts(
          cacheCallBack: (data) {
            _formatContactsData(data, isCache: true);
          },
          successCallBack: (data) {
            _formatContactsData(data);
          },
          errorCallBack: (err) {});
    }
  }

  void _formatContactsData(ResultData? data, {bool isCache = false}) {
    _contacts.clear();
    for (var element in data?.data ?? []) {
      if (element['user_count'] != 0) {
        _contacts.add(ContactModel.fromJson(element));
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getContacts();
  }

  @override
  Widget build(BuildContext context) {
    final TUISelfInfoViewModel _selfInfoViewModel =
        serviceLocator<TUISelfInfoViewModel>();
    final TUIFriendShipViewModel _friendShipViewModel =
        serviceLocator<TUIFriendShipViewModel>();
    return Scaffold(
        backgroundColor: 'F7F8FD'.toColor(),
        appBar: const CommonNav(title: '聊天信息'),
        body: SafeArea(
          child: ITimUikitGroupProfile(
            lifeCycle: GroupProfileLifeCycle(didLeaveGroup: () async {
              // Shows navigating back to the home page.
              // You can customize the reaction here.
              if (PlatformUtils().isWeb) {
                Navigator.of(context).pop();
                Navigator.of(context).pop();
              } else {
                Navigator.of(context).popUntil(ModalRoute.withName("/"));
              }
            }),
            groupID: widget.groupID,
            onClickUser: (V2TimGroupMemberFullInfo memberInfo, _) {
              if (memberInfo.userID != _selfInfoViewModel.loginInfo?.userID) {
                _friendShipViewModel
                    .isFriend(memberInfo.userID)
                    .then((isFriend) {
                  if (!isFriend) {
                    V2TimUserFullInfo friendInfo = V2TimUserFullInfo(
                        userID: memberInfo.userID,
                        nickName: memberInfo.nickName,
                        faceUrl: memberInfo.faceUrl);
                    Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                            builder: (context) => SendApplication(
                                friendInfo: friendInfo,
                                model: _selfInfoViewModel)));
                  } else {
                    // Navigator.push(
                    //     context,
                    //     MaterialPageRoute(
                    //       builder: (context) =>
                    //           UserProfile(userID: memberInfo.userID),
                    //     ));
                  }
                });
              }
            },
            profileWidgetBuilder: GroupProfileWidgetBuilder(searchMessage: () {
              return ITimUikitGroupProfileWidget.searchMessage(
                  (V2TimConversation? conversation) {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => MessageSearchPage(
                              contacts: _contacts,
                              conversation: conversation,
                            )));
                // Navigator.push(
                //     context,
                //     MaterialPageRoute(
                //         builder: (context) => Search(
                //               onTapConversation:
                //                   (V2TimConversation conversation,
                //                       V2TimMessage? targetMsg) {
                //                 Navigator.push(
                //                     context,
                //                     MaterialPageRoute(
                //                       builder: (context) => Chat(
                //                         selectedConversation: conversation,
                //                         initFindingMsg: targetMsg,
                //                       ),
                //                     ));
                //               },
                //               conversation: conversation,
                //             )));
              });
            }),
            updateGroupName: widget.updateGroupName,
          ),
        ));
  }
}
