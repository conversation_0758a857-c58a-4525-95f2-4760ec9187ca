import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/page/mine/team_manager/team_manager_nav.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';

class ContactsDetailPage extends StatefulWidget {
  final ContactModel contacts;
  const ContactsDetailPage({super.key, required this.contacts});

  @override
  State<ContactsDetailPage> createState() => _ContactsDetailPageState();
}

class _ContactsDetailPageState extends State<ContactsDetailPage> {
  List _contacts = [];

  void _initData() {
    _contacts.addAll(widget.contacts.departments);
    _contacts.addAll(widget.contacts.userList);
  }

  @override
  void initState() {
    super.initState();
    _initData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        Container(
          width: ScreenUtil().screenWidth,
          height: 211.h,
          decoration: const BoxDecoration(
              gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFFD6E8FA), Color(0xFFFFFFFF)])),
        ),
        Column(
          children: [
            TeamManagerNav(title: widget.contacts.department.name),
            _departmentDes(),
            Expanded(
                child: MediaQuery.removePadding(
                    removeTop: true,
                    context: context,
                    child: ListView.builder(
                        itemCount: _contacts.length,
                        itemBuilder: (_, index) {
                          final model = _contacts[index];
                          return model is ContactUser
                              ? _userItem(_, index)
                              : _departmentItem(_, index);
                        }),
                )),

            SizedBox(height: ScreenUtil().bottomBarHeight)
          ],
        ),
      ],
    ));
  }

  Widget _departmentDes() {
    return Container(
      margin: EdgeInsets.fromLTRB(16.w, 18.h, 16.w, 29.h),
      width: ScreenUtil().screenWidth - 32.w,
      padding: EdgeInsets.symmetric(horizontal: 21.w, vertical: 13.h),
      constraints: BoxConstraints(maxHeight: 180.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.r),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
                color: const Color(0xFF000000).withOpacity(0.05),
                offset: const Offset(0, 4),
                blurRadius: 13.5,
                spreadRadius: -2)
          ]),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '部门介绍',
              style: TextStyle(color: const Color(0xFF000000), fontSize: 16.sp)
                  .pfSemiBold,
            ),
            SizedBox(height: 12.h),
            Text(
              widget.contacts.department.about,
              style: TextStyle(color: const Color(0xFF777777), fontSize: 13.sp)
                  .pfRegular,
            ),
          ],
        ),
      ),
    );
  }

  Widget _userItem(BuildContext _, int index) {
    ContactUser user = _contacts[index];
    String avatarStr =
        user.avatar.isEmpty ? ValidatorUtils.testImageUrl : user.avatar;
    return GestureDetector(
      onTap: () {
        NavigatorUtils.push(context, MessageRouter.userProfilePage, arguments: {
          'contact': widget.contacts,
          'user': user,
        });
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
          margin: EdgeInsets.only(bottom: 25.h),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: SizedBox(
                        width: 37,
                        height: 37,
                        child: CachedNetworkImage(imageUrl: avatarStr),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Text(user.name,
                        style: TextStyle(
                                color: const Color(0xFF181818), fontSize: 16.sp)
                            .pfRegular),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Wrap(
                        spacing: 5.w,
                        runSpacing: 5.h,
                        children: [
                          ...user.roles.map(
                            (e) => Container(
                              decoration: BoxDecoration(
                                  color: const Color((0xFF3377FF)),
                                  borderRadius: BorderRadius.circular(3.r)),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 4.w, vertical: 1.h),
                              child: Text(e,
                                  style: TextStyle(
                                          color: Colors.white, fontSize: 10.sp)
                                      .pfRegular),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
              const Icon(Icons.keyboard_arrow_right, color: Color(0xFFBABABA)),
            ],
          )),
    );
  }

  Widget _departmentItem(BuildContext _, int index) {
    ContactModel model = _contacts[index];
    return GestureDetector(
      onTap: () {
        NavigatorUtils.push(context, MessageRouter.contactDetailPage,
            arguments: {'contacts': model});
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
          margin: EdgeInsets.only(bottom: 25.h),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SizedBox(
                      width: 37,
                      height: 37,
                      child:
                          Image.asset('assets/png/im/im_department_icon.png')),
                  SizedBox(width: 12.w),
                  Text(model.department.name,
                      style: TextStyle(
                              color: const Color(0xFF181818), fontSize: 16.sp)
                          .pfRegular),
                  SizedBox(width: 16.w),
                  Text(
                    '(${model.user_count})',
                    style: TextStyle(
                            color: const Color(0xFF999999), fontSize: 16.sp)
                        .pfRegular,
                  )
                ],
              ),
              const Icon(Icons.keyboard_arrow_right, color: Color(0xFFBABABA)),
            ],
          )),
    );
  }
}
