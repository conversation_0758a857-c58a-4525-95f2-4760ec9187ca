import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class HealthKitChannel {
  static HealthKitChannel? _instance;
  static const MethodChannel _channel = MethodChannel('health_kit_plugin');

  HealthKitChannel._internal() {
    debugPrint('HealthKitChannel init');
  }

  factory HealthKitChannel() {
    _instance ??= HealthKitChannel._internal();
    return _instance!;
  }

  Future<bool> isHealthKitAuthorized() async {
    try {
      final bool result = await _channel.invokeMethod('health_kit_authorized');
      return result;
    } on PlatformException catch (e) {
      debugPrint('获取健康权限失败: ${e.message}');
      return false;
    }
  }
}
