import 'package:flutter/services.dart';

class AuthService {
  static const platform = MethodChannel('com.npemployee/ali_auth');

  // Quit AliAuth and close the page
  static Future<void> quitAliAuth() async {
    try {
      await platform.invokeMethod('quitAliAuth');
      print("AliAuth page closed successfully");
    } on PlatformException catch (e) {
      print("Failed to close AliAuth page: '${e.message}'.");
    }
  }

  // Handle login logic with token, events, and error callbacks
  static Future<void> handleLogin({
    required Function(String token) onTokenReceived,
    required Function(int eventCode, String description) onEventReceived,
    required Function(String error) onError,
  }) async {
    try {
      print("Starting AliAuth...");
      await platform.invokeMethod('startAliAuth');

      _listenForAuthEvents(onTokenReceived, onEventReceived, onError);

    } on PlatformException catch (e) {
      print("Failed to handle login events: '${e.message}'.");
      onError(e.message ?? 'Unknown error');
    }
  }

  // Listen to method channel events for token and other events
  static void _listenForAuthEvents(
      Function(String token) onTokenReceived,
      Function(int eventCode, String description) onEventReceived,
      Function(String error) onError,
      ) {
    platform.setMethodCallHandler((MethodCall call) async {
      print("Received method call: ${call.method}");

      switch (call.method) {
        case 'onAuthTokenReceived':
          _handleAuthTokenReceived(call.arguments, onTokenReceived);
          break;
        case 'changePhoneEvent':
        case 'wechatLoginEvent':
          _handleEvent(call.arguments, onEventReceived);
          break;
        default:
          print("Unknown method call: ${call.method}");
      }
    });
  }

  // Process the token received from native code
  static void _handleAuthTokenReceived(
      dynamic arguments,
      Function(String token) onTokenReceived,
      ) {
    String token = arguments;

    if (token == "pending") {
      print("Auth page presented, awaiting token...");
      // Optionally show a loading indicator here
    } else {
      onTokenReceived(token); // Forward the token to the callback
    }
  }

  // Handle custom events like phone change or WeChat login
  static void _handleEvent(
      dynamic arguments,
      Function(int eventCode, String description) onEventReceived,
      ) {
    Map<String, dynamic> eventData = Map<String, dynamic>.from(arguments);
    int eventCode = eventData['eventCode'];
    String description = eventData['description'];

    print("Received event: $eventCode, Description: $description");
    onEventReceived(eventCode, description); // Trigger the event callback
  }
}
