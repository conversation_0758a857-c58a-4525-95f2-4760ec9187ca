
import 'package:flutter/services.dart';

class AndroidProxy{
  // 确保与 Android 端 FLUTTER_CALL_REGISTER_PLUGIN 的值一致
  static MethodChannel androidProxyChannel = const MethodChannel('FLUTTER_CALL_REGISTER_PLUGIN'); // 例如：'flutter_call_register_plugin'

  static Future<void> registerProxyPlugin() async {
    try {
      final result = await androidProxyChannel.invokeMethod('registerProxyPlugin');
      print('Registration result: $result');
    } on PlatformException catch (e) {
      print('Failed to register plugin: ${e.message}');
    }
  }
}