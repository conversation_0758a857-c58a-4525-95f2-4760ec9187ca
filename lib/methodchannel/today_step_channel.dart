import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

class TodayStepChannel {
  static TodayStepChannel? _instance;
  static const MethodChannel _channel = MethodChannel('today_step_channel');

  TodayStepChannel._internal() {
    debugPrint('TodayStepChannel init');
    _init();
  }

  factory TodayStepChannel() {
    _instance ??= TodayStepChannel._internal();
    return _instance!;
  }

  // 初始化方法
  Future<void> _init() async {
    try {
      final result = await _channel.invokeMethod('initTodayStep');
      debugPrint('初始化今日步数结果: $result');
    } on PlatformException catch (e) {
      debugPrint('初始化今日步数失败: ${e.message}');
    }
  }

  Future<dynamic> getTodayStep() async {
    try {
      final step = await _channel.invokeMethod('getTodayStep');
      return step;
    } on PlatformException catch (e) {
      debugPrint('获取今日步数失败: ${e.message}');
      return null;
    }
  }
}
