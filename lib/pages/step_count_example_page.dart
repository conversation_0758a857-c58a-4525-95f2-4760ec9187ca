import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:npemployee/common/page/webview_screen_page.dart';
import 'package:npemployee/widget/common_nav.dart';

/// 步数功能示例页面
class StepCountExamplePage extends StatefulWidget {
  const StepCountExamplePage({super.key});

  @override
  State<StepCountExamplePage> createState() => _StepCountExamplePageState();
}

class _StepCountExamplePageState extends State<StepCountExamplePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonNav(
        title: '步数功能示例',
      ),
      body: FutureBuilder<String>(
        future: _loadHtmlFromAssets(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            if (snapshot.hasError) {
              return Center(
                child: Text('加载HTML失败: ${snapshot.error}'),
              );
            }

            final htmlContent = snapshot.data!;
            final htmlUri = Uri.dataFromString(
              htmlContent,
              mimeType: 'text/html',
              encoding: Encoding.getByName('utf-8'),
            ).toString();

            return WebviewScreenPage(
              url: htmlUri,
              title: '步数功能示例',
              needNav: false,
              enableLongPress: false,
              keepAlive: false,
            );
          } else {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
    );
  }

  /// 从assets加载HTML文件
  Future<String> _loadHtmlFromAssets() async {
    return await rootBundle.loadString('assets/html/step_count_example.html');
  }
}
