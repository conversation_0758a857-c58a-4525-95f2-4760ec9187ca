class CourseWatchHistoryModel {
  final int chapter_id;
  final int course_id;
  final int duration;
  final int id;
  final int lesson_id;
  final int user_id;

  CourseWatchHistoryModel(
      {required this.chapter_id,
      required this.course_id,
      required this.duration,
      required this.id,
      required this.lesson_id,
      required this.user_id});

  factory CourseWatchHistoryModel.formJson(Map json) {
    return CourseWatchHistoryModel(
      chapter_id: json['chapter_id'],
      course_id: json['course_id'],
      duration: json['duration'],
      id: json['id'],
      lesson_id: json['lesson_id'],
      user_id: json['user_id'],
    );
  }
}
