class CourseWatchingModel {
  final List<RolesModel> roles;
  final int second;
  final WatchingUserModel user;

  CourseWatchingModel(this.roles, this.second, this.user);

  factory CourseWatchingModel.formJson(Map json) {
    List roleMaps = json['roles'];
    List<RolesModel> r = roleMaps.map((e) => RolesModel.formJson(e)).toList();
    WatchingUserModel userModel = WatchingUserModel.formJson(json['user']);
    return CourseWatchingModel(r, json['second'], userModel);
  }

  String get departmentName {
    String departName = '';
    for (var element in roles) {
      if (departName.isNotEmpty) {
        departName += ', '; // 添加逗号和空格分隔符
      }
      departName += element.department.name; // 拼接元素
    }
    return departName;
  }

  int get stutyTime {
    final int minutes = (second % 3600) ~/ 60;
    return minutes;
  }
}

class WatchingUserModel {
  final String avatar;
  final String guid;
  final int id;
  final String name;

  WatchingUserModel(this.avatar, this.guid, this.id, this.name);

  factory WatchingUserModel.formJson(Map json) {
    return WatchingUserModel(
        json['avatar'], json['guid'], json['id'], json['name']);
  }
}

class RolesModel {
  final WatchingDepartmentModel department;
  final List<WatchingDepartRolesModel> roles;

  RolesModel(this.department, this.roles);

  factory RolesModel.formJson(Map json) {
    WatchingDepartmentModel d =
        WatchingDepartmentModel.formJson(json['department']);
    List departmentRoles = json['roles'];
    List<WatchingDepartRolesModel> r = departmentRoles
        .map((e) => WatchingDepartRolesModel.formJson(e))
        .toList();
    return RolesModel(d, r);
  }
}

class WatchingDepartmentModel {
  final String about;
  final String area_code;
  final String created_at;
  final String department_type;
  final int id;
  final String location;
  final String name;

  WatchingDepartmentModel(
    this.about,
    this.area_code,
    this.created_at,
    this.department_type,
    this.id,
    this.location,
    this.name,
  );

  factory WatchingDepartmentModel.formJson(Map json) {
    return WatchingDepartmentModel(
      json['about'],
      json['area_code'],
      json['created_at'],
      json['department_type'],
      json['id'],
      json['location'],
      json['name'],
    );
  }
}

class WatchingDepartRolesModel {
  final int id;
  final RoleModel role;

  WatchingDepartRolesModel(this.id, this.role);

  factory WatchingDepartRolesModel.formJson(Map json) {
    RoleModel r = RoleModel.formJson(json['role']);
    return WatchingDepartRolesModel(json['id'], r);
  }
}

class RoleModel {
  final String about;
  final String created_at;
  final int id;
  final String name;
  final String role_type;

  RoleModel(this.about, this.created_at, this.id, this.name, this.role_type);

  factory RoleModel.formJson(Map json) {
    return RoleModel(json['about'], json['created_at'], json['id'],
        json['name'], json['role_type']);
  }
}
