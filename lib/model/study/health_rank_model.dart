import 'package:npemployee/model/study/user_model.dart';

class HealthRankModel {
  final UserModel user;
  final WalkModel walk;

  HealthRankModel(this.user, this.walk);

  factory HealthRankModel.formJson(Map json) {
    return HealthRankModel(
      UserModel.formJson(json['user']),
      WalkModel.formJson(json['walk']),
    );
  }
}

class WalkModel {
  final String created_at;
  final int id;
  final int user_id;
  final int walk_num;

  WalkModel(this.created_at, this.id, this.user_id, this.walk_num);

  factory WalkModel.formJson(Map json) {
    return WalkModel(
      json['created_at'],
      json['id'],
      json['user_id'],
      json['walk_num'],
    );
  }
}
