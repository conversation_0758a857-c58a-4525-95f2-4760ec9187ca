class XiaoxinMenusModel {
  final int id;
  final String icon;
  final List? jump;
  final String name;
  final int sort;
  final List? style;

  XiaoxinMenusModel({
    required this.id,
    required this.icon,
    required this.jump,
    required this.name,
    required this.sort,
    this.style,
  });
}

class XiaoxinSchoolModel {
  final int id;
  final String name;
  final int sort;
  final List<XiaoxinMenusModel> menus;

  XiaoxinSchoolModel({
    required this.id,
    required this.name,
    required this.sort,
    required this.menus,
  });
}
