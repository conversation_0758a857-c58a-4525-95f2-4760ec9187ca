class AnnualMeetingVideoTagModel {
  final int id;
  final String name;
  final int sort;
  final int parent_id;
  final String status;
  final List<AnnualMeetingVideoEdgesModel> edges;

  AnnualMeetingVideoTagModel(
      this.id, this.name, this.sort, this.parent_id, this.status, this.edges);

  factory AnnualMeetingVideoTagModel.fromJson(Map json) {
    List edgeMaps = json['tag']['edges']['children'];
    List<AnnualMeetingVideoEdgesModel> models =
        edgeMaps.map((e) => AnnualMeetingVideoEdgesModel.fromJson(e)).toList();

    return AnnualMeetingVideoTagModel(
      json['tag']['id'],
      json['tag']['name'],
      json['tag']['sort'],
      json['tag']['parent_id'],
      json['tag']['status'],
      models,
    );
  }
}

class AnnualMeetingVideoEdgesModel {
  final int id;
  final String name;
  final int? sort;

  AnnualMeetingVideoEdgesModel(this.id, this.name, this.sort);

  factory AnnualMeetingVideoEdgesModel.fromJson(Map json) {
    return AnnualMeetingVideoEdgesModel(
      json['id'],
      json['name'],
      json['sort'],
    );
  }
}
