class CourseHandoutModel {
  final int course_id;
  final String created_at;
  final int id;
  final int lesson_id;
  final String name;
  final int sort_order;
  final String url;

  CourseHandoutModel(
    this.course_id,
    this.created_at,
    this.id,
    this.lesson_id,
    this.name,
    this.sort_order,
    this.url,
  );

  factory CourseHandoutModel.fromJson(Map<String, dynamic> json) {
    return CourseHandoutModel(
      json['course_id'],
      json['created_at'],
      json['id'],
      json['lesson_id'],
      json['name'],
      json['sort_order'],
      json['url'],
    );
  }
}
