class ChapterModel {
  final int course_id;
  final int id;
  final String? phrase;
  final int sort_order;
  final String title;
  bool isSelected = false;
  bool isExpanded = false;
  final List<LessonModel> lesson;

  ChapterModel(
    this.course_id,
    this.id,
    this.phrase,
    this.sort_order,
    this.title,
    this.lesson,
  );

  factory ChapterModel.formJson(Map json) {
    List<LessonModel> lessonList = [];
    List lessonMaps = json['lesson'];
    for (var e in lessonMaps) {
      lessonList.add(LessonModel.formJson(e));
    }
    return ChapterModel(
      json['course_id'],
      json['id'],
      json['phrase'],
      json['sort_order'],
      json['title'],
      lessonList,
    );
  }

  Map toJson() {
    List<Map> lessons = lesson.map((e) => e.toJson()).toList();
    return {
      'course_id': course_id,
      'id': id,
      'phrase': phrase,
      'sort_order': sort_order,
      'title': title,
      'lesson': lessons,
    };
  }
}

class LessonModel {
  final int id;
  final int chapter_id;
  final int course_id;
  final String? phrase;
  final int sort_order;
  final String title;
  final int total_time;
  List<MediaModel> media;
  // final String url;
  bool isSelected = false;

  LessonModel(
    this.id,
    this.chapter_id,
    this.course_id,
    this.phrase,
    this.sort_order,
    this.title,
    this.total_time,
    this.media,
    // this.url,
  );

  factory LessonModel.formJson(Map json) {
    List<MediaModel> medias = [];
    List mediaMaps = json['media'] ?? [];
    for (var e in mediaMaps) {
      medias.add(MediaModel.formJson(e));
    }
    return LessonModel(
      json['id'],
      json['chapter_id'],
      json['course_id'],
      json['phrase'],
      json['sort_order'],
      json['title'],
      json['total_time'],
      medias,
      // json['url'],
    );
  }

  Map toJson() {
    List<Map> medias = media.map((e) => e.toJson()).toList();

    return {
      'id': id,
      'chapter_id': chapter_id,
      'course_id': course_id,
      'phrase': phrase,
      'sort_order': sort_order,
      'title': title,
      'total_time': total_time,
      'media': medias,
      // 'url': url,
    };
  }

  //超清
  double get vf4Size {
    return getSize('v.f4');
  }

  //高清
  double get vf5Size {
    return getSize('v.f5');
  }

  //流畅
  double get vf6Size {
    return getSize('v.f6');
  }

  //mp3
  double get getMp3Size {
    double size = 0;
    for (var media in media) {
      if (media.media_type == 'mp3') {
        media.size ??= 0;
        double result = media.size! / (1024 * 1024);
        size += result;
      }
    }
    return size;
  }

  double getSize(String sizeType) {
    double size = 0;
    for (var media in media) {
      if (media.media_url.contains(sizeType)) {
        media.size ??= 0;
        double result = media.size! / (1024 * 1024);
        size += result;
      }
    }
    return size;
  }
}

class MediaModel {
  final int id;
  final String media_type;
  String media_url;
  int? size;

  MediaModel({
    required this.id,
    required this.media_type,
    required this.media_url,
    this.size,
  });

  factory MediaModel.formJson(Map json) {
    return MediaModel(
        id: json['id'],
        media_type: json['media_type'],
        media_url: json['media_url'],
        size: json['size']);
  }

  Map toJson() {
    return {
      'id': id,
      'media_type': media_type,
      'media_url': media_url,
      'size': size
    };
  }

  double get getSize {
    size ??= 0;
    double result = size! / (1024 * 1024);
    return result;
  }
}
