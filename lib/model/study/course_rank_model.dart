class CourseRankModel {
  final int total_duration; //累计学习
  final int today_duration; //今日学习
  final int ranking; //排名
  final Map user; //人员信息

  CourseRankModel({
    required this.total_duration,
    required this.today_duration,
    required this.ranking,
    required this.user,
  });

  String get avatar {
    return user['avatar'];
  }

  String get userName {
    return user['name'];
  }

  String get guid {
    return user['guid'];
  }

  int get userId {
    return user['id'];
  }

  String get departName {
    Map edges = user['edges'];
    if (edges.isEmpty) {
      return '';
    }
    List roles = edges['department_roles'];
    List dNames = [];
    for (var e in roles) {
      if (!dNames.contains(e['edges']['department']['name'])) {
        dNames.add(e['edges']['department']['name']);
      }
    }
    String departmentName = dNames.join(',');
    return departmentName;
  }
}
