class DepartmentTreeModel {
  final DepartmentModel department;
  final List<DepartmentTreeModel>? children;
  final int level;
  final bool isSelected;

  DepartmentTreeModel({
    required this.department,
    required this.level,
    this.children = const [],
    this.isSelected = false,
  });
}

class DepartmentModel {
  final int? id;
  final int? parent_id;
  final String? name;
  final String? about;

  DepartmentModel(this.id, this.parent_id, this.name, this.about);

  factory DepartmentModel.formJson(Map json) {
    return DepartmentModel(
      json['id'],
      json['parent_id'],
      json['name'],
      json['about'],
    );
  }

  Map toJson() {
    return {
      'id': id,
      'parent_id': parent_id,
      'name': name,
      'about': about,
    };
  }
}
