class CourseListModel {
  final int id;
  final int course_type; //1-书籍 2-课程
  final String image;
  final String? introduction;
  final int? lesson_count;
  final String name;
  final String? phrase;
  final String status;
  final String tag_ids;
  final int? watch_count;
  final String? start_time;
  final String? end_time;
  final String? media_type; //视频-video 音频-audio 音视频-audioAndVideo

  CourseListModel({
    required this.id,
    required this.course_type,
    required this.image,
    required this.introduction,
    this.lesson_count,
    required this.name,
    required this.phrase,
    required this.status,
    required this.tag_ids,
    this.watch_count,
    this.start_time,
    this.end_time,
    this.media_type,
  });

  factory CourseListModel.formJson(Map json) {
    return CourseListModel(
        id: json['id'],
        course_type: json['course_type'],
        image: json['image'],
        introduction: json['introduction'],
        lesson_count: json['lesson_count'],
        name: json['name'],
        phrase: json['phrase'],
        status: json['status'],
        tag_ids: json['tag_ids'],
        watch_count: json['watch_count'],
        start_time: json['start_time'],
        end_time: json['end_time'],
        media_type: json['media_type']);
  }

  Map toJson() {
    return {
      'id': id,
      'course_type': course_type,
      'image': image,
      'introduction': introduction,
      'lesson_count': lesson_count,
      'name': name,
      'phrase': phrase,
      'status': status,
      'tag_ids': tag_ids,
      'watch_count': watch_count,
      'start_time': start_time,
      'end_time': end_time,
      'media_type': media_type,
    };
  }
}
