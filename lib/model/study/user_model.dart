class UserModel {
  final String name;
  final String avatar;
  final DepartmentModel department;

  UserModel(this.name, this.avatar, this.department);

  factory UserModel.formJson(Map json) {
    DepartmentModel model = DepartmentModel.formJson(json['department']);
    return UserModel(json['name'], json['avatar'], model);
  }
}

class DepartmentModel {
  final int id;
  final String created_at;
  final String updated_at;
  final String name;
  final String about;
  final String department_type;
  final int? last_update_user;
  final Map edges;

  DepartmentModel(this.id, this.created_at, this.updated_at, this.name,
      this.about, this.department_type, this.last_update_user, this.edges);

  factory DepartmentModel.formJson(Map json) {
    return DepartmentModel(
        json['id'],
        json['created_at'],
        json['updated_at'],
        json['name'],
        json['about'],
        json['department_type'],
        json['last_update_user'],
        json['edges']);
  }
}
