class DepartRankModel {
  final int department_id;
  final int? user_id; //查看部门榜某个部门员工时才有
  final String name;
  final int num;
  final int total_duration;
  final dynamic avg_duration;

  DepartRankModel(
      {required this.department_id,
      this.user_id,
      required this.name,
      required this.num,
      required this.total_duration,
      required this.avg_duration});
}

class TaskDepartRankModel {
  final String department;
  final int done;
  final int total;
  final int done_percent;

  TaskDepartRankModel(
      this.department, this.done, this.total, this.done_percent);

  factory TaskDepartRankModel.fromJson(Map json) {
    return TaskDepartRankModel(
      json['department'],
      json['done'],
      json['total'],
      json['done_percent'],
    );
  }
}
