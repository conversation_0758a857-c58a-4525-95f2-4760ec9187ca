class OperationalToolModel {
  final int id;
  final String name;
  final String intro;
  final int sort;
  final String image;
  final String qr_code;
  final String? meta;
  final int type;
  final String status;
  final OperationalToolEdges edges;

  OperationalToolModel(
    this.id,
    this.name,
    this.intro,
    this.sort,
    this.image,
    this.qr_code,
    this.meta,
    this.type,
    this.status,
    this.edges,
  );

  factory OperationalToolModel.fromJson(Map json) {
    return OperationalToolModel(
      json['id'],
      json['name'],
      json['intro'],
      json['sort'],
      json['image'],
      json['qr_code'],
      json['meta'],
      json['type'],
      json['status'],
      OperationalToolEdges.fromJson(json['edges']['tag']),
    );
  }
}

class OperationalToolEdges {
  final String name;
  final int sort;

  OperationalToolEdges(this.name, this.sort);

  factory OperationalToolEdges.fromJson(Map json) {
    return OperationalToolEdges(json['name'], json['sort']);
  }
}
