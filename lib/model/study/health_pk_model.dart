import 'package:npemployee/model/study/user_model.dart';

class HealthPkModel {
  final HealthModel health;
  final List<HealthLogModel> health_log;
  final List<PkerListModel> pker;
  final UserModel user;

  HealthPkModel(this.health, this.health_log, this.pker, this.user);

  factory HealthPkModel.formJson(Map json) {
    HealthModel healthModel = HealthModel.formJson(json['health']);
    List<HealthLogModel> healthLogs = [];
    for (var element in json['health_log']) {
      HealthLogModel healthLogModel = HealthLogModel.formJson(element);
      healthLogs.add(healthLogModel);
    }
    List<PkerListModel> pkers = [];
    for (var element in json['pker']) {
      PkerListModel pkerListModel = PkerListModel.formJson(element);
      pkers.add(pkerListModel);
    }
    UserModel userModel = UserModel.formJson(json['user']);
    return HealthPkModel(healthModel, healthLogs, pkers, userModel);
  }
}

class HealthModel {
  final String created_at;
  final int id;
  final String present;
  final String reduce;
  final String target;
  final int user_id;
  final int year;

  HealthModel(this.created_at, this.id, this.present, this.reduce, this.target,
      this.user_id, this.year);

  factory HealthModel.formJson(Map json) {
    return HealthModel(
      json['created_at'],
      json['id'],
      handleNumber(json['present']),
      handleNumber(json['reduce']),
      handleNumber(json['target']),
      json['user_id'],
      json['year'],
    );
  }

  static String handleNumber(dynamic value) {
    if (value is int) {
      return value.toDouble().toStringAsFixed(0);
    } else {
      return value.toString();
    }
  }
}

class HealthLogModel {
  final String created_at;
  final String date_month;
  final int health_id;
  final int id;
  final String img;
  final String weight;

  HealthLogModel(this.created_at, this.date_month, this.health_id, this.id,
      this.img, this.weight);

  factory HealthLogModel.formJson(Map json) {
    return HealthLogModel(
        json['created_at'],
        json['date_month'],
        json['health_id'],
        json['id'],
        json['img'],
        handleNumber(json['weight']));
  }

  static String handleNumber(dynamic value) {
    if (value is int) {
      return value.toDouble().toStringAsFixed(0);
    } else {
      return value.toString();
    }
  }
}

class PkerListModel {
  final PkerModel pker;
  final UserModel user;

  PkerListModel(this.pker, this.user);

  factory PkerListModel.formJson(Map json) {
    PkerModel pModel = PkerModel.formJson(json['pker']);
    UserModel uModel = UserModel.formJson(json['user']);
    return PkerListModel(pModel, uModel);
  }
}

class PkerModel {
  final String created_at;
  final int health_id;
  final int id;
  final String penalize;
  final int user_id;

  PkerModel(
      this.created_at, this.health_id, this.id, this.penalize, this.user_id);

  factory PkerModel.formJson(Map json) {
    return PkerModel(json['created_at'], json['health_id'], json['id'],
        json['penalize'], json['user_id']);
  }
}
