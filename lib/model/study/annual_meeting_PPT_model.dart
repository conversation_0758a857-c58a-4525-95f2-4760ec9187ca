class AnnualMeetingPptTagModel {
  final String created_at;
  final int id;
  final int level;
  final String name;
  final int parent_id;
  final int sort;
  final String status;

  AnnualMeetingPptTagModel(this.created_at, this.id, this.level, this.name,
      this.parent_id, this.sort, this.status);

  factory AnnualMeetingPptTagModel.formJson(Map json) {
    return AnnualMeetingPptTagModel(
      json['created_at'],
      json['id'],
      json['level'],
      json['name'],
      json['parent_id'],
      json['sort'],
      json['status'],
    );
  }
}

class AnnualMeetingPptModel {
  final String created_at;
  final int id;
  final String name;
  final int sort;
  final String status;
  final int tag_id;
  final String url;

  AnnualMeetingPptModel(this.created_at, this.id, this.name, this.sort,
      this.status, this.tag_id, this.url);

  factory AnnualMeetingPptModel.formJson(Map json) {
    return AnnualMeetingPptModel(
      json['created_at'],
      json['id'],
      json['name'],
      json['sort'],
      json['status'],
      json['tag_id'],
      json['url'],
    );
  }
}
