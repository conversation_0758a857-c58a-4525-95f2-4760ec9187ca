import 'package:image_picker/image_picker.dart';

class SubMissionListModel {
  final SubMissionModel mission_sub;
  final SubMissionUserModel user_sub;

  SubMissionListModel(this.mission_sub, this.user_sub);

  factory SubMissionListModel.fromJson(Map json) {
    return SubMissionListModel(
      SubMissionModel.fromJson(json['mission_sub']),
      SubMissionUserModel.fromJson(json['user_sub']),
    );
  }

  int get learnTime {
    if (user_sub.clear_learning_time != null &&
        user_sub.clear_learning_time != 0) {
      return user_sub.learning_time - user_sub.clear_learning_time!;
    }
    return user_sub.learning_time;
  }

  String get learnTimeMinutes {
    double t = learnTime / 60;
    double totalSeconds = mission_sub.required_seconds.toDouble();
    if (learnTime > totalSeconds) {
      t = totalSeconds / 60;
    }
    String tStr;
    if (t == t.truncateToDouble()) {
      tStr = t.truncate().toString();
    } else {
      tStr = t.toStringAsFixed(1);
    }
    return tStr;
  }

  String get totalMinutes {
    double minutes = mission_sub.required_seconds / 60;
    if (minutes == minutes.truncateToDouble()) {
      return minutes.truncate().toString();
    } else {
      return minutes.toStringAsFixed(1);
    }
  }

  int get watchedPercent {
    double percent =
        (double.parse(learnTimeMinutes) / double.parse(totalMinutes) * 100);
    return percent.toInt();
  }

  bool get finished => watchedPercent == 100;
}

class SubMissionModel {
  final int? chapter_id;
  final int? course_id;
  final String created_at;
  final String desc;
  final int id;
  final int? lesson_id;
  final int mission_id;
  final int required_seconds;

  SubMissionModel(this.chapter_id, this.course_id, this.created_at, this.desc,
      this.id, this.lesson_id, this.mission_id, this.required_seconds);

  factory SubMissionModel.fromJson(Map json) {
    return SubMissionModel(
      json['chapter_id'],
      json['course_id'],
      json['created_at'],
      json['desc'],
      json['id'],
      json['lesson_id'],
      json['mission_id'],
      json['required_seconds'],
    );
  }
}

class SubMissionUserModel {
  final int id;
  final String?
      last_sync_at; //此值为空时代表从未同步课程系统，此值会被定时任务以及懒加载调用，如果发现为空为异常，请重新调用此接口
  final int
      learning_time; //如果clear_learning_time不为null或者0，代表用户有考试失败的经历，渲染时使用(clear_learning_time - learning_time) 作为最终值渲染至客户端
  final int mission_sub_id;
  final int mission_user_id;
  final int user_id;
  final int? clear_learning_time;

  SubMissionUserModel(
      this.id,
      this.last_sync_at,
      this.learning_time,
      this.mission_sub_id,
      this.mission_user_id,
      this.user_id,
      this.clear_learning_time);

  factory SubMissionUserModel.fromJson(Map json) {
    return SubMissionUserModel(
      json['id'],
      json['last_sync_at'],
      json['learning_time'],
      json['mission_sub_id'],
      json['mission_user_id'],
      json['user_id'],
      json['clear_learning_time'],
    );
  }
}
