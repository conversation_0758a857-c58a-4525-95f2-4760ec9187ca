import 'package:npemployee/Utils/date_time_utils.dart';

class MissionListModel {
  final MissionModel mission;
  final MissionUserModel missionUser;

  MissionListModel(this.mission, this.missionUser);

  factory MissionListModel.fromJson(Map json) {
    return MissionListModel(MissionModel.fromJson(json['mission']),
        MissionUserModel.fromJson(json['missionuser']));
  }

  bool get hasExam {
    return mission.paper_id != null && mission.paper_id != 0;
  }

  String get deadLineTime {
    return DateTimeUtils.formatCustomDate(
        missionUser.mission_dead_line, 'yyyy-MM-dd HH:mm:ss');
  }

  bool get finished {
    return missionUser.mission_status == 'pass';
  }
}

class MissionModel {
  final String name;
  final int? paper_id;

  MissionModel(this.name, this.paper_id); //此值为0或null即为未绑定测验

  factory MissionModel.fromJson(Map json) {
    return MissionModel(
      json['name'],
      json['paper_id'],
    );
  }
}

class MissionUserModel {
  final String? exam_at; //为null或空代表未开始考试，否则是最后的考试时间
  final int? exam_id; //为null或空代表未开始考试，否则是考试id
  final int exam_times;
  final int id;
  final String mission_dead_line; //任务截止时间
  final int mission_id;
  final String mission_status; //missioning-进行中 pass-已完成 fail失败
  final bool read;
  final int user_id;

  MissionUserModel(
      this.exam_at,
      this.exam_id,
      this.exam_times,
      this.id,
      this.mission_dead_line,
      this.mission_id,
      this.mission_status,
      this.read,
      this.user_id);

  factory MissionUserModel.fromJson(Map json) {
    return MissionUserModel(
      json['exam_at'],
      json['exam_id'],
      json['exam_times'],
      json['id'],
      json['mission_dead_line'],
      json['mission_id'],
      json['mission_status'],
      json['read'],
      json['user_id'],
    );
  }
}
