import 'package:npemployee/model/mine/team_manager_department_model.dart';

class TeamManagerPersonModel {
  final List<RolesModel> roles;
  final UserModel user;

  TeamManagerPersonModel(this.roles, this.user);

  factory TeamManagerPersonModel.formJson(Map json) {
    List<RolesModel> r = [];
    List jsonRoles = json['roles'];
    for (var element in jsonRoles) {
      r.add(RolesModel.formJson(element));
    }

    return TeamManagerPersonModel(r, UserModel.formJson(json['user']));
  }

  String get rolesStr {
    List<String> roleList = [];
    for (var e in roles) {
      for (var element in e.roles) {
        roleList.add(element.role.name);
      }
    }
    return roleList.join(',');
  }

  List get roleIds {
    List<int> roleIds = [];
    for (var e in roles) {
      for (var element in e.roles) {
        roleIds.add(element.id);
      }
    }
    return roleIds;
  }
}

class RolesModel {
  final DepartmentModel department;
  final List<RolesRolesModel> roles;

  RolesModel(this.department, this.roles);

  factory RolesModel.formJson(Map json) {
    List<RolesRolesModel> r = [];
    List jsonRoles = json['roles'];
    for (var element in jsonRoles) {
      r.add(RolesRolesModel.formJson(element));
    }
    return RolesModel(DepartmentModel.fromJson(json['department']), r);
  }

  Map toJson() {
    return {
      'department': department.toJson(),
      'roles': roles.map((e) => e.toJson()).toList(),
    };
  }
}

class RolesRolesModel {
  final int id;
  final RoleModel role;

  RolesRolesModel(this.id, this.role);

  factory RolesRolesModel.formJson(Map json) {
    return RolesRolesModel(json['id'], RoleModel.formJson(json['role']));
  }

  Map toJson() {
    return {
      'id': id,
      'role': role.toJson(),
    };
  }
}

class RoleModel {
  final String about;
  final String created_at;
  final int id;
  final String name;
  final String role_type;

  RoleModel(this.about, this.created_at, this.id, this.name, this.role_type);

  factory RoleModel.formJson(Map json) {
    return RoleModel(json['about'], json['created_at'], json['id'],
        json['name'], json['role_type']);
  }

  Map toJson() {
    return {
      'about': about,
      'created_at': created_at,
      'id': id,
      'name': name,
      'role_type': role_type,
    };
  }
}

class UserModel {
  final String name;
  final String avatar;
  final String guid;
  final int id;
  final String id_no;
  final int distribution_department_id;
  final String mobile;
  final int promotion_department_id;
  final String status;

  UserModel(
    this.name,
    this.avatar,
    this.guid,
    this.id,
    this.id_no,
    this.distribution_department_id,
    this.mobile,
    this.promotion_department_id,
    this.status,
  );

  factory UserModel.formJson(Map json) {
    return UserModel(
      json['name'],
      json['avatar'],
      json['guid'],
      json['id'],
      json['id_no'],
      json['distribution_department_id'],
      json['mobile'],
      json['promotion_department_id'],
      json['status'],
    );
  }
}
