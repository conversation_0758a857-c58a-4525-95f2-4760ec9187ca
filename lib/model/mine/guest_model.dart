class GuestModel {
  final List<MenuModel>? menus;
  final UserModel? user;

  GuestModel(this.menus, this.user);

  factory GuestModel.fromJson(Map json) {
    List? menuMaps = json['menus'];

    return GuestModel(
      menuMaps?.map((e) => MenuModel.fromJson(e)).toList(),
      UserModel.fromJson(json['user']),
    );
  }
  Map toJson() {
    return {
      'menus': menus?.map((e) => e.toJson()).toList(),
      'user': user?.toJson()
    };
  }
}

class MenuModel {
  final int? id;
  final String? created_at;
  final String? updated_at;
  final String? name;
  final String? key;
  final String? icon;
  final int? sort;
  final List? jump; // {key, value}
  final List? style;

  MenuModel(this.id, this.created_at, this.updated_at, this.name, this.key,
      this.icon, this.sort, this.jump, this.style); // {key, value}

  factory MenuModel.from<PERSON>son(Map json) {
    return MenuModel(
      json['id'],
      json['created_at'],
      json['updated_at'],
      json['name'],
      json['key'],
      json['icon'],
      json['sort'],
      json['jump'],
      json['style'],
    );
  }

  Map toJson() {
    return {
      'id': id,
      'created_at': created_at,
      'updated_at': updated_at,
      'name': name,
      'key': key,
      'icon': icon,
      'sort': sort,
      'jump': jump,
      'style': style,
    };
  }
}

class UserModel {
  final String? avatar;
  final String? created_at;
  final int? guest_identity_id;
  final String? guid;
  final int? id;
  final String? mobile;
  final String? name;

  UserModel(this.avatar, this.created_at, this.guest_identity_id, this.guid,
      this.id, this.mobile, this.name);

  factory UserModel.fromJson(Map json) {
    return UserModel(
      json['avatar'],
      json['created_at'],
      json['guest_identity_id'],
      json['guid'],
      json['id'],
      json['mobile'],
      json['name'],
    );
  }

  Map toJson() {
    return {
      'avatar': avatar,
      'created_at': created_at,
      'guest_identity_id': guest_identity_id,
      'guid': guid,
      'id': id,
      'mobile': mobile,
      'name': name,
    };
  }
}
