import 'package:npemployee/model/study/course_list_model.dart';

class CourseStudyingModel {
  final int chapter_id;
  final int course_id;
  final StudyingInfoModel course_info;
  final String created_at;
  final int duration;
  final int id;
  final int lesson_id;
  final String updated_at;
  final int user_id;

  CourseStudyingModel(
      this.chapter_id,
      this.course_id,
      this.course_info,
      this.created_at,
      this.duration,
      this.id,
      this.lesson_id,
      this.updated_at,
      this.user_id);

  factory CourseStudyingModel.fromJson(Map json) {
    return CourseStudyingModel(
      json['chapter_id'],
      json['course_id'],
      StudyingInfoModel.fromJson(json['course_info']),
      json['created_at'],
      json['duration'],
      json['id'],
      json['lesson_id'],
      json['updated_at'],
      json['user_id'],
    );
  }
}

class StudyingInfoModel {
  final int lesson_count;
  final int total_duration;
  final int watch_duration;
  final CourseListModel course;

  StudyingInfoModel(
      this.lesson_count, this.total_duration, this.watch_duration, this.course);

  factory StudyingInfoModel.fromJson(Map json) {
    return StudyingInfoModel(
      json['lesson_count'],
      json['total_duration'],
      json['watch_duration'],
      CourseListModel.formJson(json['course']),
    );
  }
}
