class TeamManagerDepartmentModel {
  final List<DepartmentModel> children;
  final DepartmentModel department;

  TeamManagerDepartmentModel(this.children, this.department);

  factory TeamManagerDepartmentModel.fromJson(Map json) {
    List<DepartmentModel> childrens = [];
    List jsonChildren = json['children'];
    for (var element in jsonChildren) {
      childrens.add(DepartmentModel.fromJson(element['department']));
    }
    return TeamManagerDepartmentModel(
        childrens,
        DepartmentModel.fromJson(
          json['department'],
        ));
  }
}

class DepartmentModel {
  final String about;
  final String area_code;
  final String created_at;
  final String department_type;
  final int id;
  final String location;
  final String name;
  final int? parent_id;

  DepartmentModel(
    this.about,
    this.area_code,
    this.created_at,
    this.department_type,
    this.id,
    this.location,
    this.name,
    this.parent_id,
  );

  factory DepartmentModel.fromJson(Map<String, dynamic> json) {
    return DepartmentModel(
      json['about'],
      json['area_code'],
      json['created_at'],
      json['department_type'],
      json['id'],
      json['location'],
      json['name'],
      json['parent_id'],
    );
  }

  Map toJson() {
    return {
      'about': about,
      'area_code': area_code,
      'created_at': created_at,
      'department_type': department_type,
      'id': id,
      'location': location,
      'name': name,
      'parent_id': parent_id,
    };
  }
}
