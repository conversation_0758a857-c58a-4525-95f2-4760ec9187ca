class OldPersonModel {
  final int? id;
  final String? guid;
  final String? mobile;
  final String? reg_mobile;
  final String? real_name;
  final String? nick_name;
  final String? avatar;
  final String? id_card;
  final int? status;
  final String? open_id;
  final String? union_id;
  final String? ip;
  final String? created_at;
  final String? updated_at;
  final String? did;
  final String? be_did;
  final String? code;
  final int? gender;
  final String? all_union_id;
  final String? all_open_id;
  final int? is_distributor;
  final int? friend_circle;
  final List<DepartModel>? depart;

  OldPersonModel(
    this.id,
    this.guid,
    this.mobile,
    this.reg_mobile,
    this.real_name,
    this.nick_name,
    this.avatar,
    this.id_card,
    this.status,
    this.open_id,
    this.union_id,
    this.ip,
    this.created_at,
    this.updated_at,
    this.did,
    this.be_did,
    this.code,
    this.gender,
    this.all_union_id,
    this.all_open_id,
    this.is_distributor,
    this.friend_circle,
    this.depart,
  );

  factory OldPersonModel.fromJson(Map json) {
    List? departMaps = json['depart'];

    return OldPersonModel(
      json['id'],
      json['guid'],
      json['mobile'],
      json['reg_mobile'],
      json['real_name'],
      json['nick_name'],
      json['avatar'],
      json['id_card'],
      json['status'],
      json['open_id'],
      json['union_id'],
      json['ip'],
      json['created_at'],
      json['updated_at'],
      json['did'],
      json['be_did'],
      json['code'],
      json['gender'],
      json['all_union_id'],
      json['all_open_id'],
      json['is_distributor'],
      json['friend_circle'],
      departMaps?.map((e) => DepartModel.fromJson(e)).toList(),
    );
  }

  Map toJson() {
    return {
      'id': id,
      'guid': guid,
      'mobile': mobile,
      'reg_mobile': reg_mobile,
      'real_name': real_name,
      'nick_name': nick_name,
      'avatar': avatar,
      'id_card': id_card,
      'status': status,
      'open_id': open_id,
      'union_id': union_id,
      'ip': ip,
      'created_at': created_at,
      'updated_at': updated_at,
      'did': did,
      'be_did': be_did,
      'code': code,
      'gender': gender,
      'all_union_id': all_union_id,
      'all_open_id': all_open_id,
      'is_distributor': is_distributor,
      'friend_circle': friend_circle,
      'depart': depart?.map((e) => e.toJson()).toList(),
    };
  }
}

class DepartModel {
  final int? id;
  final String? school_name;
  final int? level_type;
  final String? province;
  final String? created_at;
  final String? depart_name;
  final String? alias;
  final String? city;
  final String? area;
  final int? school_id;
  final int? pro_code;
  final int? city_code;
  final int? area_code;

  DepartModel(
      this.id,
      this.school_name,
      this.level_type,
      this.province,
      this.created_at,
      this.depart_name,
      this.alias,
      this.city,
      this.area,
      this.school_id,
      this.pro_code,
      this.city_code,
      this.area_code);

  factory DepartModel.fromJson(Map json) {
    return DepartModel(
      json['id'],
      json['school_name'],
      json['level_type'],
      json['province'],
      json['created_at'],
      json['depart_name'],
      json['alias'],
      json['city'],
      json['area'],
      json['school_id'],
      json['pro_code'],
      json['city_code'],
      json['area_code'],
    );
  }

  Map toJson() {
    return {
      'id': id,
      'school_name': school_name,
      'level_type': level_type,
      'province': province,
      'created_at': created_at,
      'depart_name': depart_name,
      'alias': alias,
      'city': city,
      'area': area,
      'school_id': school_id,
      'pro_code': pro_code,
      'city_code': city_code,
      'area_code': area_code,
    };
  }
}
