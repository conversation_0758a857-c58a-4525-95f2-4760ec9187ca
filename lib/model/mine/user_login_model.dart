import 'package:json_annotation/json_annotation.dart';

part 'user_login_model.g.dart';

@JsonSerializable()
class UserLoginModel {
  @J<PERSON><PERSON><PERSON>(name: 'guid')
  final String? guid;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'token')
  final String? token;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'userType')
  final int? userType;

  @<PERSON>son<PERSON>ey(name: 'adCode')
  final int? adCode;

  @JsonKey(name: 'province')
  final String? province;

  @<PERSON>son<PERSON>ey(name: 'city')
  final String? city;

  @Json<PERSON>ey(name: 'area')
  final String? area;

  @Json<PERSON>ey(name: 'avatar')
  final String? avatar;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'nickName')
  final String? nickName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'mobile')
  final String? mobile;

  @JsonKey(name: 'openId')
  final String? openId;

  UserLoginModel({
    this.guid,
    this.token,
    this.userType,
    this.adCode,
    this.province,
    this.city,
    this.area,
    this.avatar,
    this.nickName,
    this.mobile,
    this.openId,
  });

  factory UserLoginModel.fromJson(Map<String, dynamic> json) =>
      _$UserLoginModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserLoginModelToJson(this);
}
