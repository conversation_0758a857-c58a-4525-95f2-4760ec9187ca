class UserSubModel {
  final String? sub_mobile;
  final UserModel? user;

  UserSubModel(this.sub_mobile, this.user);

  factory UserSubModel.fromJson(Map json) {
    return UserSubModel(
        json['sub_mobile'],
        UserModel.fromJson(
          json['user'],
        ));
  }

  Map toJson() {
    return {
      'sub_mobile': sub_mobile,
      'user': user?.toJson(),
    };
  }
}

class UserModel {
  final String? avatar;
  final String? guid;
  final int? id;
  final String? mobile;
  final String? name;

  UserModel(this.avatar, this.guid, this.id, this.mobile, this.name);

  factory UserModel.fromJson(Map json) {
    return UserModel(
      json['avatar'],
      json['guid'],
      json['id'],
      json['mobile'],
      json['name'],
    );
  }

  Map toJson() {
    return {
      'avatar': avatar,
      'guid': guid,
      'id': id,
      'mobile': mobile,
      'name': name,
    };
  }
}
