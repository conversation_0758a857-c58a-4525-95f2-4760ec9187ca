class PaperCategoryModel {
  final int? category_id;
  final String category_name;

  PaperCategoryModel({
    required this.category_id,
    required this.category_name,
  });

  factory PaperCategoryModel.fromJson(Map json) {
    return PaperCategoryModel(
      category_id: json['category_id'],
      category_name: json['category_name'],
    );
  }
}

class PaperModel {
  final int paper_id;
  final int question_total;
  final String paper_name;
  final String start_time;
  final String end_time;
  final String paper_status;
  final int? people_total;
  final int? limit_time;
  final int question_remain_total;

  PaperModel({
    required this.paper_id,
    required this.question_total,
    required this.paper_name,
    required this.start_time,
    required this.end_time,
    required this.paper_status, // 试卷状态，NOT-暂未开始，READY-开始答题，CONTINUE-继续答题，OVER-答题结束
    required this.people_total,
    required this.limit_time,
    required this.question_remain_total,
  });

  factory PaperModel.fromJson(Map json) {
    return PaperModel(
      paper_id: json['paper_id'],
      question_total: json['question_total'],
      paper_name: json['paper_name'],
      start_time: json['start_time'],
      end_time: json['end_time'],
      paper_status: json['paper_status'],
      people_total: json['people_total'],
      limit_time: json['limit_time'],
      question_remain_total: json['question_remain_total'],
    );
  }
}
