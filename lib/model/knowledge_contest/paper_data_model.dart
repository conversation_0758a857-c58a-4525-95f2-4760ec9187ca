import 'package:npemployee/model/knowledge_contest/paper_content_model.dart';

class PaperDataModel {
  final String paper_name;
  final String my_score;
  final String score_total;
  final String score_pass;
  final int right_total;
  final int wrong_total;
  final int unanswered_total;
  final int question_total;
  final List<PaperContentItemModel> answer_sheet;
  final int rank_with_all;
  final int answer_total_with_all;
  final List<PaperRankModel> department_rank_list;
  final List<PaperRankModel> all_rank_list;
  final List<PaperRankMissionModel> all_rank_list_for_mission;

  PaperDataModel(
    this.paper_name,
    this.my_score,
    this.score_total,
    this.score_pass,
    this.right_total,
    this.wrong_total,
    this.unanswered_total,
    this.question_total,
    this.answer_sheet,
    this.rank_with_all,
    this.answer_total_with_all,
    this.department_rank_list,
    this.all_rank_list,
    this.all_rank_list_for_mission,
  );

  factory PaperDataModel.fromJson(Map<String, dynamic> json) {
    List<PaperContentItemModel> contents = [];
    for (var element in json['answer_sheet']) {
      if (element['item_tag'] != 'MODULE') {
        contents.add(PaperContentItemModel.fromJson(element));
      }
    }
    List<PaperRankModel> department_rank_list = [];
    List<PaperRankModel> all_rank_list = [];
    List<PaperRankMissionModel> missionRanks = [];
    for (var element in json['department_rank_list']) {
      department_rank_list.add(PaperRankModel.fromJson(element));
    }
    for (var element in json['all_rank_list']) {
      all_rank_list.add(PaperRankModel.fromJson(element));
    }
    for (var element in json['all_rank_list_for_mission']) {
      missionRanks.add(PaperRankMissionModel.fromJson(element));
    }
    return PaperDataModel(
      json['paper_name'],
      json['my_score'],
      json['score_total'],
      json['score_pass'],
      json['right_total'],
      json['wrong_total'],
      json['unanswered_total'],
      json['question_total'],
      contents,
      json['rank_with_all'],
      json['answer_total_with_all'],
      department_rank_list,
      all_rank_list,
      missionRanks,
    );
  }
}

class PaperRankModel {
  final bool is_me;
  final int rank_no;
  final String name;
  final String department_name;
  final int use_time;
  final String score;

  PaperRankModel(this.is_me, this.rank_no, this.name, this.department_name,
      this.use_time, this.score);

  factory PaperRankModel.fromJson(Map<String, dynamic> json) {
    return PaperRankModel(json['is_me'], json['rank_no'], json['name'],
        json['department_name'], json['use_time'], json['score']);
  }
}

class PaperRankMissionModel {
  final String department_name;
  final String pass_rate;
  final int answer_number;

  PaperRankMissionModel(
      this.department_name, this.pass_rate, this.answer_number);

  factory PaperRankMissionModel.fromJson(Map json) {
    return PaperRankMissionModel(
      json['department_name'],
      json['pass_rate'],
      json['answer_number'],
    );
  }
}

class PaperAllRank {
  final int? rank_no;
  final String? name;
  final int? user_id;
  final String? department_name;
  final String? score;
  final List? departments;

  PaperAllRank(this.rank_no, this.name, this.user_id, this.department_name,
      this.score, this.departments);

  String get departNameSort {
    departments?.sort((a, b) => a['id'].compareTo(b['id']));
    return departments?.map((e) => e['name']).join(',') ?? '';
  }

  factory PaperAllRank.fromJson(Map json) {
    return PaperAllRank(
      json['rank_no'],
      json['name'],
      json['user_id'],
      json['department_name'],
      _formatScore(json['score']),
      json['departments'],
    );
  }

  static String _formatScore(String score) {
    double scoreValue = double.parse(score);
    if (scoreValue == scoreValue.toInt()) {
      return scoreValue.toInt().toString();
    } else {
      return scoreValue.toStringAsFixed(2);
    }
  }
}
