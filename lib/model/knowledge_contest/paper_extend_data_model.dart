class PaperExtendDataModel {
  final int? max_wrong;
  final int? max_retake;
  final int? remaining_retake;
  final int? limit_time;
  final int? max_score;
  final int? pass_score;

  PaperExtendDataModel(
    this.max_wrong,
    this.max_retake,
    this.remaining_retake,
    this.limit_time,
    this.max_score,
    this.pass_score,
  );

  factory PaperExtendDataModel.fromJson(Map json) {
    return PaperExtendDataModel(
      json['max_wrong'],
      json['max_retake'],
      json['remaining_retake'],
      json['limit_time'],
      json['max_score'],
      json['pass_score'],
    );
  }
}
