class PaperContentModel {
  final int question_total;
  final int max_wrong;
  final int answer_record_id;
  final String paper_name;
  final int max_retake;
  final String score_total;
  final String score_pass;
  final int limit_time;
  final List<PaperContentItemModel> list;

  PaperContentModel(
    this.question_total,
    this.max_wrong,
    this.answer_record_id,
    this.paper_name,
    this.max_retake,
    this.score_total,
    this.score_pass,
    this.limit_time,
    this.list,
  ); //问题列表

  factory PaperContentModel.fromJson(Map<String, dynamic> json) {
    List<PaperContentItemModel> contents = [];
    for (var element in json['list']) {
      if (element['item_tag'] != 'MODULE') {
        contents.add(PaperContentItemModel.fromJson(element));
      }
    }
    return PaperContentModel(
      json['question_total'],
      json['max_wrong'],
      json['answer_record_id'],
      json['paper_name'],
      json['max_retake'],
      json['score_total'],
      json['score_pass'],
      json['limit_time'],
      contents,
    );
  }
}

enum QuestionCategory {
  UNKNOWN,
  SINGLE,
  MULTIPLE,
  TF,
}

enum AnswerStatus {
  NOT,
  PART,
  WRONG,
  RIGHT,
}

class PaperContentItemModel {
  final int paper_id;
  final String? item_tag;
  final String? module_name;
  final int paper_content_id;
  final int question_id;
  final int question_number;
  final int question_limit_time;
  final String question_score;
  final QuestionCategory
      question_category; // 试题类别，UNKNOWN:未知，SINGLE:单选，MULTIPLE:多选, TF:判断
  final String content;
  final dynamic options;
  final dynamic answer;
  final dynamic key_point;
  final String analysis;
  AnswerStatus answer_status; // 答题状态，NOT-未答，PART-少选，WRONG-错选，RIGHT-正确
  final List selected;

  PaperContentItemModel(
    this.paper_id,
    this.item_tag,
    this.module_name,
    this.paper_content_id,
    this.question_id,
    this.question_number,
    this.question_limit_time,
    this.question_score,
    this.question_category,
    this.content,
    this.options,
    this.answer,
    this.key_point,
    this.analysis,
    this.answer_status,
    this.selected,
  );

  factory PaperContentItemModel.fromJson(Map<String, dynamic> json) {
    QuestionCategory question_category = QuestionCategory.UNKNOWN;
    if (json['question_category'] == 'SINGLE') {
      question_category = QuestionCategory.SINGLE;
    } else if (json['question_category'] == 'MULTIPLE') {
      question_category = QuestionCategory.MULTIPLE;
    } else if (json['question_category'] == 'TF') {
      question_category = QuestionCategory.TF;
    }
    AnswerStatus answer_status = AnswerStatus.NOT;
    if (json['answer_status'] == 'PART') {
      answer_status = AnswerStatus.PART;
    } else if (json['answer_status'] == 'WRONG') {
      answer_status = AnswerStatus.WRONG;
    } else if (json['answer_status'] == 'RIGHT') {
      answer_status = AnswerStatus.RIGHT;
    }
    return PaperContentItemModel(
      json['paper_id'],
      json['item_tag'],
      json['module_name'],
      json['paper_content_id'],
      json['question_id'],
      json['question_number'],
      json['question_limit_time'],
      json['question_score'],
      question_category,
      json['content'],
      json['options'],
      json['answer'],
      json['key_point'],
      json['analysis'],
      answer_status,
      json['selected'],
    );
  }
}
