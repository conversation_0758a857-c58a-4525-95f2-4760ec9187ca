class CourseQueryModel {
  final List<ItemModel>? items;
  final int? courses;
  final int? minutes;
  final String? registerAt;
  final String? teacher;

  CourseQueryModel(
      this.items, this.courses, this.minutes, this.registerAt, this.teacher);

  factory CourseQueryModel.formJson(Map json) {
    List? itemMaps = json['items'];
    List<ItemModel>? itemModels =
        itemMaps?.map((e) => ItemModel.formJson(e)).toList();
    return CourseQueryModel(
      itemModels,
      json['courses'],
      json['minutes'],
      json['registerAt'],
      json['teacher'],
    );
  }
}

class ItemModel {
  final int? courseId;
  final String? courseName;
  final int? liveMinutes;
  final int? playMinutes;
  final int? totalMinutes;

  ItemModel(this.courseId, this.courseName, this.liveMinutes, this.playMinutes,
      this.totalMinutes);

  factory ItemModel.formJson(Map json) {
    return ItemModel(
      json['courseId'],
      json['courseName'],
      json['liveMinutes'],
      json['playMinutes'],
      json['totalMinutes'],
    );
  }
}
