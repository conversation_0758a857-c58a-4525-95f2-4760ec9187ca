import 'package:npemployee/model/mine/team_manager_department_model.dart';

class ContactModel {
  final List<ContactModel>? children;
  final DepartmentModel department;
  final int user_count;
  final List? users;

  ContactModel(this.children, this.department, this.user_count, this.users);

  factory ContactModel.fromJson(Map json) {
    List childrenMaps = json['children'] ?? [];
    List<ContactModel> childrens =
        childrenMaps.map((e) => ContactModel.fromJson(e)).toList();
    return ContactModel(
      childrens,
      DepartmentModel.fromJson(json['department']),
      json['user_count'],
      json['users'],
    );
  }

  List<ContactUser> get userList {
    List<ContactUser> u = [];
    if (users != null && users!.isNotEmpty) {
      for (Map element in users ?? []) {
        if (element.containsKey('mobile')) {
          u.add(ContactUser.fromJson(element));
        }
      }
    }
    return u;
  }

  List<ContactModel> get departments {
    List<ContactModel> d = [];
    if (children != null && children!.isNotEmpty) {
      for (var e in children!) {
        if (e.user_count > 0) {
          d.add(e);
        }
      }
    }
    return d;
  }

//校区
  List<String> schools(int userId) {
    List<String> s = [];
    if (children != null && children!.isNotEmpty) {
      for (var e in children!) {
        if (e.user_count > 0) {
          for (var user in e.userList) {
            if (user.id == userId) {
              s.add(e.department.name);
            }
          }
        }
      }
    }
    return s;
  }
}

class ContactUser {
  final String avatar;
  final int id;
  final String mobile;
  final String name;
  final String guid;
  final List? department_roles;

  ContactUser(this.avatar, this.id, this.mobile, this.name, this.guid,
      this.department_roles);

  factory ContactUser.fromJson(Map json) {
    return ContactUser(
      json['avatar'],
      json['id'],
      json['mobile'],
      json['name'],
      json['guid'],
      json['department_roles'],
    );
  }
  List<String> get roles {
    List<String> r = [];
    for (var e in department_roles ?? []) {
      for (var role in e['roles'] ?? []) {
        var subRole = role['role'];
        if (subRole != null) {
          r.add(role['role']['name']);
        }
      }
    }
    return r;
  }
}
