class SystemNotificationModel {
  final String? type;
  final String? title;
  final List? img;
  final String? date;
  final List? jump;
  final List? style;

  SystemNotificationModel(
    this.type,
    this.title,
    this.img,
    this.date,
    this.jump,
    this.style,
  );

  factory SystemNotificationModel.fromJson(Map json) {
    return SystemNotificationModel(
      json['type'],
      json['title'],
      json['img'],
      json['date'],
      json['jump'],
      json['style'],
    );
  }
}
