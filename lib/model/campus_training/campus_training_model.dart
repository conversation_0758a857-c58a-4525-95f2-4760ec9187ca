class CampusTrainingTagModel {
  final int id;
  final String created_at;
  final String updated_at;
  final String name;
  final int sort;
  final int parent_id;
  final int level;
  final List<EdgesModel> edges;

  CampusTrainingTagModel(this.id, this.created_at, this.updated_at, this.name,
      this.sort, this.parent_id, this.level, this.edges);

  factory CampusTrainingTagModel.formJson(Map json) {
    List edgesChildren = json['edges']['children'];
    List<EdgesModel> children =
        edgesChildren.map((e) => EdgesModel.formJson(e)).toList();

    return CampusTrainingTagModel(
      json['id'],
      json['created_at'],
      json['updated_at'],
      json['name'],
      json['sort'],
      json['parent_id'],
      json['level'],
      children,
    );
  }

  Map toJson() {
    List children = edges.map((e) => e.toJson()).toList();
    return {
      'id': id,
      'created_at': created_at,
      'updated_at': updated_at,
      'name': name,
      'sort': sort,
      'parent_id': parent_id,
      'level': level,
      'edges': children,
    };
  }
}

class EdgesModel {
  final int? id;
  final String created_at;
  final String updated_at;
  final String name;
  final int sort;
  final int parent_id;
  final int level;

  EdgesModel(this.id, this.created_at, this.updated_at, this.name, this.sort,
      this.parent_id, this.level);

  factory EdgesModel.formJson(Map json) {
    return EdgesModel(
      json['id'],
      json['created_at'],
      json['updated_at'],
      json['name'],
      json['sort'],
      json['parent_id'],
      json['level'],
    );
  }

  Map toJson() {
    return {
      'id': id,
      'created_at': created_at,
      'updated_at': updated_at,
      'name': name,
      'sort': sort,
      'parent_id': parent_id,
      'level': level,
    };
  }
}

class CampusTrainingCourseModel {
  final int course_type;
  final int id;
  final String image;
  final String? introduction;
  final int lesson_count;
  final String name;
  final String? phrase;
  final String status;
  final String tag_ids;
  final int watch_count;
  final String created_at;
  final String? end_time;
  final String? start_time;
  final List<CampusTrainingSortModel> tag;

  CampusTrainingCourseModel(
      this.course_type,
      this.id,
      this.image,
      this.introduction,
      this.lesson_count,
      this.name,
      this.phrase,
      this.status,
      this.tag_ids,
      this.watch_count,
      this.created_at,
      this.end_time,
      this.start_time,
      this.tag);

  factory CampusTrainingCourseModel.formJson(Map json) {
    List tag = json['tag'];
    List<CampusTrainingSortModel> tagList =
        tag.map((e) => CampusTrainingSortModel.formJson(e['sort'])).toList();
    return CampusTrainingCourseModel(
      json['course_type'],
      json['id'],
      json['image'],
      json['introduction'],
      json['lesson_count'],
      json['name'],
      json['phrase'],
      json['status'],
      json['tag_ids'],
      json['watch_count'],
      json['created_at'],
      json['end_time'],
      json['start_time'],
      tagList,
    );
  }
}

class CampusTrainingSortModel {
  final int id;
  final String created_at;
  final String updated_at;
  final int course_id;
  final int tag_id;
  final Map<String, EdgesModel> edges;

  CampusTrainingSortModel(this.id, this.created_at, this.updated_at,
      this.course_id, this.tag_id, this.edges);

  factory CampusTrainingSortModel.formJson(Map json) {
    Map edges = json['edges']['tag'];
    EdgesModel edgesModel = EdgesModel.formJson(edges);
    return CampusTrainingSortModel(
      json['id'],
      json['created_at'],
      json['updated_at'],
      json['course_id'],
      json['tag_id'],
      {'tag': edgesModel},
    );
  }
}
