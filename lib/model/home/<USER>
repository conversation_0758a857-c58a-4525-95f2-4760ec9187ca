class CircleNotifyModel {
  final String? content;
  final String? created_at;
  final bool? hidden;
  final int? id;
  final List? jump;
  final int? sort;
  final List? style;

  CircleNotifyModel(this.content, this.created_at, this.hidden, this.id,
      this.jump, this.sort, this.style);

  factory CircleNotifyModel.fromJson(Map json) {
    return CircleNotifyModel(
      json['content'],
      json['created_at'],
      json['hidden'],
      json['id'],
      json['jump'],
      json['sort'],
      json['style'],
    );
  }
}
