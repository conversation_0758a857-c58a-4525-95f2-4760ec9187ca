class CirclePostModel {
  final int category_id;
  final String content;
  final int id;
  bool liked;
  int likes;
  final List<CircleMediaModel> media;
  final int post_user_id;
  final CirclePosterModel poster;
  final String show_post_time;
  final String show_poster_avatar;
  final String show_poster_name;
  final String status;

  CirclePostModel(
    this.category_id,
    this.content,
    this.id,
    this.liked,
    this.likes,
    this.media,
    this.post_user_id,
    this.poster,
    this.show_post_time,
    this.show_poster_avatar,
    this.show_poster_name,
    this.status,
  );

  factory CirclePostModel.fromJson(Map json) {
    List mediaMaps = json['media'];
    List<CircleMediaModel> mediaModels =
        mediaMaps.map((e) => CircleMediaModel.fromJson(e)).toList();
    return CirclePostModel(
      json['category_id'],
      json['content'],
      json['id'],
      json['liked'],
      json['likes'],
      mediaModels,
      json['post_user_id'],
      CirclePosterModel.from<PERSON><PERSON>(json['poster']),
      json['show_post_time'],
      json['show_poster_avatar'],
      json['show_poster_name'],
      json['status'],
    );
  }
}

class CircleMediaModel {
  final String media_type;
  final String url;

  CircleMediaModel(this.media_type, this.url);

  factory CircleMediaModel.fromJson(Map json) {
    return CircleMediaModel(
      json['media_type'],
      json['url'],
    );
  }
}

class CirclePosterModel {
  final String avatar;
  final int id;
  final String mobile;
  final String name;

  CirclePosterModel(this.avatar, this.id, this.mobile, this.name);

  factory CirclePosterModel.fromJson(Map json) {
    return CirclePosterModel(
      json['avatar'],
      json['id'],
      json['mobile'],
      json['name'],
    );
  }
}
