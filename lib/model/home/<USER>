class CircleBannerModel {
  final int id;
  final String name;
  final List<BannerImageModel> images;
  final List<BannerJumpModel>? jump;
  final List? style;
  final String created_at;

  CircleBannerModel(
      this.id, this.name, this.images, this.jump, this.style, this.created_at);

  factory CircleBannerModel.fromJson(Map json) {
    List imageMaps = json['images'];
    List? jumpMaps = json['jump'];
    List<BannerImageModel> imageModels =
        imageMaps.map((e) => BannerImageModel.fromJson(e)).toList();
    List<BannerJumpModel>? jumpModels =
        jumpMaps?.map((e) => BannerJumpModel.fromJson(e)).toList();
    return CircleBannerModel(
      json['id'],
      json['name'],
      imageModels,
      jumpModels,
      json['style'],
      json['created_at'],
    );
  }
}

class BannerImageModel {
  final String meta;
  final int width;
  final int height;

  BannerImageModel(
      {required this.meta, required this.width, required this.height});

  factory BannerImageModel.fromJson(Map json) {
    return BannerImageModel(
      meta: json['meta'],
      width: json['width'],
      height: json['height'],
    );
  }

  Map toJson() {
    return {
      'meta': meta,
      'width': width,
      'height': height,
    };
  }
}

class BannerJumpModel {
  final String key;
  final String value;

  BannerJumpModel({required this.key, required this.value});

  factory BannerJumpModel.fromJson(Map json) {
    return BannerJumpModel(
      key: json['key'],
      value: json['value'],
    );
  }
  Map toJson() {
    return {
      'key': key,
      'value': value,
    };
  }
}
