import 'package:flutter_downloader/flutter_downloader.dart';

class TaskInfo {
  TaskInfo(
      {this.name,
      this.link,
      this.size,
      this.taskId,
      this.progress,
      this.status,
      this.pptTag});

  final String? name;
  final String? link;
  final double? size;
  bool isSelected = false; //ppt我的下载列表用
  String? pptTag;

  String? taskId;
  int? progress = 0;
  DownloadTaskStatus? status = DownloadTaskStatus.undefined;

  factory TaskInfo.formJson(Map json) {
    return TaskInfo(
      name: json['name'],
      link: json['link'],
      size: json['size'],
      taskId: json['taskId'],
      progress: json['progress'],
      status: DownloadTaskStatus.fromInt(json['status']),
      pptTag: json['pptTag'],
    );
  }

  Map toJson() {
    int state = 0;
    switch (status) {
      case DownloadTaskStatus.undefined:
        state = 0;
        break;
      case DownloadTaskStatus.enqueued:
        state = 1;
        break;
      case DownloadTaskStatus.running:
        state = 2;
        break;
      case DownloadTaskStatus.complete:
        state = 3;
        break;
      case DownloadTaskStatus.failed:
        state = 4;
        break;
      case DownloadTaskStatus.canceled:
        state = 5;
        break;
      case DownloadTaskStatus.paused:
        state = 6;
        break;
      default:
    }

    return {
      'name': name,
      'link': link,
      'size': size,
      'taskId': taskId,
      'progress': progress,
      'status': state,
      'pptTag': pptTag,
    };
  }
}
