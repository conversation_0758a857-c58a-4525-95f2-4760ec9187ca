import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:npemployee/model/study/chapter_model.dart';
import 'package:npemployee/model/study/course_list_model.dart';

class DownloadLocalModel {
  bool isSelect;
  final CourseListModel course;
  List<ChapterModel> chapters;

  DownloadLocalModel(this.course, this.chapters, {this.isSelect = false});

  factory DownloadLocalModel.fromJson(Map json) {
    return DownloadLocalModel(
      CourseListModel.formJson(json['course']),
      (json['chapters'] as List).map((e) => ChapterModel.formJson(e)).toList(),
    );
  }

  Map toJson() {
    return {
      'course': course.toJson(),
      'chapters': chapters.map((e) => e.toJson()).toList(),
    };
  }

  int videoNum(List<DownloadTask> tasks) {
    return mp4Num(tasks);
  }

  String videoSize(List<DownloadTask> tasks) {
    return size('mp4');
  }

  int audioNum(List<DownloadTask> tasks) {
    return mp3Num(tasks);
  }

  String audioSize(List<DownloadTask> tasks) {
    return size('mp3');
  }

  int mp4Num(List<DownloadTask> tasks) {
    int num = 0;
    for (var task in tasks) {
      if (task.status == DownloadTaskStatus.complete) {
        for (var chapter in chapters) {
          for (var lesson in chapter.lesson) {
            for (var media in lesson.media) {
              if (media.media_type == 'mp4' && task.url == media.media_url) {
                num++;
              }
            }
          }
        }
      }
    }
    return num;
  }

  int mp3Num(List<DownloadTask> tasks) {
    int num = 0;
    for (var chapter in chapters) {
      for (var lesson in chapter.lesson) {
        for (var media in lesson.media) {
          if (media.media_type == 'mp3') {
            for (var task in tasks) {
              if (task.url == media.media_url &&
                  task.status == DownloadTaskStatus.complete) {
                num++;
              }
            }
          }
        }
      }
    }
    return num;
  }

  String size(String type) {
    double size = 0;
    for (var chapter in chapters) {
      for (var lesson in chapter.lesson) {
        for (var media in lesson.media) {
          if (media.media_type == type) {
            size += media.getSize;
          }
        }
      }
    }
    // 判断是否有小数
    if (size % 1 == 0) {
      return size.toStringAsFixed(0);
    } else {
      return size.toStringAsFixed(1);
    }
  }
}
