import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';

class ImageUtils {
  static Future<String> cropImage(String img) async {
    final croppedFile = await ImageCropper().cropImage(
      sourcePath: img,
      aspectRatio: const CropAspectRatio(ratioX: 100, ratioY: 100),
      uiSettings: [
        AndroidUiSettings(
            toolbarTitle: '编辑头像',
            toolbarColor: Colors.black,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
            aspectRatioPresets: [CropAspectRatioPreset.square],
            hideBottomControls: true),
        IOSUiSettings(
          title: '编辑头像',
          aspectRatioLockEnabled: true,
          rotateButtonsHidden: true,
          resetButtonHidden: true,
          aspectRatioPickerButtonHidden: true,
        ),
      ],
    );
    return croppedFile?.path ?? '';
  }
}
