import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionManager {
  // 检查相机权限
  static Future<bool> checkCameraPermission() async {
    var status = await Permission.camera.status;
    if (status.isGranted) {
      return true;
    }

    // 如果没有权限，请求权限
    status = await Permission.camera.request();
    return status.isGranted;
  }

  // 检查并请求相机权限
  static Future<bool> requestCameraPermission() async {
    // 检查当前权限状态
    var status = await Permission.camera.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      // 请求权限
      status = await Permission.camera.request();
      return status.isGranted;
    }

    // 如果是永久拒绝，引导用户去设置页面
    if (status.isPermanentlyDenied) {
      openAppSettings();
      return false;
    }

    return false;
  }

  // 检查是否永久拒绝
  static Future<bool> isPermanentlyDenied() async {
    var status = await Permission.camera.status;
    return status.isPermanentlyDenied;
  }
}

class PermissionAlertDialog {
  static void show({
    required BuildContext context,
    required String title,
    required String content,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text(
            title,
            style: const TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            content,
            style: const TextStyle(
              fontSize: 13,
              color: CupertinoColors.black,
            ),
          ),
          actions: [
            if (cancelText != null)
              CupertinoDialogAction(

                child: Text(
                  cancelText,
                  style: const TextStyle(
                    color: const Color(0xFF999999)
                  ),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  onCancel?.call();
                },
              ),
            CupertinoDialogAction(
              isDefaultAction: true, // 使用粗体样式
              child: Text(
                confirmText ?? '确定',
                style: const TextStyle(
                  color: const Color(0xFF0054FF)
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                onConfirm?.call();
              },
            ),
          ],
        );
      },
    );
  }
}