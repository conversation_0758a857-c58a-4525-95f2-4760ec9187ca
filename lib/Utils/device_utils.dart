
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';

///  Name: 设备工具类
///  基于 [device_info_plus](https://pub.dev/packages/device_info_plus)
///  目前仅支持 iOS 和 Android，其他平台返回空字符串
///  Created by Fitem on 2023/6/2
class DeviceUtils {
  static final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  static Map<String, dynamic>? _deviceData;
  static String? _deviceId;





  /// 获取设备型号
  static Future<String> getModel() async {
    Map<String, dynamic> deviceData = await getDeviceData();
    return deviceData['model'] ?? '';
  }

  /// 获取操作系统版本号
  static Future<String> getSystemVersion() async {
    Map<String, dynamic> deviceData = await getDeviceData();
    if (kIsWeb) {
      return '';
    } else if (Platform.isAndroid) {
      return '${deviceData['version.sdkInt']}';
    } else if (Platform.isIOS) {
      return '${deviceData['systemVersion']}';
    }
    return '';
  }

  /// 获取设备系统名称
  static Future<String> getSystemName() async {
    Map<String, dynamic> deviceData = await getDeviceData();
    if (kIsWeb) {
      return deviceData['browserName'];
    } else if (Platform.isAndroid) {
      return deviceData['display'];
    } else if (Platform.isIOS) {
      return deviceData['systemName'];
    }
    return '';
  }

  /// 设备品牌
  static Future<String> getBrand() async {
    Map<String, dynamic> deviceData = await getDeviceData();
    if (kIsWeb) {
      return '';
    } else if (Platform.isAndroid) {
      return deviceData['brand'];
    } else if (Platform.isIOS) {
      return deviceData['name'];
    } else {
      return '';
    }
  }



  /// 获取设备信息
  static Future<Map<String, dynamic>> getDeviceData() async {
    if (_deviceData != null) {
      return _deviceData!;
    }
    if (kIsWeb) {
      _deviceData = _readWebBrowserInfo(await deviceInfoPlugin.webBrowserInfo);
    } else {
      switch (defaultTargetPlatform) {
        case TargetPlatform.android:
          _deviceData = _readAndroidDeviceInfo(await deviceInfoPlugin.androidInfo);
          break;
        case TargetPlatform.iOS:
          _deviceData = _readIosDeviceInfo(await deviceInfoPlugin.iosInfo);
          break;
        case TargetPlatform.linux:
          _deviceData = _readLinuxDeviceInfo(await deviceInfoPlugin.linuxInfo);
          break;
        case TargetPlatform.macOS:
          _deviceData = _readMacOsDeviceInfo(await deviceInfoPlugin.macOsInfo);
          break;
        case TargetPlatform.windows:
          _deviceData = _readWindowsDeviceInfo(await deviceInfoPlugin.windowsInfo);
          break;
        case TargetPlatform.fuchsia:
        default:
          _deviceData = <String, dynamic>{'Error:': 'Fuchsia platform isn\'t supported'};
          break;
      }
    }
    return _deviceData!;
  }

  static Map<String, dynamic> _readAndroidDeviceInfo(AndroidDeviceInfo build) {
    return <String, dynamic>{
      'version.securityPatch': build.version.securityPatch,
      'version.sdkInt': build.version.sdkInt,
      'version.release': build.version.release,
      'version.previewSdkInt': build.version.previewSdkInt,
      'version.incremental': build.version.incremental,
      'version.codename': build.version.codename,
      'version.baseOS': build.version.baseOS,
      'board': build.board,
      'bootloader': build.bootloader,
      'brand': build.brand,
      'device': build.device,
      'display': build.display,
      'fingerprint': build.fingerprint,
      'hardware': build.hardware,
      'host': build.host,
      'id': build.id,
      'manufacturer': build.manufacturer,
      'model': build.model,
      'product': build.product,
      'supported32BitAbis': build.supported32BitAbis,
      'supported64BitAbis': build.supported64BitAbis,
      'supportedAbis': build.supportedAbis,
      'tags': build.tags,
      'type': build.type,
      'isPhysicalDevice': build.isPhysicalDevice,
      'systemFeatures': build.systemFeatures,
      'serialNumber': build.serialNumber,
    };
  }

  static Map<String, dynamic> _readIosDeviceInfo(IosDeviceInfo data) {
    return <String, dynamic>{
      'name': data.name,
      'systemName': data.systemName,
      'systemVersion': data.systemVersion,
      'model': data.model,
      'localizedModel': data.localizedModel,
      'identifierForVendor': data.identifierForVendor,
      'isPhysicalDevice': data.isPhysicalDevice,
      'utsname.sysname:': data.utsname.sysname,
      'utsname.nodename:': data.utsname.nodename,
      'utsname.release:': data.utsname.release,
      'utsname.version:': data.utsname.version,
      'utsname.machine:': data.utsname.machine,
    };
  }

  static Map<String, dynamic> _readLinuxDeviceInfo(LinuxDeviceInfo data) {
    return <String, dynamic>{
      'name': data.name,
      'version': data.version,
      'id': data.id,
      'idLike': data.idLike,
      'versionCodename': data.versionCodename,
      'versionId': data.versionId,
      'prettyName': data.prettyName,
      'buildId': data.buildId,
      'variant': data.variant,
      'variantId': data.variantId,
      'machineId': data.machineId,
    };
  }

  static Map<String, dynamic> _readWebBrowserInfo(WebBrowserInfo data) {
    return <String, dynamic>{
      'browserName': describeEnum(data.browserName),
      'appCodeName': data.appCodeName,
      'appName': data.appName,
      'appVersion': data.appVersion,
      'deviceMemory': data.deviceMemory,
      'language': data.language,
      'languages': data.languages,
      'platform': data.platform,
      'product': data.product,
      'productSub': data.productSub,
      'userAgent': data.userAgent,
      'vendor': data.vendor,
      'vendorSub': data.vendorSub,
      'hardwareConcurrency': data.hardwareConcurrency,
      'maxTouchPoints': data.maxTouchPoints,
    };
  }

  static Map<String, dynamic> _readMacOsDeviceInfo(MacOsDeviceInfo data) {
    return <String, dynamic>{
      'computerName': data.computerName,
      'hostName': data.hostName,
      'arch': data.arch,
      'model': data.model,
      'kernelVersion': data.kernelVersion,
      'majorVersion': data.majorVersion,
      'minorVersion': data.minorVersion,
      'patchVersion': data.patchVersion,
      'osRelease': data.osRelease,
      'activeCPUs': data.activeCPUs,
      'memorySize': data.memorySize,
      'cpuFrequency': data.cpuFrequency,
      'systemGUID': data.systemGUID,
    };
  }

  static Map<String, dynamic> _readWindowsDeviceInfo(WindowsDeviceInfo data) {
    return <String, dynamic>{
      'numberOfCores': data.numberOfCores,
      'computerName': data.computerName,
      'systemMemoryInMegabytes': data.systemMemoryInMegabytes,
      'userName': data.userName,
      'majorVersion': data.majorVersion,
      'minorVersion': data.minorVersion,
      'buildNumber': data.buildNumber,
      'platformId': data.platformId,
      'csdVersion': data.csdVersion,
      'servicePackMajor': data.servicePackMajor,
      'servicePackMinor': data.servicePackMinor,
      'suitMask': data.suitMask,
      'productType': data.productType,
      'reserved': data.reserved,
      'buildLab': data.buildLab,
      'buildLabEx': data.buildLabEx,
      'digitalProductId': data.digitalProductId,
      'displayVersion': data.displayVersion,
      'editionId': data.editionId,
      'installDate': data.installDate,
      'productId': data.productId,
      'productName': data.productName,
      'registeredOwner': data.registeredOwner,
      'releaseId': data.releaseId,
      'deviceId': data.deviceId,
    };
  }
}