import 'package:intl/intl.dart';

class DateTimeUtils {
  // 格式化日期为 yyyy-MM-dd
  static String formatDate(DateTime date) {
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(date);
  }

  // 获取当天的开始和结束时间
  static Map<String, String> getToday() {
    DateTime now = DateTime.now();
    DateTime startOfDay = DateTime(now.year, now.month, now.day);
    DateTime endOfDay =
        startOfDay.add(Duration(days: 1)).subtract(Duration(milliseconds: 1));

    return {
      'start': formatDate(startOfDay),
      'end': formatDate(endOfDay),
    };
  }

  // 获取昨天的开始和结束时间
  static Map<String, String> getYestorday() {
    DateTime now = DateTime.now();
    DateTime startOfDay = now.subtract(Duration(days: 1));
    DateTime endOfDay = startOfDay.subtract(Duration(milliseconds: 1));

    return {
      'start': formatDate(
          DateTime(startOfDay.year, startOfDay.month, startOfDay.day)),
      'end': formatDate(endOfDay),
    };
  }

  // 获取当周的开始和结束时间
  static Map<String, String> getCurrentWeek() {
    DateTime now = DateTime.now();
    int weekday = now.weekday; // 1（周一）到 7（周日）
    DateTime startOfWeek = now.subtract(Duration(days: weekday - 1));
    DateTime endOfWeek = startOfWeek.add(Duration(days: 6));

    return {
      'start': formatDate(
          DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day)),
      // 'end': formatDate(DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day, 23, 59, 59)),
      'end': getToday()['end']!,
    };
  }

  // 获取当月的开始和结束时间
  static Map<String, String> getCurrentMonth() {
    DateTime now = DateTime.now();
    DateTime startOfMonth = DateTime(now.year, now.month, 1);
    DateTime endOfMonth =
        DateTime(now.year, now.month + 1, 1).subtract(Duration(days: 1));

    return {
      'start': formatDate(startOfMonth),
      // 'end': formatDate(DateTime(endOfMonth.year, endOfMonth.month, endOfMonth.day, 23, 59, 59)),
      'end': getToday()['end']!,
    };
  }

  /// 年会视频时间格式 2023年8月10日 - 8月15日
  static String formatDataToymdmd(String? startTime, String? endTime) {
    String? startStr;
    String? endStr;
    if (startTime != null) {
      DateTime start = DateTime.parse(startTime).toLocal();
      DateFormat startFormatter = DateFormat('yyyy年MM月dd日');
      startStr = startFormatter.format(start);
    }
    if (endTime != null) {
      DateTime end = DateTime.parse(endTime).toLocal();
      DateFormat endFormatter = DateFormat('MM月dd日');
      endStr = endFormatter.format(end);
    }
    String result =
        '${startStr ?? ''} ${startStr == null || endStr == null ? '' : '-'} ${endStr ?? ''}';
    return result;
  }

  /// 知识竞赛时间格式 06.11 16:00 - 19.12 00:00
  static String formatDataTomdhm(String? startTime, String? endTime) {
    String? startStr;
    String? endStr;
    if (startTime != null) {
      DateTime start = DateTime.parse(startTime).toLocal();
      DateFormat startFormatter = DateFormat('MM.dd HH:mm');
      startStr = startFormatter.format(start);
    }
    if (endTime != null) {
      DateTime end = DateTime.parse(endTime).toLocal();
      DateFormat endFormatter = DateFormat('MM.dd HH:mm');
      endStr = endFormatter.format(end);
    }
    String result =
        '${startStr ?? ''} ${startStr == null || endStr == null ? '' : '-'} ${endStr ?? ''}';
    return result;
  }

  ///校区培训时间格式 2024-09-02
  static String formatDataToYmd(String? startTime) {
    String? startStr;
    if (startTime != null) {
      DateTime start = DateTime.parse(startTime).toLocal();
      DateFormat startFormatter = DateFormat('yyyy-MM-dd');
      startStr = startFormatter.format(start);
    }
    return startStr ?? '';
  }

  static String formatDataToMMd(DateTime time) {
    String? startStr;
    DateFormat startFormatter = DateFormat('MM月d日');
    startStr = startFormatter.format(time);
    return startStr;
  }

  //正在学习列表时间格式
  static String formatStudyingDate(String time) {
    DateTime dateTime = DateTime.parse(time).toLocal();
    String? startStr;
    DateFormat startFormatter = DateFormat('yyyy.MM.dd HH:mm:ss');
    startStr = startFormatter.format(dateTime);
    return startStr;
  }

  //自定义时间格式
  static String formatCustomDate(String time, String format) {
    DateTime dateTime = DateTime.parse(time).toLocal();
    String? startStr;
    DateFormat startFormatter = DateFormat(format);
    startStr = startFormatter.format(dateTime);
    return startStr;
  }

  //秒 -> 时分秒
  static String formatTime(int seconds) {
    if (seconds < 60) {
      return '$seconds 秒';
    } else if (seconds < 3600) {
      int minutes = (seconds / 60).floor();
      int remainingSeconds = seconds % 60;
      return '$minutes 分 $remainingSeconds 秒';
    } else {
      int hours = (seconds / 3600).floor();
      int remainingSeconds = seconds % 3600;
      int minutes = (remainingSeconds / 60).floor();
      int remainingSeconds2 = remainingSeconds % 60;
      return '$hours 小时 $minutes 分 $remainingSeconds2 秒';
    }
  }
}
