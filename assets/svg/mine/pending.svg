<?xml version="1.0" encoding="UTF-8"?>
<svg width="329px" height="130px" viewBox="0 0 329 130" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 60.1 (88133) - https://sketch.com -->
    <title>编组 2</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M20,0 L309,0 C320.045695,-2.02906125e-15 329,8.954305 329,20 L329,110 C329,121.045695 320.045695,130 309,130 L20,130 C8.954305,130 1.3527075e-15,121.045695 0,110 L0,20 C-1.3527075e-15,8.954305 8.954305,2.02906125e-15 20,0 Z" id="path-1"></path>
        <linearGradient x1="77.1057559%" y1="6.40170855%" x2="30.3013078%" y2="96.5071678%" id="linearGradient-3">
            <stop stop-color="#FED78F" offset="0%"></stop>
            <stop stop-color="#FCEDD1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="18.3875104%" y1="5.62017028%" x2="50%" y2="84.6419225%" id="linearGradient-4">
            <stop stop-color="#F1A589" offset="0%"></stop>
            <stop stop-color="#EA7460" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="77.1057559%" y1="6.40170855%" x2="30.3013078%" y2="96.5071678%" id="linearGradient-5">
            <stop stop-color="#FCEDD1" offset="0%"></stop>
            <stop stop-color="#FED78F" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="我的审批" transform="translate(-32.000000, -324.000000)">
            <g id="编组-2" transform="translate(32.000000, 324.000000)">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <path stroke="#E6E6E6" d="M20,0.5 C9.23044738,0.5 0.5,9.23044738 0.5,20 L0.5,110 C0.5,120.769553 9.23044738,129.5 20,129.5 L309,129.5 C319.769553,129.5 328.5,120.769553 328.5,110 L328.5,20 C328.5,9.23044738 319.769553,0.5 309,0.5 L20,0.5 Z"></path>
                <g id="编组-55" mask="url(#mask-2)">
                    <g transform="translate(179.000000, 45.000000)">
                        <g id="编组-56">
                            <rect id="矩形备份-49" fill="url(#linearGradient-3)" opacity="0.600000024" transform="translate(110.209867, 81.351351) rotate(14.000000) translate(-110.209867, -81.351351) " x="67.4531104" y="31.7837838" width="85.5135135" height="99.1351351" rx="15.1351351"></rect>
                            <rect id="矩形" fill="url(#linearGradient-4)" x="51.4594595" y="0" width="85.5135135" height="99.1351351" rx="15.1351351"></rect>
                            <path d="M93.8547533,22.7027027 C99.1756992,22.7027027 103.489388,27.0163914 103.489388,32.3373373 C103.489388,35.1699199 102.339363,37.8597347 100.112886,40.3603604 C99.5752905,40.963806 99.345293,41.7810724 99.4892628,42.5763263 L99.5470706,42.8925175 C99.671593,43.581528 100.194441,44.1302081 100.87665,44.2877878 L105.387411,45.3283283 C108.880011,46.1347316 111.52877,48.9878555 112.073847,52.5306556 L112.637911,56.1961962 C112.832127,57.4590859 112.465117,58.7438139 111.633106,59.7135395 C110.801095,60.6832651 109.587069,61.2412412 108.309333,61.2412412 L79.4001737,61.2412412 C78.1224374,61.2412412 76.9084119,60.6832651 76.0764009,59.7135395 C75.2443898,58.7438139 74.8773792,57.4590859 75.0715951,56.1961962 L75.6356592,52.5306556 C76.180862,48.9876092 78.8300298,46.134419 82.3229715,45.3283283 L86.8337323,44.2877878 C87.5159418,44.1302081 88.0387895,43.581528 88.1633118,42.8925175 L88.2202438,42.5772022 C88.3646833,41.7822216 88.135342,40.9649912 87.5983719,40.3612362 C85.3710195,37.8597347 84.2201186,35.1699199 84.2201187,32.3373373 C84.2201187,27.0163914 88.5338074,22.7027027 93.8547533,22.7027027 L93.8547533,22.7027027 Z M93.8547533,26.2062062 C90.4686231,26.2062062 87.7236222,28.9512071 87.7236222,32.3373373 C87.7236222,34.2555055 88.529428,36.1386386 90.2146132,38.0314064 L90.3740226,38.2179679 C91.5157036,39.608876 91.9887152,41.4311073 91.6676912,43.2017017 L91.6107593,43.517017 C91.2364287,45.5836169 89.6676069,47.2288994 87.6211447,47.701076 L83.1103839,48.7416166 C81.0149081,49.2256078 79.4258026,50.9375393 79.0988724,53.0631882 L78.5348084,56.7287287 C78.4959744,56.9812462 78.5693318,57.2381325 78.7356554,57.4320645 C78.901979,57.6259964 79.1446876,57.7377379 79.4001737,57.7377379 L108.309333,57.7377379 C108.564972,57.7377379 108.807922,57.6263618 108.974442,57.4323969 C109.140962,57.238432 109.214431,56.9813974 109.175574,56.7287287 L108.61151,53.0631882 C108.284454,50.937293 106.69494,49.2252952 104.599123,48.7416166 L100.088362,47.701076 C98.0422402,47.2285932 96.4738226,45.5833735 96.0996232,43.517017 L96.0418154,43.2008258 C95.7059494,41.3457935 96.2421487,39.4393787 97.4957693,38.0314064 C99.1809545,36.1395145 99.9858844,34.2563813 99.9858844,32.3373373 C99.9858844,30.7112602 99.3399278,29.151783 98.1901177,28.0019729 C97.0403076,26.8521628 95.4808304,26.2062062 93.8547533,26.2062062 Z M97.3582568,51.6066066 C97.9841613,51.6065108 98.5625619,51.9403718 98.8755418,52.4824051 C99.1885217,53.0244383 99.1885217,53.6922784 98.8755418,54.2343117 C98.5625619,54.7763449 97.9841613,55.1102059 97.3582568,55.1101101 L90.3512498,55.1101101 C89.7253453,55.1102059 89.1469447,54.7763449 88.8339648,54.2343117 C88.5209849,53.6922784 88.5209849,53.0244383 88.8339648,52.4824051 C89.1469447,51.9403718 89.7253453,51.6065108 90.3512498,51.6066066 L97.3582568,51.6066066 Z" id="形状" fill="#FFFFFF" fill-rule="nonzero"></path>
                            <rect id="矩形备份-48" fill="url(#linearGradient-5)" opacity="0.600000024" transform="translate(54.864865, 73.783784) rotate(-16.000000) translate(-54.864865, -73.783784) " x="12.1081081" y="24.2162162" width="85.5135135" height="99.1351351" rx="15.1351351"></rect>
                        </g>
                        <g id="编组" transform="translate(78.520678, 66.452703)">
                            <rect id="矩形" fill="#FFFFFF" x="0.283783784" y="0.141891892" width="30.2702703" height="3.78378378" rx="1.89189189"></rect>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>