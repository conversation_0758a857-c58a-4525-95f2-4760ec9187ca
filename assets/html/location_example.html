<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>定位功能示例</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            padding: 16px;
            margin: 0;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #0066cc;
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
        }

        .section {
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }

        .section:last-child {
            border-bottom: none;
        }

        h2 {
            color: #333;
            font-size: 18px;
            margin-bottom: 10px;
        }

        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            margin: 5px 5px 5px 0;
            font-size: 14px;
            cursor: pointer;
        }

        button:disabled {
            background-color: #cccccc;
        }

        .device-list {
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 8px;
        }

        .device-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .device-item>div:first-child {
            flex: 1;
        }

        .device-item>div:last-child {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .device-item:last-child {
            border-bottom: none;
        }

        .device-name {
            font-weight: bold;
        }

        .device-id {
            font-size: 12px;
            color: #666;
        }

        .device-rssi {
            font-size: 12px;
            color: #999;
        }

        .connect-btn {
            background-color: #4CAF50;
        }

        .disconnect-btn {
            background-color: #F44336;
        }

        .services-btn {
            background-color: #2196F3;
        }

        .device-status {
            font-size: 12px;
            margin-top: 4px;
        }

        .log-container {
            margin-top: 20px;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 8px;
            max-height: 200px;
            overflow-y: auto;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 4px;
            word-break: break-all;
        }

        .success {
            color: #4CAF50;
        }

        .error {
            color: #F44336;
        }

        .info {
            color: #2196F3;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>定位功能示例</h1>

        <div class="section">
            <h2>定位服务状态</h2>
            <button id="checkLocationService">检查定位服务</button>
            <button id="checkPermission">检查定位权限</button>
            <button id="requestPermission">请求定位权限</button>
            <button id="getLocationAccuracy">获取定位精度</button>
        </div>

        <div class="section">
            <h2>获取位置</h2>
            <button id="getCurrentPosition">获取当前位置</button>
            <button id="getLastKnownPosition">获取最后已知位置</button>
            <button id="startPositionStream">开始位置监听</button>
            <button id="stopPositionStream">停止位置监听</button>
            <div class="device-list" id="positionInfo">
                <div class="device-item">点击按钮获取位置信息</div>
            </div>
        </div>

        <div class="section">
            <h2>距离计算</h2>
            <div style="margin-bottom: 10px;">
                <label>起始位置 - 纬度:</label>
                <input type="number" id="startLat" placeholder="39.9042" step="any"
                    style="width: 100px; margin: 0 5px;">
                <label>经度:</label>
                <input type="number" id="startLng" placeholder="116.4074" step="any"
                    style="width: 100px; margin: 0 5px;">
            </div>
            <div style="margin-bottom: 10px;">
                <label>结束位置 - 纬度:</label>
                <input type="number" id="endLat" placeholder="31.2304" step="any" style="width: 100px; margin: 0 5px;">
                <label>经度:</label>
                <input type="number" id="endLng" placeholder="121.4737" step="any" style="width: 100px; margin: 0 5px;">
            </div>
            <button id="calculateDistance">计算距离</button>
            <div id="distanceResult"></div>
        </div>

        <div class="section">
            <h2>设置</h2>
            <button id="openAppSettings">打开应用设置</button>
            <button id="openLocationSettings">打开定位设置</button>
        </div>

        <div class="section">
            <h2>日志</h2>
            <div class="log-container" id="logContainer"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let positionStream = null;
        let currentPosition = null;
        let lastKnownPosition = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 注册定位回调函数
            window.locationCallback = function (action, result) {
                logMessage(`收到定位回调: ${action}`, 'info');

                try {
                    const resultObj = result;

                    if (resultObj.code === 0) {
                        // 处理不同的回调
                        switch (action) {
                            case 'isLocationServiceEnabled':
                                logMessage(`定位服务状态: ${resultObj.data ? '已启用' : '未启用'}`, resultObj.data ? 'success' : 'error');
                                break;
                            case 'checkPermission':
                                logMessage(`定位权限状态: ${getPermissionText(resultObj.data)}`, 'info');
                                break;
                            case 'requestPermission':
                                logMessage(`权限请求结果: ${getPermissionText(resultObj.data)}`, resultObj.data === 'whileInUse' || resultObj.data === 'always' ? 'success' : 'error');
                                break;
                            case 'getCurrentPosition':
                                currentPosition = resultObj.data;
                                updatePositionInfo('当前位置', resultObj.data);
                                logMessage(`获取当前位置成功`, 'success');
                                break;
                            case 'getLastKnownPosition':
                                lastKnownPosition = resultObj.data;
                                updatePositionInfo('最后已知位置', resultObj.data);
                                logMessage(`获取最后已知位置成功`, 'success');
                                break;
                            case 'getLocationAccuracy':
                                logMessage(`定位精度状态: ${getAccuracyText(resultObj.data)}`, 'info');
                                break;
                            case 'calculateDistance':
                                updateDistanceResult(resultObj.data);
                                logMessage(`距离计算成功: ${resultObj.data.toFixed(2)} 米`, 'success');
                                break;
                            case 'positionUpdate':
                                updatePositionInfo('实时位置', resultObj.data);
                                logMessage(`位置更新`, 'info');
                                break;
                            case 'openAppSettings':
                            case 'openLocationSettings':
                                logMessage(`${action === 'openAppSettings' ? '应用设置' : '定位设置'}打开${resultObj.data ? '成功' : '失败'}`, resultObj.data ? 'success' : 'error');
                                break;
                            default:
                                logMessage(`${action} 成功: ${JSON.stringify(resultObj.data)}`, 'success');
                                break;
                        }
                    } else {
                        logMessage(`${action} 失败: ${resultObj.msg}`, 'error');
                    }
                } catch (e) {
                    logMessage(`解析回调结果失败: ${e}`, 'error');
                }
            };

            // 绑定按钮事件
            document.getElementById('checkLocationService').addEventListener('click', checkLocationService);
            document.getElementById('checkPermission').addEventListener('click', checkPermission);
            document.getElementById('requestPermission').addEventListener('click', requestPermission);
            document.getElementById('getLocationAccuracy').addEventListener('click', getLocationAccuracy);
            document.getElementById('getCurrentPosition').addEventListener('click', getCurrentPosition);
            document.getElementById('getLastKnownPosition').addEventListener('click', getLastKnownPosition);
            document.getElementById('startPositionStream').addEventListener('click', startPositionStream);
            document.getElementById('stopPositionStream').addEventListener('click', stopPositionStream);
            document.getElementById('calculateDistance').addEventListener('click', calculateDistance);
            document.getElementById('openAppSettings').addEventListener('click', openAppSettings);
            document.getElementById('openLocationSettings').addEventListener('click', openLocationSettings);

            // 开始监听位置更新
            sendToNative('positionStreamListen');

            logMessage('页面加载完成', 'info');
        });

        // 定位功能方法
        function checkLocationService() {
            sendToNative('isLocationServiceEnabled');
        }

        function checkPermission() {
            sendToNative('checkPermission');
        }

        function requestPermission() {
            sendToNative('requestPermission');
        }

        function getLocationAccuracy() {
            sendToNative('getLocationAccuracy');
        }

        function getCurrentPosition() {
            sendToNative('getCurrentPosition', {
                accuracy: 'high',
                timeLimit: 30 // 30秒超时
            });
        }

        function getLastKnownPosition() {
            sendToNative('getLastKnownPosition');
        }

        function startPositionStream() {
            sendToNative('startPositionStream', {
                accuracy: 'high',
                distanceFilter: 10, // 10米距离过滤
                timeInterval: 5000 // 5秒时间间隔
            });
            logMessage('开始位置监听...', 'info');
        }

        function stopPositionStream() {
            sendToNative('stopPositionStream');
            logMessage('停止位置监听', 'info');
        }

        function calculateDistance() {
            const startLat = parseFloat(document.getElementById('startLat').value);
            const startLng = parseFloat(document.getElementById('startLng').value);
            const endLat = parseFloat(document.getElementById('endLat').value);
            const endLng = parseFloat(document.getElementById('endLng').value);

            if (isNaN(startLat) || isNaN(startLng) || isNaN(endLat) || isNaN(endLng)) {
                logMessage('请输入有效的坐标值', 'error');
                return;
            }

            sendToNative('calculateDistance', {
                startLatitude: startLat,
                startLongitude: startLng,
                endLatitude: endLat,
                endLongitude: endLng
            });
        }

        function openAppSettings() {
            sendToNative('openAppSettings');
        }

        function openLocationSettings() {
            sendToNative('openLocationSettings');
        }

        // 辅助方法
        function sendToNative(action, data = {}) {
            logMessage(`发送到原生: ${action}`, 'info');
            try {
                window.xtjrChannel.postMessage(JSON.stringify({
                    action: action,
                    data: data
                }));
            } catch (e) {
                logMessage(`发送消息失败: ${e}`, 'error');
            }
        }

        function updatePositionInfo(title, position) {
            const positionInfo = document.getElementById('positionInfo');

            if (!position) {
                positionInfo.innerHTML = '<div class="device-item">没有位置信息</div>';
                return;
            }

            const positionElement = document.createElement('div');
            positionElement.className = 'device-item';
            positionElement.innerHTML = `
                <div>
                    <div class="device-name">${title}</div>
                    <div class="device-id">纬度: ${position.latitude.toFixed(6)}</div>
                    <div class="device-id">经度: ${position.longitude.toFixed(6)}</div>
                    <div class="device-rssi">精度: ${position.accuracy.toFixed(2)} 米</div>
                    <div class="device-rssi">海拔: ${position.altitude ? position.altitude.toFixed(2) + ' 米' : '未知'}</div>
                    <div class="device-rssi">速度: ${position.speed ? (position.speed * 3.6).toFixed(2) + ' km/h' : '未知'}</div>
                    <div class="device-rssi">时间: ${new Date(position.timestamp).toLocaleString()}</div>
                </div>
            `;

            // 清空并添加新的位置信息
            positionInfo.innerHTML = '';
            positionInfo.appendChild(positionElement);
        }

        function updateDistanceResult(distance) {
            const distanceResult = document.getElementById('distanceResult');
            distanceResult.innerHTML = `
                <div style="margin-top: 10px; padding: 10px; background-color: #f0f0f0; border-radius: 4px;">
                    <strong>距离: ${distance.toFixed(2)} 米</strong><br>
                    <span>${(distance / 1000).toFixed(3)} 公里</span>
                </div>
            `;
        }

        function getPermissionText(permission) {
            switch (permission) {
                case 'denied': return '拒绝';
                case 'deniedForever': return '永久拒绝';
                case 'whileInUse': return '使用时允许';
                case 'always': return '始终允许';
                case 'unableToDetermine': return '无法确定';
                default: return permission;
            }
        }

        function getAccuracyText(accuracy) {
            switch (accuracy) {
                case 'precise': return '精确';
                case 'reduced': return '降低';
                default: return accuracy;
            }
        }

        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    </script>
</body>

</html>